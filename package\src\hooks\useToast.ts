/**
 * MOKO SOSTANZA Dental CRM - Toast Hook
 * Hook per la gestione dei toast notifications
 */

import { create } from 'zustand';

interface Toast {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

interface ToastStore {
  toasts: Toast[];
  showToast: (message: string, type: Toast['type'], duration?: number) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

const useToastStore = create<ToastStore>((set, get) => ({
  toasts: [],

  showToast: (message, type, duration = 3000) => {
    const id = Date.now().toString();
    const toast: Toast = { id, message, type, duration };
    
    set(state => ({ toasts: [...state.toasts, toast] }));
    
    // Auto-remove toast after duration
    if (duration > 0) {
      setTimeout(() => {
        get().removeToast(id);
      }, duration);
    }
  },

  removeToast: (id) => {
    set(state => ({ toasts: state.toasts.filter(t => t.id !== id) }));
  },

  clearToasts: () => {
    set({ toasts: [] });
  }
}));

export const useToast = () => {
  const { showToast, removeToast, clearToasts, toasts } = useToastStore();
  
  return {
    showToast,
    removeToast, 
    clearToasts,
    toasts
  };
};

export default useToast;
