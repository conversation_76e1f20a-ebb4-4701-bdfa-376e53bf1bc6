-- 🏥 MOKO SOSTANZA Dental CRM - Add patient columns for UDI and anamnesis signing
-- Migration to add udi and anamnesi_signed columns to patients table

-- up migration
ALTER TABLE public.patients 
ADD COLUMN IF NOT EXISTS udi TEXT;

ALTER TABLE public.patients 
ADD COLUMN IF NOT EXISTS anamnesi_signed B<PERSON><PERSON><PERSON>N DEFAULT false;

-- Create index for UDI search if needed
CREATE INDEX IF NOT EXISTS idx_patients_udi ON public.patients(udi);

-- Update existing rows to have default values
UPDATE public.patients 
SET anamnesi_signed = false 
WHERE anamnesi_signed IS NULL;
