-- Migration: Temporarily disable <PERSON><PERSON> for development
-- This should be reverted in production
-- Created: 2025-07-22 12:18:00

-- Disable R<PERSON> on all tables for development
ALTER TABLE patients DISABLE ROW LEVEL SECURITY;
ALTER TABLE doctors DISABLE ROW LEVEL SECURITY;
ALTER TABLE appointments DISABLE ROW LEVEL SECURITY;
ALTER TABLE invoices DISABLE ROW LEVEL SECURITY;
ALTER TABLE treatments DISABLE ROW LEVEL SECURITY;
ALTER TABLE reminders DISABLE ROW LEVEL SECURITY;
ALTER TABLE patient_files DISABLE ROW LEVEL SECURITY;
ALTER TABLE patient_events DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE clinics DISABLE ROW LEVEL SECURITY;

-- Keep storage policies disabled for now
DROP POLICY IF EXISTS "clinic_isolation" ON storage.objects;
DROP POLICY IF EXISTS "clinic_isolation_write" ON storage.objects;

-- Add a simple policy that allows all authenticated users
CREATE POLICY "dev_allow_all" ON storage.objects
  FOR ALL USING (auth.role() = 'authenticated');
