// Test setup file for Jest
import '@testing-library/jest-dom';

// Mock global objects and APIs that are not available in Jest environment
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

global.matchMedia = jest.fn().mockImplementation((query) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(), // deprecated
  removeListener: jest.fn(), // deprecated
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
}));

// Mock window.confirm and window.alert
global.confirm = jest.fn(() => true);
global.alert = jest.fn();
