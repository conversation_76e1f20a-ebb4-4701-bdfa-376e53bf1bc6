/**
 * 🔧 SOLUZIONE FINALE RLS PATIENTS
 * 
 * <PERSON><PERSON> script applica la soluzione definitiva per il problema RLS sui pazienti:
 * 1. Ricrea completamente le policy RLS per patients
 * 2. Testa la creazione di un nuovo utente
 * 3. Verifica che tutto funzioni correttamente
 * 
 * NOTA: Richiede VITE_SUPABASE_SERVICE_ROLE_KEY nel file .env
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.log('❌ ERRORE: VITE_SUPABASE_SERVICE_ROLE_KEY mancante nel file .env');
  console.log('📋 Aggiungi la Service Role Key dal dashboard Supabase al file .env:');
  console.log('   VITE_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...');
  process.exit(1);
}

console.log('🔧 === SOLUZIONE FINALE RLS PATIENTS ===\n');

async function applicaSoluzione() {
  // Client service per operazioni admin
  const supabaseService = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('1️⃣ Ricreazione policy RLS per patients...\n');

    // Esegui lo script SQL di ricreazione policy
    const sqlScript = `
      -- Disabilita RLS temporaneamente
      ALTER TABLE public.patients DISABLE ROW LEVEL SECURITY;
      
      -- Elimina tutte le policy esistenti
      DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
      DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
      DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
      DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;
      DROP POLICY IF EXISTS "patients_clinic_isolation_select" ON public.patients;
      DROP POLICY IF EXISTS "patients_clinic_isolation_insert" ON public.patients;
      DROP POLICY IF EXISTS "patients_clinic_isolation_update" ON public.patients;
      DROP POLICY IF EXISTS "patients_clinic_isolation_delete" ON public.patients;
      DROP POLICY IF EXISTS "patients_select" ON public.patients;
      DROP POLICY IF EXISTS "patients_insert" ON public.patients;
      DROP POLICY IF EXISTS "patients_update" ON public.patients;
      DROP POLICY IF EXISTS "patients_delete" ON public.patients;
      DROP POLICY IF EXISTS "select_own_clinic_patients" ON public.patients;
      DROP POLICY IF EXISTS "insert_own_clinic_patients" ON public.patients;
      DROP POLICY IF EXISTS "update_own_clinic_patients" ON public.patients;
      DROP POLICY IF EXISTS "delete_own_clinic_patients" ON public.patients;
      
      -- Riabilita RLS
      ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
      
      -- Crea policy nuove con nomi unici
      CREATE POLICY "patients_rls_select_v2" ON public.patients
        FOR SELECT USING (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );
      
      CREATE POLICY "patients_rls_insert_v2" ON public.patients
        FOR INSERT WITH CHECK (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );
      
      CREATE POLICY "patients_rls_update_v2" ON public.patients
        FOR UPDATE USING (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        ) WITH CHECK (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );
      
      CREATE POLICY "patients_rls_delete_v2" ON public.patients
        FOR DELETE USING (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );
    `;

    // Esegui lo script SQL
    const { error: sqlError } = await supabaseService.rpc('exec_sql', { 
      sql: sqlScript 
    });

    if (sqlError) {
      console.log('❌ Errore esecuzione SQL:', sqlError.message);
      console.log('📋 Esegui manualmente lo script FORCE-RECREATE-PATIENTS-POLICIES.sql nel dashboard Supabase');
      return;
    }

    console.log('✅ Policy RLS ricreate con successo');

    console.log('\n2️⃣ Verifica policy create...\n');

    // Verifica che le policy siano state create
    const { data: policies, error: policiesError } = await supabaseService
      .from('pg_policies')
      .select('policyname, cmd, qual, with_check')
      .eq('schemaname', 'public')
      .eq('tablename', 'patients');

    if (policiesError) {
      console.log('⚠️ Impossibile verificare policy:', policiesError.message);
    } else {
      console.log('📋 Policy RLS attive per patients:');
      console.table(policies);
    }

    console.log('\n3️⃣ Test con nuovo utente...\n');

    // Client per operazioni utente
    const supabaseUser = createClient(supabaseUrl, process.env.VITE_SUPABASE_ANON_KEY);

    // Logout da eventuali sessioni precedenti
    await supabaseUser.auth.signOut();

    // Crea nuovo utente per test
    const timestamp = Date.now();
    const testEmail = `test.final.${timestamp}@demo.com`;

    console.log(`📧 Creazione nuovo utente: ${testEmail}`);

    const { data: signUpData, error: signUpError } = await supabaseUser.auth.signUp({
      email: testEmail,
      password: 'demo123456'
    });

    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }

    console.log('✅ Nuovo utente creato');

    // Chiama Edge Function per iniettare JWT claims
    console.log('\n🔧 Chiamata Edge Function per JWT claims...');

    const { data: functionResult, error: functionError } = await supabaseUser.functions.invoke('issue-jwt', {
      body: { user: signUpData.user }
    });

    if (functionError) {
      console.log('❌ Errore Edge Function:', functionError.message);
      return;
    }

    console.log('✅ Edge Function eseguita:', functionResult);

    // Verifica JWT claims
    const { data: { user: updatedUser }, error: userError } = await supabaseUser.auth.getUser();

    if (userError || !updatedUser) {
      console.log('❌ Errore ricarica utente:', userError?.message);
      return;
    }

    const clinicId = updatedUser.app_metadata?.clinic_id;
    const appRole = updatedUser.app_metadata?.app_role;

    console.log('\n📋 JWT claims aggiornati:');
    console.log(`  - Clinic ID: ${clinicId}`);
    console.log(`  - App Role: ${appRole}`);

    if (!clinicId) {
      console.log('❌ JWT claims mancanti - problema nell\'Edge Function');
      return;
    }

    console.log('\n4️⃣ Test creazione paziente...\n');

    // Test creazione paziente
    const { data: newPatient, error: createError } = await supabaseUser
      .from('patients')
      .insert({
        first_name: 'Test',
        last_name: 'Finale',
        clinic_id: clinicId
      })
      .select()
      .single();

    if (createError) {
      console.log('❌ ERRORE CREAZIONE PAZIENTE:', createError.message);
      console.log('📋 Dettagli errore:', createError);
      
      // Analisi errore
      if (createError.code === '42501') {
        console.log('\n🔍 ANALISI: Errore di permessi RLS');
        console.log('   - Le policy RLS stanno bloccando l\'inserimento');
        console.log('   - Verifica che i JWT claims siano corretti');
        console.log('   - Controlla il path JWT nelle policy');
      }
      
      return;
    }

    console.log('✅ PAZIENTE CREATO CON SUCCESSO!');
    console.log('📋 Dati paziente:', newPatient);

    // Cleanup - rimuovi il paziente di test
    await supabaseService
      .from('patients')
      .delete()
      .eq('id', newPatient.id);

    console.log('🧹 Paziente di test rimosso');

    console.log('\n🎉 === SOLUZIONE APPLICATA CON SUCCESSO ===');
    console.log('✅ Policy RLS ricreate');
    console.log('✅ JWT claims funzionanti');
    console.log('✅ Creazione pazienti funzionante');
    console.log('\n📋 Il problema RLS è stato risolto definitivamente!');

  } catch (error) {
    console.error('💥 Errore durante l\'applicazione della soluzione:', error);
    console.log('\n📋 FALLBACK: Esegui manualmente lo script SQL nel dashboard Supabase:');
    console.log('   1. Apri Supabase Dashboard > SQL Editor');
    console.log('   2. Copia e incolla il contenuto di FORCE-RECREATE-PATIENTS-POLICIES.sql');
    console.log('   3. Esegui lo script completo');
  }
}

// Esegui la soluzione
applicaSoluzione();
