/**
 * MOKO SOSTANZA Dental CRM - Development Data Cleanup Script
 * 
 * Script per la pulizia dei dati di test dal database.
 * Rimuove fatture con invoice_number like 'TEST-%' e purga bucket test-invoices.
 * 
 * Usage:
 * npm run dev:purge
 * npx tsx scripts/cleanup_dev.ts
 */

import { createClient } from '@supabase/supabase-js'
import type { Database } from '../src/types/database'

// Configurazione Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY è richiesta per questo script')
  console.error('   Assicurati di impostare la variabile d\'ambiente con la service role key')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey)

interface CleanupStats {
  testInvoices: number
  testInvoiceItems: number
  testInvoiceFiles: number
  orphanFiles: number
}

/**
 * Verifica se siamo in ambiente di sviluppo
 */
function isDevEnvironment(): boolean {
  const env = process.env.NODE_ENV || 'development'
  const url = process.env.VITE_SUPABASE_URL || ''
  
  return env === 'development' || url.includes('localhost') || url.includes('127.0.0.1')
}

/**
 * Conferma dall'utente prima di procedere
 */
async function confirmCleanup(): Promise<boolean> {
  if (process.argv.includes('--force')) {
    return true
  }

  console.log('🧹 PULIZIA DATI TEST')
  console.log('Questo script eliminerà:')
  console.log('  - Fatture con invoice_number che inizia con "TEST-"')
  console.log('  - Items correlati alle fatture TEST-*')
  console.log('  - File nel bucket "test-invoices"')
  console.log('  - File orphan nel bucket "patient-files" (senza record DB corrispondente)')
  console.log('')
  console.log('Per continuare, riavvia lo script con il flag --force:')
  console.log('npm run dev:purge -- --force')
  console.log('')
  
  return false
}

/**
 * Elimina le fatture di test e i relativi items
 */
async function cleanupTestInvoices(): Promise<{ invoices: number; items: number }> {
  try {
    console.log('🧹 Ricerca fatture TEST-*...')
    
    // Prima trova tutte le fatture TEST-*
    const { data: testInvoices, error: findError } = await supabase
      .from('invoices')
      .select('id, invoice_number')
      .like('invoice_number', 'TEST-%')
    
    if (findError) {
      console.error('❌ Errore nel cercare fatture TEST-*:', findError)
      return { invoices: 0, items: 0 }
    }

    if (!testInvoices || testInvoices.length === 0) {
      console.log('✅ Nessuna fattura TEST-* trovata')
      return { invoices: 0, items: 0 }
    }

    console.log(`📋 Trovate ${testInvoices.length} fatture TEST-*`)
    const invoiceIds = testInvoices.map(inv => inv.id)

    // Elimina prima gli items delle fatture TEST-*
    console.log('🗑️  Eliminazione items fatture TEST-*...')
    const { data: deletedItems, error: itemsError } = await supabase
      .from('invoice_items')
      .delete()
      .in('invoice_id', invoiceIds)
      .select('id')
    
    if (itemsError) {
      console.error('❌ Errore eliminazione items:', itemsError)
    }

    // Poi elimina le fatture TEST-*
    console.log('🗑️  Eliminazione fatture TEST-*...')
    const { data: deletedInvoices, error: invoicesError } = await supabase
      .from('invoices')
      .delete()
      .like('invoice_number', 'TEST-%')
      .select('id')
    
    if (invoicesError) {
      console.error('❌ Errore eliminazione fatture:', invoicesError)
    }

    const itemsCount = deletedItems?.length || 0
    const invoicesCount = deletedInvoices?.length || 0

    console.log(`✅ Eliminate ${invoicesCount} fatture TEST-*`)
    console.log(`✅ Eliminati ${itemsCount} items correlati`)

    return { 
      invoices: invoicesCount, 
      items: itemsCount 
    }
    
  } catch (error) {
    console.error('❌ Errore durante pulizia fatture TEST-*:', error)
    return { invoices: 0, items: 0 }
  }
}

/**
 * Purga il bucket test-invoices
 */
async function purgeTestInvoicesBucket(): Promise<number> {
  try {
    console.log('🗑️  Purga bucket "test-invoices"...')
    
    // Lista tutti i file nel bucket test-invoices
    const { data: files, error: listError } = await supabase.storage
      .from('test-invoices')
      .list()
    
    if (listError) {
      if (listError.message.includes('Bucket not found')) {
        console.log('✅ Bucket "test-invoices" non esiste')
        return 0
      }
      console.error('❌ Errore nel listare file bucket:', listError)
      return 0
    }

    if (!files || files.length === 0) {
      console.log('✅ Bucket "test-invoices" già vuoto')
      return 0
    }

    // Elimina tutti i file
    const filePaths = files.map(file => file.name)
    const { data, error: deleteError } = await supabase.storage
      .from('test-invoices')
      .remove(filePaths)
    
    if (deleteError) {
      console.error('❌ Errore eliminazione file bucket:', deleteError)
      return 0
    }

    console.log(`✅ Eliminati ${filePaths.length} file dal bucket "test-invoices"`)
    return filePaths.length
    
  } catch (error) {
    console.error('❌ Errore durante purga bucket:', error)
    return 0
  }
}

/**
 * Rimuove orphan storage objects (file nel bucket senza record corrispondente nel DB)
 */
async function cleanupOrphanStorageObjects(): Promise<number> {
  try {
    console.log('🗑️  Ricerca orphan storage objects nel bucket "patient-files"...')
    
    // Lista tutti i file nel bucket patient-files
    const { data: allFiles, error: listError } = await supabase.storage
      .from('patient-files')
      .list('', {
        limit: 1000,
        offset: 0
      })
    
    if (listError) {
      if (listError.message.includes('Bucket not found')) {
        console.log('✅ Bucket "patient-files" non esiste')
        return 0
      }
      console.error('❌ Errore nel listare file bucket:', listError)
      return 0
    }

    if (!allFiles || allFiles.length === 0) {
      console.log('✅ Bucket "patient-files" è vuoto')
      return 0
    }

    console.log(`📋 Trovati ${allFiles.length} file nel bucket "patient-files"`)

    // Trova tutti i path esistenti nel database
    const { data: dbFiles, error: dbError } = await supabase
      .from('patient_files')
      .select('path')
    
    if (dbError) {
      console.error('❌ Errore nel recuperare file dal database:', dbError)
      return 0
    }

    const dbPaths = new Set(dbFiles?.map(f => f.path) || [])
    console.log(`📋 Trovati ${dbPaths.size} path nel database`)

    // Trova i file orphan (nel bucket ma non nel DB)
    const orphanFiles = allFiles.filter(file => {
      // Il nome del file nel bucket dovrebbe corrispondere al path nel DB
      return !dbPaths.has(file.name)
    })

    if (orphanFiles.length === 0) {
      console.log('✅ Nessun orphan storage object trovato')
      return 0
    }

    console.log(`🗑️  Trovati ${orphanFiles.length} orphan storage objects`)
    
    // Elimina i file orphan
    const orphanPaths = orphanFiles.map(file => file.name)
    const { data, error: deleteError } = await supabase.storage
      .from('patient-files')
      .remove(orphanPaths)
    
    if (deleteError) {
      console.error('❌ Errore eliminazione orphan files:', deleteError)
      return 0
    }

    console.log(`✅ Eliminati ${orphanFiles.length} orphan storage objects`)
    return orphanFiles.length
    
  } catch (error) {
    console.error('❌ Errore durante cleanup orphan storage objects:', error)
    return 0
  }
}

/**
 * Pulizia completa dei dati di test
 */
async function cleanupTestData(): Promise<CleanupStats> {
  const stats: CleanupStats = {
    testInvoices: 0,
    testInvoiceItems: 0,
    testInvoiceFiles: 0,
    orphanFiles: 0
  }

  console.log('🚀 Inizio pulizia dati TEST-*...')
  console.log('')

  // 1. Pulizia fatture TEST-* e relativi items
  const { invoices, items } = await cleanupTestInvoices()
  stats.testInvoices = invoices
  stats.testInvoiceItems = items

  // 2. Purga bucket test-invoices
  stats.testInvoiceFiles = await purgeTestInvoicesBucket()

  // 3. Pulizia orphan storage objects
  stats.orphanFiles = await cleanupOrphanStorageObjects()

  return stats
}

/**
 * Mostra le statistiche della pulizia
 */
function showStats(stats: CleanupStats): void {
  console.log('')
  console.log('📊 Statistiche pulizia TEST:')
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
  console.log(`   Fatture TEST-*:       ${stats.testInvoices}`)
  console.log(`   Items fatture TEST-*: ${stats.testInvoiceItems}`)
  console.log(`   File test-invoices:   ${stats.testInvoiceFiles}`)
  console.log(`   Orphan storage files: ${stats.orphanFiles}`)
  console.log('')
  
  const total = Object.values(stats).reduce((sum, count) => sum + count, 0)
  console.log(`📈 Totale record processati: ${total}`)
}

/**
 * Main function
 */
async function main(): Promise<void> {
  try {
    console.log('🛠️  MOKO SOSTANZA - Cleanup Dati TEST')
    console.log('====================================')
    console.log('')

    // Verifica ambiente
    if (!isDevEnvironment()) {
      console.error('❌ Questo script può essere eseguito solo in ambiente di sviluppo!')
      console.error('   NODE_ENV deve essere "development" o VITE_SUPABASE_URL deve puntare a localhost')
      process.exit(1)
    }

    // Conferma dall'utente
    if (!(await confirmCleanup())) {
      console.log('🚫 Operazione annullata')
      process.exit(0)
    }

    // Esegui pulizia dei dati TEST
    const stats = await cleanupTestData()
    
    // Mostra statistiche
    showStats(stats)
    
    console.log('')
    console.log('✅ Pulizia TEST completata con successo!')
    console.log('')
    console.log('💡 Note:')
    console.log('   - Solo le fatture TEST-* sono state eliminate')
    console.log('   - Il bucket "test-invoices" è stato svuotato')
    console.log('   - Gli orphan storage objects sono stati rimossi')
    console.log('   - Altri dati di produzione sono rimasti intatti')

  } catch (error) {
    console.error('❌ Errore durante la pulizia:', error)
    process.exit(1)
  }
}

// Esegui lo script
if (require.main === module) {
  main()
}

export { main as cleanupTestData }
