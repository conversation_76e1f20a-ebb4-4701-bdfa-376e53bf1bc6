-- Migration: Enhance doctors table with complete professional fields
-- Author: <PERSON>
-- Date: 2025-07-20

-- Drop existing doctors table and recreate with enhanced schema
DROP TABLE IF EXISTS public.doctors CASCADE;

-- Create enhanced doctors table
CREATE TABLE IF NOT EXISTS public.doctors (
    id SERIAL PRIMARY KEY,
    
    -- <PERSON><PERSON>ag<PERSON> (obbligatori FNOMCeO)
    title VARCHAR(10) NOT NULL DEFAULT 'DOTT.' CHECK (title IN ('DOTT.', 'DOTT.SSA', 'PROF.')),
    first_name VARCHAR(255) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    fiscal_code VARCHAR(16) NOT NULL UNIQUE CHECK (fiscal_code ~ '^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$'),
    birth_date DATE NOT NULL,
    sex VARCHAR(1) NOT NULL CHECK (sex IN ('M', 'F')),
    birth_city_code VARCHAR(4) NOT NULL, -- Codice Belfiore
    citizenship VARCHAR(100) DEFAULT 'ITALIANA',
    
    -- Indir<PERSON><PERSON> di residenza (obbligatorio)
    residence_street VARCHAR(255) NOT NULL,
    residence_cap VARCHAR(5) NOT NULL CHECK (residence_cap ~ '^[0-9]{5}$'),
    residence_city_code VARCHAR(4) NOT NULL, -- Codice Belfiore
    
    -- Contatti (PEC obbligatoria per legge DL 76/2020)
    pec VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255),
    mobile VARCHAR(20),
    phone VARCHAR(20),
    
    -- Iscrizione all'Ordine (obbligatorio)
    order_province VARCHAR(2) NOT NULL, -- Sigla provincia (es. 'RM', 'MI')
    order_number VARCHAR(20) NOT NULL,
    order_date DATE NOT NULL,
    albo_code VARCHAR(1) NOT NULL DEFAULT 'O' CHECK (albo_code = 'O'), -- O = Odontoiatra
    practice_status BOOLEAN NOT NULL DEFAULT true, -- Stato esercizio S/N
    
    -- Dati operativi CRM
    calendar_color VARCHAR(7) NOT NULL DEFAULT '#1E88E5' CHECK (calendar_color ~ '^#[0-9A-Fa-f]{6}$'),
    revenue_share_percentage DECIMAL(5,2) DEFAULT 0.00 CHECK (revenue_share_percentage >= 0 AND revenue_share_percentage <= 100),
    
    -- Dati facoltativi
    vat_id VARCHAR(11) CHECK (vat_id IS NULL OR vat_id ~ '^[0-9]{11}$'),
    iban VARCHAR(34) CHECK (iban IS NULL OR iban ~ '^IT[0-9]{2}[A-Z0-9]{23}$'),
    avatar_url TEXT,
    notes TEXT,
    
    -- Metadati
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
);

-- Create specialties table for many-to-many relationship
CREATE TABLE IF NOT EXISTS public.doctor_specialties (
    id SERIAL PRIMARY KEY,
    doctor_id INTEGER NOT NULL REFERENCES public.doctors(id) ON DELETE CASCADE,
    specialty_code VARCHAR(10) NOT NULL, -- Codice branca FNOMCeO
    specialty_name VARCHAR(255) NOT NULL,
    specialization_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create availability slots table
CREATE TABLE IF NOT EXISTS public.doctor_availability (
    id SERIAL PRIMARY KEY,
    doctor_id INTEGER NOT NULL REFERENCES public.doctors(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    CONSTRAINT valid_time_range CHECK (start_time < end_time)
);

-- Create indexes for performance
CREATE INDEX idx_doctors_fiscal_code ON public.doctors(fiscal_code);
CREATE INDEX idx_doctors_pec ON public.doctors(pec);
CREATE INDEX idx_doctors_order_province_number ON public.doctors(order_province, order_number);
CREATE INDEX idx_doctors_deleted_at ON public.doctors(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_doctor_specialties_doctor_id ON public.doctor_specialties(doctor_id);
CREATE INDEX idx_doctor_availability_doctor_id ON public.doctor_availability(doctor_id);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_doctors_updated_at 
    BEFORE UPDATE ON public.doctors 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies (Row Level Security)
ALTER TABLE public.doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctor_specialties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctor_availability ENABLE ROW LEVEL SECURITY;

-- Allow all operations for now (customize based on your auth needs)
CREATE POLICY "Enable all operations for doctors" ON public.doctors FOR ALL USING (true);
CREATE POLICY "Enable all operations for doctor_specialties" ON public.doctor_specialties FOR ALL USING (true);
CREATE POLICY "Enable all operations for doctor_availability" ON public.doctor_availability FOR ALL USING (true);

-- Restore the foreign key constraint between appointments and doctors
ALTER TABLE public.appointments 
ADD CONSTRAINT appointments_doctor_id_fkey 
FOREIGN KEY (doctor_id) REFERENCES public.doctors(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_id ON public.appointments(doctor_id);
