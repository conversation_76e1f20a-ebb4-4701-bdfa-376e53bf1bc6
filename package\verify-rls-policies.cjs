/**
 * Verifica lo stato attuale delle policy RLS
 * Controlla se le policy sono state aggiornate correttamente
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 === VERIFICA POLICY RLS ATTUALI ===\n');

async function verifyRlsPolicies() {
  // Usa service role key per accesso completo
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('1️⃣ Verifica policy RLS per patients...');
    
    // Query per verificare le policy attuali
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('policyname, cmd, qual, with_check')
      .eq('schemaname', 'public')
      .eq('tablename', 'patients')
      .like('policyname', 'clinic_isolation_%')
      .order('cmd');
    
    if (policiesError) {
      console.log('❌ Errore verifica policy:', policiesError.message);
      return;
    }
    
    console.log(`📋 Policy trovate: ${policies.length}`);
    
    policies.forEach(policy => {
      console.log(`\n📝 Policy: ${policy.policyname}`);
      console.log(`  - Comando: ${policy.cmd}`);
      console.log(`  - Condizione USING: ${policy.qual || 'null'}`);
      console.log(`  - Condizione WITH CHECK: ${policy.with_check || 'null'}`);
      
      // Verifica se usa il path corretto
      const hasCorrectPath = policy.qual?.includes("auth.jwt() -> 'app_metadata'") || 
                            policy.with_check?.includes("auth.jwt() -> 'app_metadata'");
      const hasOldPath = policy.qual?.includes("auth.jwt() ->> 'clinic_id'") || 
                        policy.with_check?.includes("auth.jwt() ->> 'clinic_id'");
      
      if (hasCorrectPath) {
        console.log(`  ✅ Usa il path corretto per JWT claims`);
      } else if (hasOldPath) {
        console.log(`  ❌ Usa ancora il path vecchio per JWT claims`);
      } else {
        console.log(`  ⚠️ Path JWT claims non riconosciuto`);
      }
    });
    
    console.log('\n2️⃣ Test JWT claims in SQL...');
    
    // Test per verificare se i JWT claims sono accessibili
    const { data: jwtTest, error: jwtError } = await supabase
      .rpc('exec_sql', {
        sql_query: `
          SELECT 
            'Test JWT access' as test,
            auth.jwt() as full_jwt,
            auth.jwt() -> 'app_metadata' as app_metadata,
            auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_from_jwt,
            auth.uid() as user_id;
        `
      });
    
    if (jwtError) {
      console.log('⚠️ Non posso testare JWT claims direttamente:', jwtError.message);
    } else {
      console.log('📋 Test JWT claims:');
      console.log(JSON.stringify(jwtTest, null, 2));
    }
    
    console.log('\n3️⃣ Verifica RLS abilitato...');
    
    const { data: rlsStatus, error: rlsError } = await supabase
      .from('pg_tables')
      .select('tablename, rowsecurity')
      .eq('schemaname', 'public')
      .in('tablename', ['patients', 'doctors', 'appointments', 'treatments']);
    
    if (rlsError) {
      console.log('❌ Errore verifica RLS:', rlsError.message);
    } else {
      console.log('📋 Stato RLS per tabelle:');
      rlsStatus.forEach(table => {
        console.log(`  - ${table.tablename}: ${table.rowsecurity ? '✅ ABILITATO' : '❌ DISABILITATO'}`);
      });
    }
    
    console.log('\n4️⃣ Verifica clinica demo...');
    
    const { data: clinics, error: clinicsError } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', '00000000-0000-0000-0000-000000000000');
    
    if (clinicsError) {
      console.log('❌ Errore verifica clinica:', clinicsError.message);
    } else if (clinics.length === 0) {
      console.log('❌ Clinica demo NON TROVATA');
      console.log('🔧 Creazione clinica demo...');
      
      const { data: newClinic, error: createError } = await supabase
        .from('clinics')
        .insert({
          id: '00000000-0000-0000-0000-000000000000',
          name: 'Demo Clinic',
          vat_number: '12345678901',
          address: 'Via Demo 123',
          city: 'Milano',
          postal_code: '20100',
          province: 'MI',
          phone: '+39 02 1234567',
          email: '<EMAIL>',
          website: 'https://democlinic.it'
        })
        .select()
        .single();
      
      if (createError) {
        console.log('❌ Errore creazione clinica:', createError.message);
      } else {
        console.log('✅ Clinica demo creata con successo');
      }
    } else {
      console.log('✅ Clinica demo trovata:', clinics[0].name);
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

verifyRlsPolicies().then(() => {
  console.log('\n🏁 Verifica policy completata');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
