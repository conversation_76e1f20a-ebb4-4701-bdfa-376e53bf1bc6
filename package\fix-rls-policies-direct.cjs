/**
 * Fix diretto delle policy RLS tramite client Supabase
 * Esegue le query SQL per correggere il path JWT nelle policy
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔧 === FIX POLICY RLS - PATH JWT ===\n');

async function fixRlsPolicies() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('1️⃣ Login come utente amministratore...');
    
    // Login con utente esistente per avere i permessi
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123456'
    });
    
    if (signInError) {
      console.log('❌ Errore login:', signInError.message);
      return;
    }
    
    console.log('✅ Login riuscito:', signInData.user.email);
    
    console.log('\n2️⃣ Verifica policy attuali...');
    
    // Verifica policy esistenti (questa query potrebbe non funzionare con anon key)
    try {
      const { data: currentPolicies, error: policiesError } = await supabase
        .rpc('exec_sql', {
          sql_query: `
            SELECT policyname, cmd, qual as using_condition, with_check as with_check_condition
            FROM pg_policies 
            WHERE schemaname = 'public' 
              AND tablename = 'patients' 
              AND policyname LIKE 'clinic_isolation_%'
            ORDER BY cmd;
          `
        });
      
      if (policiesError) {
        console.log('⚠️ Non posso verificare le policy direttamente:', policiesError.message);
      } else {
        console.log('📋 Policy attuali:', currentPolicies);
      }
    } catch (error) {
      console.log('⚠️ Verifica policy non disponibile con anon key');
    }
    
    console.log('\n3️⃣ Applicazione fix policy RLS...');
    
    // Lista delle query SQL da eseguire
    const fixQueries = [
      // Rimuovi policy esistenti per patients
      'DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;',
      'DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;',
      'DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;',
      'DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;',
      
      // Crea policy corrette per patients
      `CREATE POLICY "clinic_isolation_select" ON public.patients
        FOR SELECT USING (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );`,
      
      `CREATE POLICY "clinic_isolation_insert" ON public.patients
        FOR INSERT WITH CHECK (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );`,
      
      `CREATE POLICY "clinic_isolation_update" ON public.patients
        FOR UPDATE USING (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        ) WITH CHECK (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );`,
      
      `CREATE POLICY "clinic_isolation_delete" ON public.patients
        FOR DELETE USING (
          clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
        );`
    ];
    
    console.log('📝 Esecuzione query di fix...');
    
    for (let i = 0; i < fixQueries.length; i++) {
      const query = fixQueries[i];
      console.log(`\n🔄 Query ${i + 1}/${fixQueries.length}:`);
      console.log(query.substring(0, 60) + '...');
      
      try {
        const { data, error } = await supabase.rpc('exec_sql', {
          sql_query: query
        });
        
        if (error) {
          console.log(`❌ Errore query ${i + 1}:`, error.message);
          
          // Se l'errore è di permessi, proviamo con un approccio diverso
          if (error.message.includes('permission') || error.message.includes('access')) {
            console.log('⚠️ Errore di permessi - le policy RLS devono essere aggiornate tramite Dashboard');
            break;
          }
        } else {
          console.log(`✅ Query ${i + 1} eseguita con successo`);
        }
      } catch (queryError) {
        console.log(`❌ Errore esecuzione query ${i + 1}:`, queryError.message);
      }
    }
    
    console.log('\n4️⃣ Test creazione paziente dopo fix...');
    
    // Chiama Edge Function per assicurarsi che i claims siano presenti
    await supabase.functions.invoke('issue-jwt', {
      body: { user: signInData.user }
    });
    
    // Test inserimento paziente
    const { data: { user } } = await supabase.auth.getUser();
    const clinicId = user?.app_metadata?.clinic_id || '00000000-0000-0000-0000-000000000000';
    
    const testPatient = {
      first_name: 'Test',
      last_name: 'PolicyFix',
      phone: '+39 ************',
      date_of_birth: '1990-01-01',
      address: 'Via Test',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      anamnesis: 'Test policy fix',
      medical_history: 'Test',
      clinic_id: clinicId
    };
    
    const { data: newPatient, error: patientError } = await supabase
      .from('patients')
      .insert(testPatient)
      .select()
      .single();
    
    if (patientError) {
      console.log('❌ INSERIMENTO ANCORA FALLISCE:');
      console.log(`  - Codice: ${patientError.code}`);
      console.log(`  - Messaggio: ${patientError.message}`);
      
      if (patientError.code === '42501') {
        console.log('\n🚨 LE POLICY NON SONO STATE AGGIORNATE CORRETTAMENTE');
        console.log('📋 SOLUZIONE MANUALE NECESSARIA:');
        console.log('1. Vai su Supabase Dashboard > SQL Editor');
        console.log('2. Esegui queste query una per una:');
        console.log('');
        fixQueries.forEach((query, index) => {
          console.log(`-- Query ${index + 1}`);
          console.log(query);
          console.log('');
        });
      }
    } else {
      console.log('🎉 SUCCESSO! Policy corrette:');
      console.log(`  - Paziente creato: ${newPatient.first_name} ${newPatient.last_name}`);
      console.log(`  - ID: ${newPatient.id}`);
      console.log(`  - Clinic ID: ${newPatient.clinic_id}`);
      console.log('\n✅ Le policy RLS ora funzionano correttamente!');
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

fixRlsPolicies().then(() => {
  console.log('\n🏁 Fix policy completato');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
