import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user from auth event
    const { user } = await req.json()
    
    if (!user?.email) {
      throw new Error('No user email provided')
    }

    console.log(`🔐 [issue-jwt] Processing auth for user: ${user.email}`)

    // Check if users table exists, create stub if needed
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('id, clinic_id, role')
      .eq('email', user.email)
      .single()

    let userRow = existingUser

    // Create stub user if not exists
    if (fetchError && fetchError.code === 'PGRST116') { // No rows returned
      console.log(`👤 [issue-jwt] Creating stub user for ${user.email}`)

      // Use fixed demo clinic ID
      const demoClinicId = '00000000-0000-0000-0000-000000000000'

      console.log(`🏥 [issue-jwt] Using demo clinic ID: ${demoClinicId}`)

      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          id: user.id,
          email: user.email,
          clinic_id: demoClinicId,
          role: 'doctor' // default role
        })
        .select('id, clinic_id, role')
        .single()

      if (createError) {
        console.error(`❌ [issue-jwt] Failed to create user:`, createError)
        throw new Error(`Failed to create user: ${createError.message}`)
      }

      console.log(`✅ [issue-jwt] User created successfully:`, newUser)
      userRow = newUser
    } else if (fetchError) {
      throw new Error(`Failed to fetch user: ${fetchError.message}`)
    }

    // Build custom JWT claims
    const customClaims = {
      clinic_id: userRow.clinic_id,
      app_role: userRow.role || 'doctor'
    }

    console.log(`✅ [issue-jwt] Custom claims for ${user.email}:`, customClaims)

    // Update user metadata with custom claims
    const { error: updateError } = await supabase.auth.admin.updateUserById(
      user.id,
      {
        app_metadata: customClaims
      }
    )

    if (updateError) {
      throw new Error(`Failed to update user metadata: ${updateError.message}`)
    }

    // 🔍 BLOCCO B - JWT DUMP COMPLETO per diagnosi
    console.log('[JWT-DUMP]', JSON.stringify(user, null, 2));

    return new Response(
      JSON.stringify({ 
        success: true,
        claims: customClaims 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error(`❌ [issue-jwt] Error:`, error)
    
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Unknown error' 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
