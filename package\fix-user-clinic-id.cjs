/**
 * Script per verificare e aggiornare i clinic_id degli utenti
 * Questo script risolve il problema principale dell'isolamento multi-clinica
 */

const { createClient } = require('@supabase/supabase-js');

// Configurazione Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

// ID delle cliniche disponibili
const DEMO_CLINIC_ID = '00000000-0000-0000-0000-000000000000';
const CLINIC_A_ID = '11111111-1111-1111-1111-111111111111';
const CLINIC_B_ID = '22222222-2222-2222-2222-222222222222';

console.log('🔧 === FIX CLINIC_ID UTENTI ===\n');

async function listAllUsers() {
  console.log('👥 === LISTA UTENTI REGISTRATI ===');
  
  // Nota: Questo richiede privilegi admin, potrebbe non funzionare con anon key
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Prova a ottenere l'utente corrente
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('❌ Errore nell\'ottenere l\'utente:', error.message);
      console.log('💡 Assicurati di essere loggato nell\'applicazione');
      return [];
    }
    
    if (!user) {
      console.log('❌ Nessun utente autenticato');
      console.log('💡 Fai login nell\'applicazione prima di eseguire questo script');
      return [];
    }
    
    console.log('✅ Utente corrente trovato:');
    console.log(`  - ID: ${user.id}`);
    console.log(`  - Email: ${user.email}`);
    console.log(`  - Clinic ID: ${user.user_metadata?.clinic_id || 'NON IMPOSTATO'}`);
    
    return [user];
    
  } catch (error) {
    console.log('❌ Errore nel recuperare utenti:', error.message);
    return [];
  }
}

async function checkUserClinicId(user) {
  console.log(`\n🔍 === VERIFICA CLINIC_ID PER ${user.email} ===`);
  
  const clinicId = user.user_metadata?.clinic_id;
  
  if (!clinicId) {
    console.log('❌ PROBLEMA: Clinic ID non impostato!');
    console.log('💡 Questo è il motivo per cui l\'isolamento non funziona');
    return false;
  }
  
  console.log(`✅ Clinic ID trovato: ${clinicId}`);
  
  // Verifica se il clinic_id è valido
  const validClinicIds = [DEMO_CLINIC_ID, CLINIC_A_ID, CLINIC_B_ID];
  if (!validClinicIds.includes(clinicId)) {
    console.log('⚠️  ATTENZIONE: Clinic ID non riconosciuto');
    console.log(`   Clinic ID validi: ${validClinicIds.join(', ')}`);
  }
  
  return true;
}

async function suggestClinicAssignment() {
  console.log('\n🏥 === ASSEGNAZIONE CLINICHE SUGGERITA ===');
  
  console.log('Per risolvere il problema dell\'isolamento, ogni utente deve avere un clinic_id.');
  console.log('Ecco le opzioni disponibili:\n');
  
  console.log(`1. 🏥 Clinica Demo (ID: ${DEMO_CLINIC_ID})`);
  console.log('   - Clinica di default per sviluppo e test');
  console.log('   - Tutti i dati esistenti appartengono a questa clinica');
  
  console.log(`\n2. 🏥 Clinica Test A (ID: ${CLINIC_A_ID})`);
  console.log('   - Clinica di test per simulare isolamento');
  console.log('   - Inizialmente vuota');
  
  console.log(`\n3. 🏥 Clinica Test B (ID: ${CLINIC_B_ID})`);
  console.log('   - Seconda clinica di test per simulare isolamento');
  console.log('   - Inizialmente vuota');
}

async function createTestScript() {
  console.log('\n📝 === CREAZIONE SCRIPT DI FIX ===');
  
  const fixScript = `
-- Script SQL per aggiornare clinic_id degli utenti
-- Esegui questo script nel dashboard Supabase o tramite SQL Editor

-- Opzione 1: Assegna tutti gli utenti alla clinica demo
UPDATE auth.users 
SET user_metadata = jsonb_set(
  COALESCE(user_metadata, '{}'), 
  '{clinic_id}', 
  '"${DEMO_CLINIC_ID}"'
)
WHERE user_metadata->>'clinic_id' IS NULL;

-- Opzione 2: Assegna utenti specifici a cliniche diverse
-- Sostituisci '<EMAIL>' con l'email dell'utente

-- Assegna utente alla Clinica A
UPDATE auth.users 
SET user_metadata = jsonb_set(
  COALESCE(user_metadata, '{}'), 
  '{clinic_id}', 
  '"${CLINIC_A_ID}"'
)
WHERE email = '<EMAIL>';

-- Assegna utente alla Clinica B  
UPDATE auth.users 
SET user_metadata = jsonb_set(
  COALESCE(user_metadata, '{}'), 
  '{clinic_id}', 
  '"${CLINIC_B_ID}"'
)
WHERE email = '<EMAIL>';

-- Verifica risultati
SELECT 
  id,
  email,
  user_metadata->>'clinic_id' as clinic_id
FROM auth.users
ORDER BY email;
`;

  console.log('📄 Script SQL generato:');
  console.log(fixScript);
  
  // Salva lo script in un file
  const fs = require('fs');
  fs.writeFileSync('fix-user-clinic-id.sql', fixScript);
  console.log('✅ Script salvato in: fix-user-clinic-id.sql');
}

async function createJavaScriptFix() {
  console.log('\n🔧 === ALTERNATIVA: FIX TRAMITE JAVASCRIPT ===');
  
  const jsFixCode = `
// Aggiungi questo codice temporaneo in ServiceUtils.ts per il debug
export async function getClinicId(): Promise<string> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('Utente non autenticato');
    }
    
    let clinicId = user.user_metadata?.clinic_id;
    
    // DEBUG: Log per verificare i metadati utente
    console.log('🔍 Debug getClinicId:');
    console.log('  - User ID:', user.id);
    console.log('  - Email:', user.email);
    console.log('  - User Metadata:', user.user_metadata);
    console.log('  - Clinic ID:', clinicId);
    
    if (!clinicId) {
      console.log('⚠️  FALLBACK: Clinic ID non trovato, uso clinica demo');
      
      // TEMPORANEO: Assegna clinic_id basato sull'email per test
      if (user.email?.includes('clinica-a') || user.email?.includes('test1')) {
        clinicId = '${CLINIC_A_ID}';
        console.log('📍 Assegnato alla Clinica A per test');
      } else if (user.email?.includes('clinica-b') || user.email?.includes('test2')) {
        clinicId = '${CLINIC_B_ID}';
        console.log('📍 Assegnato alla Clinica B per test');
      } else {
        clinicId = '${DEMO_CLINIC_ID}';
        console.log('📍 Assegnato alla Clinica Demo');
      }
    }
    
    return clinicId;
  } catch (error) {
    console.error('❌ Errore in getClinicId:', error);
    // Fallback alla clinica demo
    return '${DEMO_CLINIC_ID}';
  }
}
`;

  console.log('📄 Codice JavaScript temporaneo:');
  console.log(jsFixCode);
  
  console.log('\n💡 ISTRUZIONI:');
  console.log('1. Sostituisci temporaneamente la funzione getClinicId in ServiceUtils.ts');
  console.log('2. Questo aggiungerà logging per debug e fallback intelligenti');
  console.log('3. Testa con utenti diversi per vedere i log');
  console.log('4. Una volta identificato il problema, applica la soluzione definitiva');
}

async function showManualSteps() {
  console.log('\n📋 === PASSI MANUALI PER RISOLVERE ===');
  
  console.log('🎯 SOLUZIONE RAPIDA (Tramite Dashboard Supabase):');
  console.log('1. Vai al Dashboard Supabase');
  console.log('2. Sezione "Authentication" > "Users"');
  console.log('3. Per ogni utente, clicca sui tre puntini > "Edit user"');
  console.log('4. Nella sezione "User Metadata", aggiungi:');
  console.log('   {');
  console.log(`     "clinic_id": "${DEMO_CLINIC_ID}"`);
  console.log('   }');
  console.log('5. Salva le modifiche');
  console.log('6. Ripeti per tutti gli utenti (assegnando clinic_id diversi per test)');
  
  console.log('\n🎯 SOLUZIONE AVANZATA (Tramite SQL):');
  console.log('1. Vai al Dashboard Supabase');
  console.log('2. Sezione "SQL Editor"');
  console.log('3. Esegui lo script SQL generato sopra');
  console.log('4. Verifica i risultati con la query di controllo');
  
  console.log('\n🎯 SOLUZIONE SVILUPPO (Tramite Codice):');
  console.log('1. Usa il codice JavaScript temporaneo sopra');
  console.log('2. Aggiungi logging per debug');
  console.log('3. Testa con utenti diversi');
  console.log('4. Applica la soluzione definitiva');
}

async function runFix() {
  console.log('🚀 Iniziando diagnosi e fix clinic_id utenti...\n');
  
  const users = await listAllUsers();
  
  if (users.length === 0) {
    console.log('\n❌ Impossibile procedere senza utenti autenticati');
    console.log('💡 Fai login nell\'applicazione e riprova');
    return;
  }
  
  let hasProblems = false;
  
  for (const user of users) {
    const isValid = await checkUserClinicId(user);
    if (!isValid) {
      hasProblems = true;
    }
  }
  
  if (hasProblems) {
    console.log('\n❌ PROBLEMI IDENTIFICATI CON CLINIC_ID UTENTI');
    await suggestClinicAssignment();
    await createTestScript();
    await createJavaScriptFix();
    await showManualSteps();
  } else {
    console.log('\n✅ TUTTI GLI UTENTI HANNO CLINIC_ID VALIDI');
    console.log('💡 Il problema potrebbe essere altrove (RLS, migrazioni, ecc.)');
  }
  
  console.log('\n🏁 === FIX COMPLETATO ===');
  console.log('Segui le istruzioni sopra per risolvere i problemi identificati.');
}

// Esegui il fix
runFix().catch(error => {
  console.error('❌ Errore durante il fix:', error);
  process.exit(1);
});
