export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      appointments: {
        Row: {
          clinic_id: string
          created_at: string | null
          date: string
          deleted_at: string | null
          doctor_id: number
          end_time: string
          id: number
          notes: string | null
          patient_id: string
          start_time: string
          status: string | null
          treatment_id: number
          updated_at: string | null
        }
        Insert: {
          clinic_id?: string
          created_at?: string | null
          date: string
          deleted_at?: string | null
          doctor_id: number
          end_time: string
          id?: number
          notes?: string | null
          patient_id: string
          start_time: string
          status?: string | null
          treatment_id: number
          updated_at?: string | null
        }
        Update: {
          clinic_id?: string
          created_at?: string | null
          date?: string
          deleted_at?: string | null
          doctor_id?: number
          end_time?: string
          id?: number
          notes?: string | null
          patient_id?: string
          start_time?: string
          status?: string | null
          treatment_id?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "appointments_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_treatment_id_fkey"
            columns: ["treatment_id"]
            isOneToOne: false
            referencedRelation: "treatments"
            referencedColumns: ["id"]
          },
        ]
      }
      dental_procedures: {
        Row: {
          created_at: string | null
          created_by: string
          date: string
          description: string | null
          id: number
          patient_id: string
          procedure_type: string | null
          teeth_involved: number[] | null
          type: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          date: string
          description?: string | null
          id?: number
          patient_id: string
          procedure_type?: string | null
          teeth_involved?: number[] | null
          type: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          date?: string
          description?: string | null
          id?: number
          patient_id?: string
          procedure_type?: string | null
          teeth_involved?: number[] | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dental_procedures_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      doctor_availability: {
        Row: {
          created_at: string | null
          day_of_week: number
          doctor_id: number
          end_time: string
          id: number
          is_active: boolean
          start_time: string
        }
        Insert: {
          created_at?: string | null
          day_of_week: number
          doctor_id: number
          end_time: string
          id?: number
          is_active?: boolean
          start_time: string
        }
        Update: {
          created_at?: string | null
          day_of_week?: number
          doctor_id?: number
          end_time?: string
          id?: number
          is_active?: boolean
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "doctor_availability_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
        ]
      }
      doctor_specialties: {
        Row: {
          created_at: string | null
          doctor_id: number
          id: number
          specialization_date: string
          specialty_code: string
          specialty_name: string
        }
        Insert: {
          created_at?: string | null
          doctor_id: number
          id?: number
          specialization_date: string
          specialty_code: string
          specialty_name: string
        }
        Update: {
          created_at?: string | null
          doctor_id?: number
          id?: number
          specialization_date?: string
          specialty_code?: string
          specialty_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "doctor_specialties_doctor_id_fkey"
            columns: ["doctor_id"]
            isOneToOne: false
            referencedRelation: "doctors"
            referencedColumns: ["id"]
          },
        ]
      }
      doctors: {
        Row: {
          albo_code: string
          avatar_url: string | null
          birth_date: string
          calendar_color: string
          citizenship: string | null
          created_at: string | null
          deleted_at: string | null
          email: string | null
          first_name: string
          fiscal_code: string
          iban: string | null
          id: number
          last_name: string
          mobile: string | null
          notes: string | null
          order_date: string
          order_number: string
          order_province: string
          pec: string
          phone: string | null
          practice_status: boolean
          qualifications: string | null
          residence_cap: string
          residence_street: string
          sex: string
          title: string
          updated_at: string | null
          vat_id: string | null
        }
        Insert: {
          albo_code?: string
          avatar_url?: string | null
          birth_date: string
          calendar_color?: string
          citizenship?: string | null
          created_at?: string | null
          deleted_at?: string | null
          email?: string | null
          first_name: string
          fiscal_code: string
          iban?: string | null
          id?: number
          last_name: string
          mobile?: string | null
          notes?: string | null
          order_date: string
          order_number: string
          order_province: string
          pec: string
          phone?: string | null
          practice_status?: boolean
          qualifications?: string | null
          residence_cap: string
          residence_street: string
          sex: string
          title?: string
          updated_at?: string | null
          vat_id?: string | null
        }
        Update: {
          albo_code?: string
          avatar_url?: string | null
          birth_date?: string
          calendar_color?: string
          citizenship?: string | null
          created_at?: string | null
          deleted_at?: string | null
          email?: string | null
          first_name?: string
          fiscal_code?: string
          iban?: string | null
          id?: number
          last_name?: string
          mobile?: string | null
          notes?: string | null
          order_date?: string
          order_number?: string
          order_province?: string
          pec?: string
          phone?: string | null
          practice_status?: boolean
          qualifications?: string | null
          residence_cap?: string
          residence_street?: string
          sex?: string
          title?: string
          updated_at?: string | null
          vat_id?: string | null
        }
        Relationships: []
      }
      event_attachments: {
        Row: {
          created_at: string | null
          event_id: number
          id: number
          name: string
          size: number
          type: string
          updated_at: string | null
          upload_date: string
          url: string
        }
        Insert: {
          created_at?: string | null
          event_id: number
          id?: number
          name: string
          size: number
          type: string
          updated_at?: string | null
          upload_date: string
          url: string
        }
        Update: {
          created_at?: string | null
          event_id?: number
          id?: number
          name?: string
          size?: number
          type?: string
          updated_at?: string | null
          upload_date?: string
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "event_attachments_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "patient_events"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_items: {
        Row: {
          created_at: string | null
          id: string
          invoice_id: string
          iva: number
          quantity: number
          subtotal: number
          total: number
          treatment_id: number
          unit_price: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          invoice_id: string
          iva?: number
          quantity?: number
          subtotal: number
          total: number
          treatment_id: number
          unit_price: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          invoice_id?: string
          iva?: number
          quantity?: number
          subtotal?: number
          total?: number
          treatment_id?: number
          unit_price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoice_items_treatment_id_fkey"
            columns: ["treatment_id"]
            isOneToOne: false
            referencedRelation: "treatments"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          clinic_id: string | null
          created_at: string | null
          deleted_at: string | null
          description: string
          due_date: string
          id: string
          invoice_number: string
          issue_date: string
          notes: string | null
          patient_id: string
          payment_date: string | null
          payment_method: string | null
          rate_number: number | null
          sal: number | null
          status: string | null
          subtotal: number
          tax_amount: number
          tax_rate: number
          total: number
          updated_at: string | null
        }
        Insert: {
          clinic_id?: string | null
          created_at?: string | null
          deleted_at?: string | null
          description: string
          due_date: string
          id?: string
          invoice_number: string
          issue_date: string
          notes?: string | null
          patient_id: string
          payment_date?: string | null
          payment_method?: string | null
          rate_number?: number | null
          sal?: number | null
          status?: string | null
          subtotal: number
          tax_amount: number
          tax_rate?: number
          total: number
          updated_at?: string | null
        }
        Update: {
          clinic_id?: string | null
          created_at?: string | null
          deleted_at?: string | null
          description?: string
          due_date?: string
          id?: string
          invoice_number?: string
          issue_date?: string
          notes?: string | null
          patient_id?: string
          payment_date?: string | null
          payment_method?: string | null
          rate_number?: number | null
          sal?: number | null
          status?: string | null
          subtotal?: number
          tax_amount?: number
          tax_rate?: number
          total?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      medical_devices: {
        Row: {
          created_at: string | null
          id: number
          name: string
          procedure_id: number
          udi_code: string | null
          updated_at: string | null
          use_date: string
        }
        Insert: {
          created_at?: string | null
          id?: number
          name: string
          procedure_id: number
          udi_code?: string | null
          updated_at?: string | null
          use_date: string
        }
        Update: {
          created_at?: string | null
          id?: number
          name?: string
          procedure_id?: number
          udi_code?: string | null
          updated_at?: string | null
          use_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "medical_devices_procedure_id_fkey"
            columns: ["procedure_id"]
            isOneToOne: false
            referencedRelation: "dental_procedures"
            referencedColumns: ["id"]
          },
        ]
      }
      patient_events: {
        Row: {
          created_at: string | null
          created_by: string
          date: string
          description: string
          id: number
          patient_id: string
          time: string
          title: string
          type: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          date: string
          description: string
          id?: number
          patient_id: string
          time: string
          title: string
          type: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          date?: string
          description?: string
          id?: number
          patient_id?: string
          time?: string
          title?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patient_events_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      patient_files: {
        Row: {
          category: string | null
          created_at: string | null
          deleted_at: string | null
          filename: string
          id: string
          mime_type: string
          path: string
          patient_id: string
          size: number
          updated_at: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          deleted_at?: string | null
          filename: string
          id?: string
          mime_type: string
          path: string
          patient_id: string
          size: number
          updated_at?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          deleted_at?: string | null
          filename?: string
          id?: string
          mime_type?: string
          path?: string
          patient_id?: string
          size?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patient_files_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      patients: {
        Row: {
          address: string
          allergies: string | null
          anamnesi_signed: boolean | null
          anamnesis: string
          city: string
          created_at: string | null
          date_of_birth: string
          email: string | null
          first_name: string
          fiscal_code: string | null
          id: string
          is_smoker: boolean | null
          last_name: string
          medical_history: string
          medications: string | null
          phone: string
          postal_code: string
          province: string
          udi: string | null
          updated_at: string | null
        }
        Insert: {
          address: string
          allergies?: string | null
          anamnesi_signed?: boolean | null
          anamnesis: string
          city: string
          created_at?: string | null
          date_of_birth: string
          email?: string | null
          first_name: string
          fiscal_code?: string | null
          id?: string
          is_smoker?: boolean | null
          last_name: string
          medical_history: string
          medications?: string | null
          phone: string
          postal_code: string
          province: string
          udi?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: string
          allergies?: string | null
          anamnesi_signed?: boolean | null
          anamnesis?: string
          city?: string
          created_at?: string | null
          date_of_birth?: string
          email?: string | null
          first_name?: string
          fiscal_code?: string | null
          id?: string
          is_smoker?: boolean | null
          last_name?: string
          medical_history?: string
          medications?: string | null
          phone?: string
          postal_code?: string
          province?: string
          udi?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      products: {
        Row: {
          category: string
          created_at: string | null
          description: string | null
          id: number
          last_order: string | null
          location: string | null
          min_quantity: number
          name: string
          notes: string | null
          price: number
          quantity: number
          supplier: string | null
          unit: string
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          description?: string | null
          id?: number
          last_order?: string | null
          location?: string | null
          min_quantity: number
          name: string
          notes?: string | null
          price: number
          quantity: number
          supplier?: string | null
          unit: string
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string | null
          id?: number
          last_order?: string | null
          location?: string | null
          min_quantity?: number
          name?: string
          notes?: string | null
          price?: number
          quantity?: number
          supplier?: string | null
          unit?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      reminders: {
        Row: {
          channel: string | null
          completed: boolean | null
          created_at: string | null
          date: string
          id: number
          patient_id: string | null
          text: string
          time: string
          title: string
          updated_at: string | null
        }
        Insert: {
          channel?: string | null
          completed?: boolean | null
          created_at?: string | null
          date: string
          id?: number
          patient_id?: string | null
          text: string
          time: string
          title: string
          updated_at?: string | null
        }
        Update: {
          channel?: string | null
          completed?: boolean | null
          created_at?: string | null
          date?: string
          id?: number
          patient_id?: string | null
          text?: string
          time?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reminders_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      treatments: {
        Row: {
          category: string
          created_at: string | null
          description: string | null
          duration: number
          id: number
          name: string
          price: number
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          description?: string | null
          duration: number
          id?: number
          name: string
          price: number
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string | null
          duration?: number
          id?: number
          name?: string
          price?: number
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      generate_invoice_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
