/**
 * MOKO SOSTANZA Dental CRM - Cron Job: Update Overdue Invoices
 * 
 * Supabase Edge Function che viene eseguita quotidianamente per:
 * - Aggiornare lo stato delle fatture scadute da 'sent' a 'overdue'
 * - Inviare notifiche (opzionale, per implementazioni future)
 * 
 * Schedule: Ogni giorno alle 08:00 UTC
 * Trigger: Cron job configurato in Supabase
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

interface Database {
  public: {
    Tables: {
      invoices: {
        Row: {
          id: string
          status: string
          due_date: string
          deleted_at: string | null
        }
        Update: {
          status?: string
          updated_at?: string
        }
      }
    }
  }
}

serve(async (req) => {
  try {
    // Verifica che la richiesta sia autorizzata (security check)
    const authHeader = req.headers.get('Authorization')
    if (!authHeader || !authHeader.includes('Bearer')) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }), 
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Inizializza client Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient<Database>(supabaseUrl, supabaseKey)

    // Ottieni la data di oggi in formato ISO
    const today = new Date().toISOString().split('T')[0]

    console.log(`🕐 Cron job started at ${new Date().toISOString()}`)
    console.log(`📅 Checking for overdue invoices before: ${today}`)

    // Aggiorna le fatture scadute
    const { data: updatedInvoices, error } = await supabase
      .from('invoices')
      .update({ 
        status: 'overdue',
        updated_at: new Date().toISOString()
      })
      .eq('status', 'sent')
      .lt('due_date', today)
      .is('deleted_at', null)
      .select('id, due_date')

    if (error) {
      console.error('❌ Error updating overdue invoices:', error)
      return new Response(
        JSON.stringify({ 
          error: 'Database error', 
          details: error.message 
        }), 
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }

    const updatedCount = updatedInvoices?.length || 0
    
    console.log(`✅ Updated ${updatedCount} invoices to overdue status`)
    
    if (updatedCount > 0) {
      console.log('📋 Updated invoice IDs:', updatedInvoices.map(inv => inv.id))
    }

    // Risposta di successo
    return new Response(
      JSON.stringify({ 
        success: true,
        message: `Successfully updated ${updatedCount} overdue invoices`,
        updated_invoices: updatedCount,
        timestamp: new Date().toISOString()
      }), 
      { 
        status: 200, 
        headers: { 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('💥 Cron job failed:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }), 
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    )
  }
})
