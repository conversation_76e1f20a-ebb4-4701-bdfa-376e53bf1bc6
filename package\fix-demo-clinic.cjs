/**
 * 🏥 FIX CLINICA DEMO PER RLS
 * 
 * <PERSON>o script:
 * 1. Verifica se la clinica demo esiste
 * 2. Crea la clinica demo se non esiste
 * 3. Testa nuovamente la creazione paziente
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

const DEMO_CLINIC_ID = '00000000-0000-0000-0000-000000000000';

console.log('🏥 === FIX CLINICA DEMO PER RLS ===\n');

async function fixDemoClinic() {
  const supabaseService = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('1️⃣ Verifica esistenza clinica demo...\n');

    // Verifica se la clinica demo esiste
    const { data: existingClinic, error: fetchError } = await supabaseService
      .from('clinics')
      .select('*')
      .eq('id', DEMO_CLINIC_ID)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.log('❌ Errore verifica clinica:', fetchError.message);
      return;
    }

    if (existingClinic) {
      console.log('✅ Clinica demo già esistente:');
      console.log('📋 Nome:', existingClinic.name);
      console.log('📋 ID:', existingClinic.id);
    } else {
      console.log('⚠️ Clinica demo non esistente, la creo...');

      // Crea la clinica demo
      const { data: newClinic, error: createError } = await supabaseService
        .from('clinics')
        .insert({
          id: DEMO_CLINIC_ID,
          name: 'Clinica Demo',
          address: 'Via Demo 123',
          phone: '************',
          email: '<EMAIL>'
        })
        .select()
        .single();

      if (createError) {
        console.log('❌ Errore creazione clinica demo:', createError.message);
        console.log('📋 Dettagli:', createError);
        return;
      }

      console.log('✅ Clinica demo creata con successo:');
      console.log('📋 Dati:', newClinic);
    }

    console.log('\n2️⃣ Test creazione paziente con clinica demo...\n');

    // Ora testa la creazione di un paziente
    const supabaseUser = createClient(supabaseUrl, supabaseAnonKey);

    // Logout da eventuali sessioni precedenti
    await supabaseUser.auth.signOut();

    // Crea nuovo utente per test
    const timestamp = Date.now();
    const testEmail = `test.clinic.${timestamp}@demo.com`;

    console.log(`📧 Creazione nuovo utente: ${testEmail}`);

    const { data: signUpData, error: signUpError } = await supabaseUser.auth.signUp({
      email: testEmail,
      password: 'demo123456'
    });

    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }

    console.log('✅ Nuovo utente creato');

    // Chiama Edge Function per iniettare JWT claims
    console.log('\n🔧 Chiamata Edge Function per JWT claims...');

    const { data: functionResult, error: functionError } = await supabaseUser.functions.invoke('issue-jwt', {
      body: { user: signUpData.user }
    });

    if (functionError) {
      console.log('❌ Errore Edge Function:', functionError.message);
      return;
    }

    console.log('✅ Edge Function eseguita:', functionResult);

    // Aspetta propagazione
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verifica JWT claims
    const { data: { user: updatedUser }, error: userError } = await supabaseUser.auth.getUser();

    if (userError || !updatedUser) {
      console.log('❌ Errore ricarica utente:', userError?.message);
      return;
    }

    const clinicId = updatedUser.app_metadata?.clinic_id;
    console.log('\n📋 JWT claims:');
    console.log(`  - Clinic ID: ${clinicId}`);
    console.log(`  - App Role: ${updatedUser.app_metadata?.app_role}`);

    if (clinicId !== DEMO_CLINIC_ID) {
      console.log('❌ Clinic ID non corrispondente:');
      console.log(`   - Atteso: ${DEMO_CLINIC_ID}`);
      console.log(`   - Trovato: ${clinicId}`);
      return;
    }

    console.log('\n3️⃣ Test creazione paziente...\n');

    // Test creazione paziente
    const patientData = {
      first_name: 'Test',
      last_name: 'Clinica',
      clinic_id: clinicId,
      email: `patient.clinic.${timestamp}@demo.com`,
      phone: '**********'
    };

    console.log('📋 Dati paziente:', patientData);

    const { data: newPatient, error: createError } = await supabaseUser
      .from('patients')
      .insert(patientData)
      .select()
      .single();

    if (createError) {
      console.log('❌ ERRORE CREAZIONE PAZIENTE:', createError.message);
      console.log('📋 Codice:', createError.code);
      console.log('📋 Dettagli:', createError);

      if (createError.code === '42501') {
        console.log('\n🔍 ANALISI: Le policy RLS stanno ancora bloccando');
        console.log('📋 Possibili cause:');
        console.log('   1. Policy non create correttamente nel database');
        console.log('   2. Path JWT errato nelle policy');
        console.log('   3. RLS non abilitato correttamente');
        
        console.log('\n🔧 SOLUZIONE: Verifica nel dashboard Supabase:');
        console.log('   1. Vai su Database > Tables > patients > RLS');
        console.log('   2. Verifica che ci siano le policy patients_rls_*_v2');
        console.log('   3. Verifica che RLS sia abilitato');
        console.log('   4. Se le policy non ci sono, riesegui lo script SQL');
      }

      return;
    }

    console.log('✅ PAZIENTE CREATO CON SUCCESSO!');
    console.log('📋 Dati paziente:', newPatient);

    // Test lettura
    const { data: patients, error: readError } = await supabaseUser
      .from('patients')
      .select('*')
      .eq('clinic_id', clinicId);

    if (readError) {
      console.log('⚠️ Errore lettura pazienti:', readError.message);
    } else {
      console.log(`✅ Lettura pazienti: ${patients?.length || 0} pazienti trovati`);
    }

    // Cleanup
    console.log('\n🧹 Cleanup...');
    await supabaseService.from('patients').delete().eq('id', newPatient.id);
    console.log('✅ Paziente di test rimosso');

    console.log('\n🎉 === SOLUZIONE COMPLETA ===');
    console.log('✅ Clinica demo esistente');
    console.log('✅ JWT claims funzionanti');
    console.log('✅ Creazione pazienti funzionante');
    console.log('✅ RLS funzionante correttamente');

  } catch (error) {
    console.error('💥 Errore:', error);
    console.log('\n📋 Stack trace:', error.stack);
  }
}

// Esegui il fix
fixDemoClinic();
