/**
 * MOKO SOSTANZA Dental CRM - File Upload Component
 * 
 * Componente drag-and-drop per upload OTP, CBCT, documenti
 */

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { <PERSON><PERSON>, Card, Badge, Progress } from 'flowbite-react';
import { Icon } from '@iconify/react';
import { FILE_CATEGORIES } from '../../services/FileService';
import { useFileStore } from '../../store/useFileStore';
import { useToast } from '../shared/Toast';

interface FileUploadProps {
  patientId: string;
  category?: 'otp' | 'cbct' | 'document';
  onUploadComplete?: () => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ 
  patientId, 
  category = 'document',
  onUploadComplete 
}) => {
  const { upload, uploading, uploadProgress } = useFileStore();
  const { showSuccess, showError } = useToast();
  const [dragActive, setDragActive] = useState(false);

  const categoryConfig = FILE_CATEGORIES[category];
  const maxSizeMB = Math.round(categoryConfig.maxSize / (1024 * 1024));

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setDragActive(false);
    
    if (acceptedFiles.length === 0) {
      showError('Nessun file valido selezionato');
      return;
    }

    for (const file of acceptedFiles) {
      try {
        const success = await upload(patientId, file, category);
        
        if (success) {
          showSuccess(`${file.name} caricato con successo`);
          onUploadComplete?.();
        } else {
          showError(`Errore nel caricamento di ${file.name}`);
        }
      } catch (error) {
        console.error('Upload error:', error);
        showError(`Errore nel caricamento di ${file.name}`);
      }
    }
  }, [patientId, category, upload, showSuccess, showError, onUploadComplete]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'application/pdf': ['.pdf'],
      'application/dicom': ['.dcm']
    },
    maxSize: categoryConfig.maxSize,
    multiple: true,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            showError(`${file.name} è troppo grande (max ${maxSizeMB}MB)`);
          } else if (error.code === 'file-invalid-type') {
            showError(`${file.name} non è un tipo di file supportato`);
          } else {
            showError(`Errore con ${file.name}: ${error.message}`);
          }
        });
      });
    }
  });

  const getCategoryIcon = () => {
    switch (category) {
      case 'otp':
        return 'solar:medical-kit-outline';
      case 'cbct':
        return 'solar:scanner-outline';
      default:
        return 'solar:document-add-outline';
    }
  };

  const getCategoryColor = () => {
    switch (category) {
      case 'otp':
        return 'bg-blue-50 border-blue-200 text-blue-700';
      case 'cbct':
        return 'bg-purple-50 border-purple-200 text-purple-700';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-700';
    }
  };

  return (
    <Card className="w-full">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon icon={getCategoryIcon()} height={20} />
            <h3 className="text-lg font-semibold">
              Carica {categoryConfig.label}
            </h3>
          </div>
          <Badge color="info" size="sm">
            Max {maxSizeMB}MB
          </Badge>
        </div>

        {/* Dropzone */}
        <div
          {...getRootProps()}
          className={`
            relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
            ${isDragActive || dragActive 
              ? 'border-primary bg-primary/5 scale-[1.02]' 
              : getCategoryColor()
            }
            hover:border-primary hover:bg-primary/5
          `}
        >
          <input {...getInputProps()} />
          
          <div className="space-y-4">
            <div className="flex justify-center">
              <Icon 
                icon={isDragActive ? 'solar:cloud-upload-bold' : getCategoryIcon()} 
                height={48} 
                className={isDragActive ? 'text-primary animate-bounce' : 'text-gray-400'}
              />
            </div>
            
            <div>
              <p className="text-lg font-medium mb-2">
                {isDragActive 
                  ? `Rilascia qui i file ${categoryConfig.label}` 
                  : `Trascina qui i file ${categoryConfig.label}`
                }
              </p>
              <p className="text-sm text-gray-500 mb-4">
                oppure <span className="text-primary font-medium">clicca per selezionare</span>
              </p>
              <p className="text-xs text-gray-400">
                Formati supportati: {categoryConfig.extensions.join(', ')} • Max {maxSizeMB}MB
              </p>
            </div>
          </div>

          {uploading && (
            <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-lg">
              <div className="text-center">
                <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2"></div>
                <p className="text-sm font-medium">Caricamento in corso...</p>
              </div>
            </div>
          )}
        </div>

        {/* Upload Progress */}
        {uploadProgress.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Progresso Upload:</h4>
            {uploadProgress.map((progress) => (
              <div key={progress.fileId} className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="truncate flex-1 mr-2">{progress.filename}</span>
                  <div className="flex items-center gap-2">
                    {progress.status === 'success' && (
                      <Icon icon="solar:check-circle-bold" className="text-green-500" height={16} />
                    )}
                    {progress.status === 'error' && (
                      <Icon icon="solar:close-circle-bold" className="text-red-500" height={16} />
                    )}
                    <span className="text-xs text-gray-500">
                      {progress.progress}%
                    </span>
                  </div>
                </div>
                <Progress 
                  progress={progress.progress} 
                  color={progress.status === 'error' ? 'red' : progress.status === 'success' ? 'green' : 'blue'}
                  size="sm"
                />
                {progress.error && (
                  <p className="text-xs text-red-500">{progress.error}</p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-2 pt-2 border-t">
          <Button
            size="sm"
            color="light"
            onClick={() => (document.querySelector('input[type="file"]') as HTMLInputElement)?.click()}
            disabled={uploading}
          >
            <Icon icon="solar:folder-outline" height={16} className="mr-1" />
            Seleziona File
          </Button>
          
          {category === 'document' && (
            <>
              <Button
                size="sm"
                color="info"
                onClick={() => {
                  // Trigger OTP upload
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.jpg,.jpeg,.png,.pdf,.dcm';
                  input.onchange = (e) => {
                    const files = (e.target as HTMLInputElement).files;
                    if (files) {
                      onDrop(Array.from(files));
                    }
                  };
                  input.click();
                }}
                disabled={uploading}
              >
                <Icon icon="solar:medical-kit-outline" height={16} className="mr-1" />
                OTP
              </Button>
              
              <Button
                size="sm"
                color="purple"
                onClick={() => {
                  // Trigger CBCT upload
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = '.jpg,.jpeg,.png,.pdf,.dcm';
                  input.onchange = (e) => {
                    const files = (e.target as HTMLInputElement).files;
                    if (files) {
                      onDrop(Array.from(files));
                    }
                  };
                  input.click();
                }}
                disabled={uploading}
              >
                <Icon icon="solar:scanner-outline" height={16} className="mr-1" />
                CBCT
              </Button>
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

export default FileUpload;