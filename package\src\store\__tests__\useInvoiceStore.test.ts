/**
 * MOKO SOSTANZA Dental CRM - useInvoiceStore Unit Tests
 * Test per lo store Zustand delle fatture con optimistic updates
 */

import { renderHook, act } from '@testing-library/react';
import { useInvoiceStore } from '../useInvoiceStore';
import InvoiceService from '../../services/InvoiceService';
import type { InvoiceWithDetails } from '../../services/InvoiceService';

// Mock del servizio InvoiceService
jest.mock('../../services/InvoiceService');
const mockInvoiceService = InvoiceService as jest.Mocked<typeof InvoiceService>;

// Mock data
const mockInvoice: InvoiceWithDetails = {
  id: 'test-invoice-id',
  invoice_number: 'INV-2025-001',
  issue_date: '2025-01-15',
  due_date: '2025-02-15',
  subtotal: 100.00,
  tax_rate: 22.00,
  tax_amount: 22.00,
  total: 122.00,
  status: 'draft',
  payment_method: null,
  payment_date: null,
  patient_id: 'patient-id-123',
  description: 'Test invoice',
  notes: 'Test notes',
  created_at: '2025-01-15T10:00:00Z',
  updated_at: '2025-01-15T10:00:00Z',
  clinic_id: 'test-clinic-id',
  deleted_at: null,
  rate_number: null,
  sal: null,
  patient: {
    id: 'patient-id-123',
    first_name: 'Mario',
    last_name: 'Rossi',
    email: '<EMAIL>',
    phone: '*********',
    date_of_birth: '1980-01-01',
    fiscal_code: '****************',
    address: 'Via Roma 1',
    city: 'Milano',
    postal_code: '20100',
    province: 'MI',
    medical_history: 'Nessuna allergia nota',
    allergies: null,
    medications: null,
    is_smoker: false,
    anamnesis: 'Anamnesi test',
    anamnesi_signed: true,
    udi: null,
    created_at: '2025-01-15T10:00:00Z',
    updated_at: '2025-01-15T10:00:00Z'
  }
};

const mockInvoices = [
  mockInvoice,
  {
    ...mockInvoice,
    id: 'test-invoice-id-2',
    invoice_number: 'INV-2025-002',
    description: 'Second invoice',
  }
];

describe('useInvoiceStore', () => {
  beforeEach(() => {
    // Reset store state
    const { result } = renderHook(() => useInvoiceStore());
    act(() => {
      result.current.invoices.length = 0;
      result.current.setError(null);
    });

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('load', () => {
    it('should load invoices successfully', async () => {
      const mockResult = {
        invoices: mockInvoices,
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1
        }
      };

      mockInvoiceService.getInvoices.mockResolvedValue(mockResult);

      const { result } = renderHook(() => useInvoiceStore());

      await act(async () => {
        await result.current.load();
      });

      expect(mockInvoiceService.getInvoices).toHaveBeenCalledWith({});
      expect(result.current.invoices).toEqual(mockInvoices);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle load invoices error', async () => {
      const error = new Error('Failed to load invoices');
      mockInvoiceService.getInvoices.mockRejectedValue(error);

      const { result } = renderHook(() => useInvoiceStore());

      await act(async () => {
        await result.current.load();
      });

      expect(result.current.invoices).toEqual([]);
      expect(result.current.error).toBe('Failed to load invoices');
      expect(result.current.loading).toBe(false);
    });

    it('should load invoices with filters', async () => {
      const mockResult = {
        invoices: [mockInvoice],
        pagination: {
          page: 1,
          limit: 50,
          total: 1,
          totalPages: 1
        }
      };

      const filters = { search: 'test', status: 'draft' };

      mockInvoiceService.getInvoices.mockResolvedValue(mockResult);

      const { result } = renderHook(() => useInvoiceStore());

      await act(async () => {
        await result.current.load(filters);
      });

      expect(mockInvoiceService.getInvoices).toHaveBeenCalledWith(filters);
      expect(result.current.invoices).toEqual([mockInvoice]);
    });
  });

  describe('create', () => {
    it('should create invoice with optimistic update', async () => {
      const newInvoiceData = {
        invoice_number: 'INV-2025-003',
        patient_id: 'patient-id-456',
        issue_date: '2025-01-20',
        due_date: '2025-02-20',
        subtotal: 150.00,
        tax_rate: 22.00,
        tax_amount: 33.00,
        total: 183.00,
        description: 'New invoice',
      };

      const items = [
        {
          treatment_id: 1,
          quantity: 1,
          unit_price: 150.00,
          subtotal: 150.00,
          iva: 22.00,
          total: 183.00,
        }
      ];

      const createdInvoice = { ...mockInvoice, ...newInvoiceData, id: 'new-invoice-id' };
      mockInvoiceService.createInvoiceWithItems.mockResolvedValue(createdInvoice);

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load some invoices
      act(() => {
        result.current.invoices.push(mockInvoice);
      });

      await act(async () => {
        await result.current.create(newInvoiceData, items);
      });

      expect(mockInvoiceService.createInvoiceWithItems).toHaveBeenCalledWith(newInvoiceData, items);
      expect(result.current.invoices).toContainEqual(createdInvoice);
      expect(result.current.invoices).toHaveLength(2);
      expect(result.current.error).toBeNull();
    });

    it('should rollback optimistic update on create error', async () => {
      const newInvoiceData = {
        invoice_number: 'INV-2025-003',
        patient_id: 'patient-id-456',
        issue_date: '2025-01-20',
        due_date: '2025-02-20',
        subtotal: 150.00,
        tax_rate: 22.00,
        tax_amount: 33.00,
        total: 183.00,
        description: 'New invoice',
      };

      const items = [
        {
          treatment_id: 1,
          quantity: 1,
          unit_price: 150.00,
          subtotal: 150.00,
          iva: 22.00,
          total: 183.00,
        }
      ];

      const error = new Error('Failed to create invoice');
      mockInvoiceService.createInvoiceWithItems.mockRejectedValue(error);

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load some invoices
      act(() => {
        result.current.invoices.push(mockInvoice);
      });

      const originalLength = result.current.invoices.length;

      await act(async () => {
        await result.current.create(newInvoiceData, items);
      });

      // Should rollback to original state
      expect(result.current.invoices).toHaveLength(originalLength);
      expect(result.current.invoices).not.toContainEqual(
        expect.objectContaining({ description: 'New invoice' })
      );
      expect(result.current.error).toBe('Failed to create invoice');
    });
  });

  describe('update', () => {
    it('should update invoice with optimistic update', async () => {
      const updateData = {
        description: 'Updated invoice description',
        total: 150.00,
      };

      const updatedInvoice = { ...mockInvoice, ...updateData };
      mockInvoiceService.updateInvoice.mockResolvedValue(updatedInvoice);

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load invoice
      act(() => {
        result.current.invoices.push(mockInvoice);
      });

      await act(async () => {
        await result.current.update(mockInvoice.id, updateData);
      });

      expect(mockInvoiceService.updateInvoice).toHaveBeenCalledWith(mockInvoice.id, updateData);
      expect(result.current.invoices[0]).toEqual(updatedInvoice);
      expect(result.current.error).toBeNull();
    });

    it('should rollback optimistic update on update error', async () => {
      const updateData = {
        description: 'Updated invoice description',
        total: 150.00,
      };

      const error = new Error('Failed to update invoice');
      mockInvoiceService.updateInvoice.mockRejectedValue(error);

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load invoice
      act(() => {
        result.current.invoices.push(mockInvoice);
      });

      const originalInvoice = result.current.invoices[0];

      await act(async () => {
        await result.current.update(mockInvoice.id, updateData);
      });

      // Should rollback to original state
      expect(result.current.invoices[0]).toEqual(originalInvoice);
      expect(result.current.error).toBe('Failed to update invoice');
    });
  });

  describe('softDelete', () => {
    it('should soft delete invoice with optimistic update', async () => {
      mockInvoiceService.softDeleteInvoice.mockResolvedValue();

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load invoices
      act(() => {
        result.current.invoices.push(...mockInvoices);
      });

      await act(async () => {
        await result.current.softDelete(mockInvoice.id);
      });

      expect(mockInvoiceService.softDeleteInvoice).toHaveBeenCalledWith(mockInvoice.id);
      expect(result.current.invoices).not.toContainEqual(
        expect.objectContaining({ id: mockInvoice.id })
      );
      expect(result.current.invoices).toHaveLength(1);
      expect(result.current.error).toBeNull();
    });

    it('should rollback optimistic update on soft delete error', async () => {
      const error = new Error('Failed to delete invoice');
      mockInvoiceService.softDeleteInvoice.mockRejectedValue(error);

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load invoices
      act(() => {
        result.current.invoices.push(...mockInvoices);
      });

      const originalLength = result.current.invoices.length;

      await act(async () => {
        await result.current.softDelete(mockInvoice.id);
      });

      // Should rollback to original state
      expect(result.current.invoices).toHaveLength(originalLength);
      expect(result.current.invoices).toContainEqual(
        expect.objectContaining({ id: mockInvoice.id })
      );
      expect(result.current.error).toBe('Failed to delete invoice');
    });
  });

  describe('restore', () => {
    it('should restore invoice successfully', async () => {
      const restoredInvoice = { ...mockInvoice, status: 'draft' as const };
      mockInvoiceService.restoreInvoice.mockResolvedValue(restoredInvoice);

      const { result } = renderHook(() => useInvoiceStore());

      await act(async () => {
        await result.current.restore(mockInvoice.id);
      });

      expect(mockInvoiceService.restoreInvoice).toHaveBeenCalledWith(mockInvoice.id);
      expect(result.current.invoices).toContainEqual(restoredInvoice);
      expect(result.current.error).toBeNull();
    });

    it('should handle restore invoice error', async () => {
      const error = new Error('Failed to restore invoice');
      mockInvoiceService.restoreInvoice.mockRejectedValue(error);

      const { result } = renderHook(() => useInvoiceStore());

      await act(async () => {
        await result.current.restore(mockInvoice.id);
      });

      expect(result.current.error).toBe('Failed to restore invoice');
    });
  });

  describe('markAsPaid', () => {
    it('should mark invoice as paid with optimistic update', async () => {
      const paymentDate = '2025-01-20';
      const paymentMethod = 'Carta di credito';
      const paidInvoice = {
        ...mockInvoice,
        status: 'paid' as const,
        payment_date: paymentDate,
        payment_method: paymentMethod,
      };

      mockInvoiceService.markAsPaid.mockResolvedValue(paidInvoice);

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load invoice
      act(() => {
        result.current.invoices.push(mockInvoice);
      });

      await act(async () => {
        await result.current.markAsPaid(mockInvoice.id, paymentDate, paymentMethod);
      });

      expect(mockInvoiceService.markAsPaid).toHaveBeenCalledWith(mockInvoice.id, paymentDate, paymentMethod);
      expect(result.current.invoices[0]).toEqual(paidInvoice);
      expect(result.current.error).toBeNull();
    });

    it('should rollback optimistic update on mark as paid error', async () => {
      const paymentDate = '2025-01-20';
      const paymentMethod = 'Carta di credito';
      const error = new Error('Failed to mark as paid');
      
      mockInvoiceService.markAsPaid.mockRejectedValue(error);

      const { result } = renderHook(() => useInvoiceStore());

      // Pre-load invoice
      act(() => {
        result.current.invoices.push(mockInvoice);
      });

      const originalInvoice = result.current.invoices[0];

      await act(async () => {
        await result.current.markAsPaid(mockInvoice.id, paymentDate, paymentMethod);
      });

      // Should rollback to original state
      expect(result.current.invoices[0]).toEqual(originalInvoice);
      expect(result.current.error).toBe('Failed to mark as paid');
    });
  });

  describe('setError', () => {
    it('should set error message', () => {
      const { result } = renderHook(() => useInvoiceStore());

      act(() => {
        result.current.setError('Test error message');
      });

      expect(result.current.error).toBe('Test error message');
    });

    it('should clear error when setting null', () => {
      const { result } = renderHook(() => useInvoiceStore());

      // Set error first
      act(() => {
        result.current.setError('Test error message');
      });

      expect(result.current.error).toBe('Test error message');

      // Clear error
      act(() => {
        result.current.setError(null);
      });

      expect(result.current.error).toBeNull();
    });
  });
});
