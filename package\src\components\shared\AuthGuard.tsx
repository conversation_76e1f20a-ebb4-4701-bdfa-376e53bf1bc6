/**
 * Auth Guard Component per gestire lo stato di autenticazione
 * Sostituisce la logica localStorage con Supabase Auth
 */
import { FC, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: FC<AuthGuardProps> = ({ children }) => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const isAuthRoute = location.pathname.startsWith('/auth');
    
    if (!loading) {
      if (!user && !isAuthRoute) {
        // Redirect to login if user is not authenticated and not on auth pages
        console.log('AuthGuard: Redirecting to login - no user found');
        navigate('/auth/login', { replace: true });
      } else if (user && isAuthRoute) {
        // Redirect to home if user is authenticated and on auth pages
        console.log('AuthGuard: Redirecting to home - user already authenticated');
        navigate('/', { replace: true });
      }
    }
  }, [user, loading, navigate, location.pathname]);

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-lightgray dark:bg-dark">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300 text-lg">
            Verifica autenticazione...
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthGuard;
