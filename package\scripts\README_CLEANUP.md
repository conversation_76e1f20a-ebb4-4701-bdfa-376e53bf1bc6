# Cleanup Development Data Script

Script per la pulizia dei dati di sviluppo e test dal database Supabase.

## ⚠️ ATTENZIONE

Questo script è **IRREVERSIBILE** e eliminerà tutti i dati di sviluppo dal database. Usalo solo in ambiente di sviluppo!

## Prerequisiti

1. **Variabili d'ambiente** necessarie:

   ```bash
   VITE_SUPABASE_URL=http://localhost:54321
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   NODE_ENV=development
   ```

2. **Service Role Key**: È necessaria la service role key di Supabase (non la anon key) per eseguire operazioni di admin.

## Utilizzo

### Controllo Sicurezza

```bash
# Controlla cosa verrebbe eliminato (senza --force)
npx tsx scripts/cleanup_dev.ts
```

### Esecuzione Completa

```bash
# Elimina tutti i dati di test/dev
npx tsx scripts/cleanup_dev.ts --force
```

### Opzioni Avanzate

```bash
# Include anche i medici nella pulizia (attenzione!)
npx tsx scripts/cleanup_dev.ts --force --include-doctors

# Include reset delle sequence (per ora disabilitato)
npx tsx scripts/cleanup_dev.ts --force --reset-sequences
```

## Cosa Viene Eliminato

### Soft Delete (marked as deleted, non rimosso fisicamente)

- ✅ **Fatture** (`invoices.deleted_at`)
- ✅ **Pazienti** (`patients.deleted_at`)
- ✅ **Appuntamenti** (`appointments.deleted_at`)

### Hard Delete (rimosso fisicamente)

- 🗑️ **Items delle fatture** (`invoice_items`)
- 🗑️ **Promemoria** (`reminders`)
- 🗑️ **Trattamenti** (`treatments`)
- 🗑️ **Procedure dentali** (`dental_procedures`)

### Opzionale

- ⚠️ **Medici** (solo con `--include-doctors`)

## Protezioni di Sicurezza

1. **Controllo Ambiente**: Funziona solo se:

   - `NODE_ENV=development`, oppure
   - `VITE_SUPABASE_URL` contiene `localhost` o `127.0.0.1`

2. **Conferma Richiesta**: Richiede il flag `--force` per eseguire

3. **Service Role Key**: Necessaria per operazioni di admin

4. **Preservazione Medici**: I medici non vengono eliminati per default (potrebbero essere collegati all'autenticazione)

## Output di Esempio

```
🛠️  MOKO SOSTANZA - Cleanup Database Dev
=====================================

🧹 Pulizia items fatture...
🧹 Pulizia fatture...
🧹 Pulizia appuntamenti...
🧹 Pulizia promemoria...
🧹 Pulizia pazienti...
🧹 Pulizia trattamenti...
🧹 Pulizia procedure dentali...
⚠️  I medici non sono stati eliminati (usa --include-doctors per includerli)

📊 Statistiche pulizia:
━━━━━━━━━━━━━━━━━━━━━━━━━━
   Fatture:           15
   Items fatture:     87
   Pazienti:          23
   Appuntamenti:      42
   Trattamenti:       12
   Medici:            0
   Procedure dentali: 8
   Promemoria:        5
   File:              0

📈 Totale record processati: 192

✅ Pulizia completata con successo!
```

## Dopo la Pulizia

1. **Riavvia il server** se necessario
2. **Verifica i dati di seed**: Assicurati che i dati base siano ancora presenti
3. **Controlla l'autenticazione**: Verifica che il login funzioni ancora
4. **Re-seed se necessario**: Puoi ririempire il DB con dati di test:
   ```bash
   npx supabase db reset --local
   ```

## Risoluzione Problemi

### "Service role key richiesta"

```bash
# Imposta la service role key (non la anon key!)
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key-here"
```

### "Solo ambiente di sviluppo"

```bash
# Assicurati che sia impostato l'ambiente di sviluppo
export NODE_ENV=development
# oppure
export VITE_SUPABASE_URL=http://localhost:54321
```

### "Operazione annullata"

```bash
# Usa il flag --force per confermare
npx tsx scripts/cleanup_dev.ts --force
```

## Personalizzazione

Per modificare quali tabelle vengono pulite o cambiare la logica, modifica il file `scripts/cleanup_dev.ts`:

```typescript
// Aggiungi nuove tabelle nella funzione cleanupDevDatabase()
stats.my_table = await cleanupTable('my_table', 'mia tabella', false);
```
