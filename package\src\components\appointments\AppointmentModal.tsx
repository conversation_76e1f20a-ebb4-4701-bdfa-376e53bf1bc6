import { useState, useEffect } from 'react';
import { Modal, Button, Label, TextInput, Select, Textarea } from 'flowbite-react';
import { Icon } from '@iconify/react';
import { AppointmentService, type Appointment, type Patient, type Treatment } from '../../services/AppointmentService';
import { PatientService } from '../../services/PatientService';
import { DoctorService, type DoctorWithRelations } from '../../services/DoctorService';
import { TreatmentService } from '../../services/TreatmentService';
import { useAppointmentStore } from '../../store/useAppointmentStore';
import { useToast } from '../shared/Toast';
import QuickPatientModal from '../patients/QuickPatientModal';

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointment?: Appointment;
  selectedDate?: string;
  selectedTime?: string;
}

const AppointmentModal = ({ isOpen, onClose, appointment, selectedDate, selectedTime }: AppointmentModalProps) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [doctors, setDoctors] = useState<DoctorWithRelations[]>([]);
  const [treatments, setTreatments] = useState<Treatment[]>([]);
  const [loading, setLoading] = useState(false);
  const [isQuickPatientModalOpen, setIsQuickPatientModalOpen] = useState(false);
  
  // Stati per l'autocompletamento pazienti
  const [patientSearch, setPatientSearch] = useState('');
  const [showPatientSuggestions, setShowPatientSuggestions] = useState(false);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);

  // Store actions
  const { create, update } = useAppointmentStore();
  const { showSuccess, showError } = useToast();

  const [formData, setFormData] = useState({
    patientId: '',
    doctorId: 0,
    treatmentId: 0,
    date: '',
    startTime: '',
    endTime: '',
    status: 'confermato' as Appointment['status'],
    notes: ''
  });

  // Load data when modal opens
  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  const loadData = async () => {
    try {
      const [patientsResult, doctorsResult, treatmentsResult] = await Promise.all([
        PatientService.getPatients(),
        DoctorService.getDoctors(),
        TreatmentService.getTreatments()
      ]);

      setPatients(patientsResult.patients || []);
      setDoctors(doctorsResult.doctors || []);
      setTreatments(treatmentsResult.treatments || []);
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  // Calcola l'ora di fine in base al trattamento selezionato
  const calculateEndTime = (startTime: string, treatmentId: number) => {
    if (!startTime || !treatmentId) return '';

    const treatment = treatments.find(t => t.id === treatmentId);
    if (!treatment) return '';

    const [hours, minutes] = startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const endMinutes = startMinutes + treatment.duration;

    const endHours = Math.floor(endMinutes / 60);
    const endMins = endMinutes % 60;

    return `${String(endHours).padStart(2, '0')}:${String(endMins).padStart(2, '0')}`;
  };

  // Inizializza il form quando si apre il modale
  useEffect(() => {
    if (appointment) {
      // Modifica di un appuntamento esistente
      const patient = patients.find(p => p.id === appointment.patient_id);
      const patientName = patient 
        ? `${patient.first_name} ${patient.last_name}`
        : '';
      
      setFormData({
        patientId: appointment.patient_id,
        doctorId: appointment.doctor_id,
        treatmentId: appointment.treatment_id,
        date: appointment.date,
        startTime: appointment.start_time,
        endTime: appointment.end_time,
        status: appointment.status,
        notes: appointment.notes || ''
      });
      setPatientSearch(patientName);
    } else {
      // Nuovo appuntamento
      setFormData({
        patientId: '',
        doctorId: 0,
        treatmentId: 0,
        date: selectedDate || new Date().toISOString().split('T')[0],
        startTime: selectedTime || '09:00',
        endTime: '',
        status: 'confermato',
        notes: ''
      });
      setPatientSearch('');
    }
  }, [isOpen, appointment, selectedDate, selectedTime, patients]);

  // Aggiorna l'ora di fine quando cambia il trattamento o l'ora di inizio
  useEffect(() => {
    if (formData.treatmentId && formData.startTime) {
      const treatmentIdNumber = typeof formData.treatmentId === 'string' ? parseInt(formData.treatmentId) : formData.treatmentId;
      const endTime = calculateEndTime(formData.startTime, treatmentIdNumber);
      setFormData(prev => ({ ...prev, endTime }));
    }
  }, [formData.treatmentId, formData.startTime, treatments]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Gestisce la ricerca dei pazienti
  const handlePatientSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPatientSearch(value);
    
    if (value.length > 0) {
      const filtered = patients.filter(patient => 
        `${patient.first_name} ${patient.last_name}`.toLowerCase().includes(value.toLowerCase()) ||
        patient.fiscal_code?.toLowerCase().includes(value.toLowerCase()) ||
        patient.phone?.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredPatients(filtered);
      setShowPatientSuggestions(true);
    } else {
      setShowPatientSuggestions(false);
      setFormData(prev => ({ ...prev, patientId: '' }));
    }
  };

  // Seleziona un paziente dall'autocompletamento
  const handleSelectPatient = (patient: Patient) => {
    setPatientSearch(`${patient.first_name} ${patient.last_name}`);
    setFormData(prev => ({ ...prev, patientId: patient.id }));
    setShowPatientSuggestions(false);
  };

  // Nasconde i suggerimenti quando si clicca fuori
  const handlePatientInputBlur = () => {
    setTimeout(() => setShowPatientSuggestions(false), 200);
  };

  // Gestisce l'apertura del modale per l'aggiunta rapida di un paziente
  const handleOpenQuickPatientModal = () => {
    setIsQuickPatientModalOpen(true);
  };

  // Gestisce la chiusura del modale per l'aggiunta rapida di un paziente
  const handleCloseQuickPatientModal = () => {
    setIsQuickPatientModalOpen(false);
  };

  // Gestisce l'aggiunta di un nuovo paziente
  const handlePatientAdded = async (patientId: string) => {
    setIsQuickPatientModalOpen(false);
    
    // Ricarica la lista dei pazienti per includere il nuovo paziente
    try {
      const patientsResult = await PatientService.getPatients();
      setPatients(patientsResult.patients || []);
      
      // Trova il nuovo paziente e aggiorna il form
      const newPatient = (patientsResult.patients || []).find(p => p.id === patientId);
      if (newPatient) {
        setFormData(prev => ({ ...prev, patientId }));
        setPatientSearch(`${newPatient.first_name} ${newPatient.last_name}`);
      }
    } catch (error) {
      console.error('Error reloading patients:', error);
      // Anche se c'è un errore nel ricaricare, aggiorna comunque il form
      setFormData(prev => ({ ...prev, patientId }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      
      console.log('Form data:', formData);
      
      // Valida che tutti i campi richiesti siano presenti
      if (!formData.patientId) {
        throw new Error('Seleziona un paziente dalla lista dei suggerimenti');
      }
      if (!formData.doctorId || !formData.treatmentId || !formData.date || !formData.startTime) {
        throw new Error('Tutti i campi obbligatori devono essere compilati');
      }

      // Assicurati che l'end_time sia calcolato
      let endTime = formData.endTime;
      if (!endTime || endTime.trim() === '') {
        endTime = calculateEndTime(formData.startTime, formData.treatmentId);
        if (!endTime) {
          throw new Error('Impossibile calcolare l\'ora di fine. Verifica che il trattamento e l\'ora di inizio siano validi.');
        }
      }
      
      const appointmentData = {
        patient_id: formData.patientId,
        doctor_id: parseInt(formData.doctorId.toString()),
        treatment_id: parseInt(formData.treatmentId.toString()),
        date: formData.date,
        start_time: formData.startTime,
        end_time: endTime,
        status: formData.status,
        notes: formData.notes
      } as any;

      console.log('Appointment data to save:', appointmentData);

      if (appointment) {
        // Aggiorna usando lo store
        await update(appointment.id, appointmentData);
        showSuccess('Appuntamento aggiornato con successo');
      } else {
        // Crea usando lo store
        const newAppointment = await create(appointmentData);
        console.log('Created appointment:', newAppointment);
        showSuccess('Appuntamento creato con successo');
      }

      onClose();
    } catch (error) {
      console.error('Error saving appointment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Errore durante il salvataggio dell\'appuntamento';
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="lg">
        <Modal.Header>
          {appointment ? 'Modifica Appuntamento' : 'Nuovo Appuntamento'}
        </Modal.Header>
        <Modal.Body>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Campo Paziente con Autocompletamento */}
            <div className="relative">
              <div className="flex justify-between items-center mb-2">
                <Label htmlFor="patientSearch" value="Paziente *" />
                <Button
                  color="light"
                  size="xs"
                  type="button"
                  onClick={handleOpenQuickPatientModal}
                  className="flex items-center gap-1 py-1"
                >
                  <Icon icon="solar:add-circle-outline" height={16} />
                  <span className="text-xs">Nuovo Paziente</span>
                </Button>
              </div>
              <TextInput
                id="patientSearch"
                name="patientSearch"
                value={patientSearch}
                onChange={handlePatientSearch}
                onBlur={handlePatientInputBlur}
                onFocus={() => patientSearch.length > 0 && setShowPatientSuggestions(true)}
                placeholder="Digita il nome, cognome, codice fiscale o telefono..."
                autoComplete="off"
                required
              />
              {showPatientSuggestions && filteredPatients.length > 0 && (
                <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
                  {filteredPatients.map(patient => (
                    <div
                      key={patient.id}
                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                      onClick={() => handleSelectPatient(patient)}
                    >
                      <div className="font-medium">{patient.first_name} {patient.last_name}</div>
                      <div className="text-sm text-gray-500">
                        {patient.fiscal_code && `CF: ${patient.fiscal_code} • `}
                        Tel: {patient.phone}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="doctorId" value="Dottore *" />
              <Select
                id="doctorId"
                name="doctorId"
                value={formData.doctorId}
                onChange={handleChange}
                required
              >
                <option value={0}>Seleziona un dottore</option>
                {doctors.map(doctor => (
                  <option key={doctor.id} value={doctor.id}>
                    {doctor.first_name} {doctor.last_name}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <Label htmlFor="treatmentId" value="Trattamento *" />
              <Select
                id="treatmentId"
                name="treatmentId"
                value={formData.treatmentId}
                onChange={handleChange}
                required
              >
                <option value={0}>Seleziona un trattamento</option>
                {treatments.map(treatment => (
                  <option key={treatment.id} value={treatment.id}>
                    {treatment.name} - €{treatment.price} ({treatment.duration} min)
                  </option>
                ))}
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="date" value="Data *" />
                <TextInput
                  id="date"
                  name="date"
                  type="date"
                  value={formData.date}
                  onChange={handleChange}
                  required
                />
              </div>
              <div>
                <Label htmlFor="startTime" value="Ora di inizio *" />
                <TextInput
                  id="startTime"
                  name="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="endTime" value="Ora di fine (automatica)" />
                <TextInput
                  id="endTime"
                  name="endTime"
                  type="time"
                  value={formData.endTime}
                  readOnly
                />
              </div>
              <div>
                <Label htmlFor="status" value="Stato" />
                <Select
                  id="status"
                  name="status"
                  value={formData.status || 'confermato'}
                  onChange={handleChange}
                >
                  <option value="confermato">Confermato</option>
                  <option value="in attesa">In Attesa</option>
                  <option value="cancellato">Cancellato</option>
                  <option value="completato">Completato</option>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="notes" value="Note" />
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                placeholder="Note aggiuntive sull'appuntamento..."
              />
            </div>
          </form>
        </Modal.Body>
        <Modal.Footer>
          <div className="flex justify-between w-full">
            <Button color="gray" onClick={onClose} disabled={loading}>
              Annulla
            </Button>
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? (
                <>
                  <Icon icon="eos-icons:loading" className="mr-2" />
                  Salvataggio...
                </>
              ) : (
                appointment ? 'Modifica Appuntamento' : 'Crea Appuntamento'
              )}
            </Button>
          </div>
        </Modal.Footer>
      </Modal>

      {/* QuickPatientModal */}
      <QuickPatientModal
        isOpen={isQuickPatientModalOpen}
        onClose={handleCloseQuickPatientModal}
        onPatientAdded={handlePatientAdded}
      />
    </>
  );
};

export default AppointmentModal;
