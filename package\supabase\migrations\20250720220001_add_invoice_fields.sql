-- Add new fields to invoices table and create invoice_items table
-- Migration: 20250720220001_add_invoice_fields

-- Add new columns to invoices table
ALTER TABLE public.invoices 
ADD COLUMN IF NOT EXISTS clinic_id UUID,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS sal NUMERIC(5,2),
ADD COLUMN IF NOT EXISTS rate_number INTEGER;

-- Update existing columns that need modification
ALTER TABLE public.invoices 
ALTER COLUMN payment_date SET DATA TYPE DATE,
ALTER COLUMN payment_method SET DATA TYPE VARCHAR(50),
ALTER COLUMN status SET DEFAULT 'draft',
ALTER COLUMN subtotal SET DATA TYPE NUMERIC(10,2),
ALTER COLUMN tax_rate SET DATA TYPE NUMERIC(5,2),
ALTER COLUMN tax_rate SET DEFAULT 22.00,
ALTER COLUMN tax_amount SET DATA TYPE NUMERIC(10,2);

-- Create invoice_items table
CREATE TABLE IF NOT EXISTS public.invoice_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
    treatment_id INTEGER NOT NULL REFERENCES public.treatments(id) ON DELETE RESTRICT,
    quantity NUMERIC(10,2) NOT NULL DEFAULT 1,
    unit_price NUMERIC(10,2) NOT NULL,
    subtotal NUMERIC(10,2) NOT NULL,
    iva NUMERIC(5,2) NOT NULL DEFAULT 22.00,
    total NUMERIC(10,2) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_invoices_clinic_issue_date ON public.invoices(clinic_id, issue_date);
CREATE INDEX IF NOT EXISTS idx_invoices_not_deleted ON public.invoices(deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice ON public.invoice_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_items_treatment ON public.invoice_items(treatment_id);

-- Add updated_at trigger for invoice_items
DROP TRIGGER IF EXISTS update_invoice_items_updated_at ON public.invoice_items;
CREATE TRIGGER update_invoice_items_updated_at 
    BEFORE UPDATE ON public.invoice_items 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
