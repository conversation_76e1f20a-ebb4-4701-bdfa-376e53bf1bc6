-- Configurazione email di benvenuto
-- Aggiorna il trigger esistente per includere l'invio dell'email di benvenuto

-- Modifica il trigger esistente per includere logging dell'email di benvenuto
CREATE OR REPLACE FUNCTION handle_new_user_registration()
RETURNS TRIGGER AS $$
DECLARE
  new_clinic_id uuid;
  clinic_name_val text;
  clinic_vat_val text;
  user_first_name text;
  user_last_name text;
BEGIN
  -- Log dettagliato per debugging
  RAISE LOG 'TRIGGER START: User % with metadata: %', NEW.email, NEW.raw_user_meta_data;

  -- Controlla se questa è una registrazione clinica
  IF NEW.raw_user_meta_data IS NOT NULL AND NEW.raw_user_meta_data ? 'clinic_name' THEN

    -- Estrai i valori dai metadata
    clinic_name_val := COALESCE(NEW.raw_user_meta_data->>'clinic_name', 'Clinica Senza Nome');
    clinic_vat_val := COALESCE(NEW.raw_user_meta_data->>'clinic_vat_number', '00000000000');
    user_first_name := COALESCE(NEW.raw_user_meta_data->>'first_name', 'Nome');
    user_last_name := COALESCE(NEW.raw_user_meta_data->>'last_name', 'Cognome');

    RAISE LOG 'EXTRACTED VALUES: clinic=%, vat=%, user=%,%', clinic_name_val, clinic_vat_val, user_first_name, user_last_name;

    -- Inserisci nuova clinica
    BEGIN
      INSERT INTO public.clinics (
        name, vat_number, address, city, postal_code, province, phone, email, description
      ) VALUES (
        clinic_name_val,
        clinic_vat_val,
        COALESCE(NEW.raw_user_meta_data->>'clinic_address', ''),
        COALESCE(NEW.raw_user_meta_data->>'clinic_city', ''),
        COALESCE(NEW.raw_user_meta_data->>'clinic_postal_code', ''),
        COALESCE(NEW.raw_user_meta_data->>'clinic_province', ''),
        COALESCE(NEW.raw_user_meta_data->>'clinic_phone', ''),
        COALESCE(NEW.raw_user_meta_data->>'clinic_email', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'clinic_description', '')
      ) RETURNING id INTO new_clinic_id;

      RAISE LOG 'CLINIC CREATED: ID = %', new_clinic_id;

    EXCEPTION WHEN OTHERS THEN
      RAISE LOG 'CLINIC ERROR: %', SQLERRM;
      RETURN NEW;
    END;

    -- Inserisci record utente
    IF new_clinic_id IS NOT NULL THEN
      RAISE LOG 'ATTEMPTING USER INSERT: id=%, email=%, first=%, last=%, clinic_id=%',
                NEW.id, NEW.email, user_first_name, user_last_name, new_clinic_id;

      BEGIN
        INSERT INTO public.users (
          id, email, first_name, last_name, role, clinic_id
        ) VALUES (
          NEW.id, NEW.email, user_first_name, user_last_name, 'manager', new_clinic_id
        );

        RAISE LOG 'USER RECORD CREATED SUCCESSFULLY for %', NEW.email;

        -- Log per email di benvenuto (Supabase gestisce automaticamente l'invio)
        RAISE LOG 'WELCOME EMAIL: Should be sent automatically by Supabase to %', NEW.email;

      EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'USER INSERT ERROR: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
      END;
    ELSE
      RAISE LOG 'SKIPPING USER INSERT: clinic_id is NULL';
    END IF;

  ELSE
    RAISE LOG 'NO CLINIC DATA: metadata is % or missing clinic_name', NEW.raw_user_meta_data;
  END IF;

  RETURN NEW;

EXCEPTION WHEN OTHERS THEN
  RAISE LOG 'TRIGGER FATAL ERROR: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Il trigger è già esistente, non serve ricrearlo
-- L'email di benvenuto viene inviata automaticamente da Supabase
-- quando l'utente si registra (email di conferma con template personalizzato)

-- Log dell'operazione
DO $$
BEGIN
  RAISE NOTICE 'Email di benvenuto configurata - template in supabase/templates/welcome.html';
  RAISE NOTICE 'Supabase invierà automaticamente email di conferma con template personalizzato';
END $$;