import { But<PERSON>, Label, TextInput, Alert } from "flowbite-react";
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { supabase } from "../../../lib/supabase";
import FullLogo from "src/layouts/full/shared/logo/FullLogo";

const gradientStyle = {
  background: "linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",
  backgroundSize: "400% 400%",
  animation: "gradient 15s ease infinite",
  minHeight: "100vh",
};

/**
 * Pagina per il recupero password
 * Invia email di reset tramite Supabase Auth
 */
const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log('🔄 [ForgotPassword] Starting password reset...');
    
    if (!email.trim()) {
      setError('Email è obbligatoria');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Invia email di reset password
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (resetError) {
        throw resetError;
      }

      console.log('✅ [ForgotPassword] Reset email sent successfully');
      setIsSuccess(true);
      
    } catch (error: any) {
      console.error('❌ [ForgotPassword] Reset failed:', error);
      
      if (error.message?.includes('User not found')) {
        setError('Email non trovata. Verifica di aver inserito l\'email corretta.');
      } else if (error.message?.includes('Email rate limit exceeded')) {
        setError('Troppi tentativi. Riprova tra qualche minuto.');
      } else {
        setError(error.message || 'Errore durante l\'invio dell\'email. Riprova.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div style={gradientStyle} className="relative min-h-screen py-8">
        <div className="flex justify-center items-center px-4 min-h-screen">
          <div className="rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full max-w-md border-none">
            <div className="text-center">
              <div className="mx-auto mb-4">
                <FullLogo />
              </div>
              
              <Alert color="success" className="mb-4">
                <span className="font-medium">Email inviata!</span>
                <br />
                Controlla la tua casella di posta per il link di reset password.
              </Alert>

              <div className="space-y-4">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Se non ricevi l'email entro qualche minuto, controlla la cartella spam.
                </p>
                
                <Link
                  to="/auth/login"
                  className="inline-block text-primary hover:underline"
                >
                  ← Torna al login
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={gradientStyle} className="relative min-h-screen py-8">
      <div className="flex justify-center items-center px-4 min-h-screen">
        <div className="rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full max-w-md border-none">
          <div className="flex flex-col gap-4">
            <div className="mx-auto">
              <FullLogo />
            </div>
            
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Recupera Password
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Inserisci la tua email per ricevere il link di reset password
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert color="failure">
                  <span className="font-medium">Errore!</span> {error}
                </Alert>
              )}

              <div>
                <Label htmlFor="email" value="Email" />
                <TextInput
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                  className="form-control form-rounded-xl"
                />
              </div>

              <Button 
                type="submit" 
                color="primary" 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Invio in corso...
                  </>
                ) : (
                  'Invia Link di Reset'
                )}
              </Button>

              <div className="text-center">
                <Link
                  to="/auth/login"
                  className="text-sm text-primary hover:underline"
                >
                  ← Torna al login
                </Link>
              </div>

              <div className="border-t pt-4">
                <p className="text-xs text-gray-500 text-center">
                  🔒 Riceverai un link sicuro per reimpostare la password.
                  <br />
                  Il link scadrà dopo 1 ora per sicurezza.
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
