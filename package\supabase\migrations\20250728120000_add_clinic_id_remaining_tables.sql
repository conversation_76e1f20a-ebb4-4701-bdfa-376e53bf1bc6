-- Migration: Add clinic_id to remaining tables for multi-tenant isolation
-- Created: 2025-07-28 12:00:00
-- Author: AI Assistant

-- Add clinic_id column to patients table
ALTER TABLE public.patients
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for patients clinic isolation
CREATE INDEX IF NOT EXISTS idx_patients_clinic
  ON public.patients(clinic_id)
  WHERE clinic_id IS NOT NULL;

-- Update existing patients with demo clinic
UPDATE public.patients
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to doctors table
ALTER TABLE public.doctors
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for doctors clinic isolation
CREATE INDEX IF NOT EXISTS idx_doctors_clinic
  ON public.doctors(clinic_id)
  WHERE deleted_at IS NULL;

-- Update existing doctors with demo clinic
UPDATE public.doctors
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to treatments table
ALTER TABLE public.treatments
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for treatments clinic isolation
CREATE INDEX IF NOT EXISTS idx_treatments_clinic
  ON public.treatments(clinic_id);

-- Update existing treatments with demo clinic
UPDATE public.treatments
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to reminders table
ALTER TABLE public.reminders
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for reminders clinic isolation
CREATE INDEX IF NOT EXISTS idx_reminders_clinic
  ON public.reminders(clinic_id);

-- Update existing reminders with demo clinic
UPDATE public.reminders
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to patient_files table
ALTER TABLE public.patient_files
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for patient_files clinic isolation
CREATE INDEX IF NOT EXISTS idx_patient_files_clinic
  ON public.patient_files(clinic_id)
  WHERE deleted_at IS NULL;

-- Update existing patient_files with demo clinic
UPDATE public.patient_files
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.patients.clinic_id IS 'FK to clinics table - isolates patient data per clinic';
COMMENT ON COLUMN public.doctors.clinic_id IS 'FK to clinics table - isolates doctor data per clinic';
COMMENT ON COLUMN public.treatments.clinic_id IS 'FK to clinics table - isolates treatment catalog per clinic';
COMMENT ON COLUMN public.reminders.clinic_id IS 'FK to clinics table - isolates reminders per clinic';
COMMENT ON COLUMN public.patient_files.clinic_id IS 'FK to clinics table - isolates patient files per clinic';
