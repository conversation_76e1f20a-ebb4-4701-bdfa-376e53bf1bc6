/**
 * Test funzionale per verificare l'isolamento delle cliniche con dati reali
 * Questo script crea dati di test e verifica che l'isolamento funzioni correttamente
 */

const { createClient } = require('@supabase/supabase-js');

// Configurazione Supabase (usa le variabili d'ambiente o valori di default per test)
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

console.log('🏥 === TEST FUNZIONALE ISOLAMENTO CLINICHE ===');
console.log('Questo test crea dati di test e verifica l\'isolamento reale\n');

// ID delle cliniche di test
const DEMO_CLINIC_ID = '00000000-0000-0000-0000-000000000000';
const TEST_CLINIC_A_ID = '11111111-1111-1111-1111-111111111111';
const TEST_CLINIC_B_ID = '22222222-2222-2222-2222-222222222222';

/**
 * Test di base per verificare la connessione al database
 */
async function testDatabaseConnection() {
  console.log('🔌 Testando connessione al database...');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test semplice per verificare la connessione
    const { data, error } = await supabase
      .from('clinics')
      .select('id')
      .limit(1);
    
    if (error) {
      console.log('❌ Errore di connessione:', error.message);
      console.log('💡 Assicurati che Supabase sia in esecuzione localmente');
      console.log('💡 Comando: npx supabase start');
      return false;
    }
    
    console.log('✅ Connessione al database riuscita!');
    return true;
    
  } catch (error) {
    console.log('❌ Errore di connessione:', error.message);
    console.log('💡 Verifica che le variabili d\'ambiente siano configurate correttamente');
    return false;
  }
}

/**
 * Verifica che le cliniche di test esistano
 */
async function ensureTestClinicsExist() {
  console.log('\n🏥 Verificando esistenza cliniche di test...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  const clinicsToCheck = [
    { id: DEMO_CLINIC_ID, name: 'Clinica Demo' },
    { id: TEST_CLINIC_A_ID, name: 'Clinica Test A' },
    { id: TEST_CLINIC_B_ID, name: 'Clinica Test B' }
  ];
  
  for (const clinic of clinicsToCheck) {
    try {
      // Verifica se la clinica esiste
      const { data: existing, error: selectError } = await supabase
        .from('clinics')
        .select('id')
        .eq('id', clinic.id)
        .single();
      
      if (selectError && selectError.code === 'PGRST116') {
        // Clinica non esiste, creala
        console.log(`📝 Creando clinica: ${clinic.name} (${clinic.id})`);
        
        const { error: insertError } = await supabase
          .from('clinics')
          .insert({
            id: clinic.id,
            name: clinic.name,
            address: 'Via Test 123',
            phone: '123456789',
            email: `test@${clinic.name.toLowerCase().replace(' ', '')}.com`
          });
        
        if (insertError) {
          console.log(`❌ Errore nella creazione di ${clinic.name}:`, insertError.message);
        } else {
          console.log(`✅ Clinica ${clinic.name} creata con successo`);
        }
      } else if (existing) {
        console.log(`✅ Clinica ${clinic.name} già esistente`);
      } else if (selectError) {
        console.log(`❌ Errore nel verificare ${clinic.name}:`, selectError.message);
      }
      
    } catch (error) {
      console.log(`❌ Errore con clinica ${clinic.name}:`, error.message);
    }
  }
}

/**
 * Test di isolamento con query dirette al database
 */
async function testDirectDatabaseIsolation() {
  console.log('\n🔍 Testando isolamento con query dirette...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  // Test 1: Verifica pazienti per clinica
  console.log('\n📋 Test pazienti per clinica:');
  
  const clinics = [DEMO_CLINIC_ID, TEST_CLINIC_A_ID, TEST_CLINIC_B_ID];
  
  for (const clinicId of clinics) {
    try {
      const { data: patients, error } = await supabase
        .from('patients')
        .select('id, first_name, last_name, clinic_id')
        .eq('clinic_id', clinicId);
      
      if (error) {
        console.log(`❌ Errore nel recuperare pazienti per clinica ${clinicId}:`, error.message);
      } else {
        console.log(`  Clinica ${clinicId}: ${patients.length} pazienti`);
        
        // Verifica che tutti i pazienti appartengano alla clinica corretta
        const wrongClinicPatients = patients.filter(p => p.clinic_id !== clinicId);
        if (wrongClinicPatients.length > 0) {
          console.log(`  ⚠️  PROBLEMA: ${wrongClinicPatients.length} pazienti con clinic_id errato!`);
        } else {
          console.log(`  ✅ Tutti i pazienti hanno clinic_id corretto`);
        }
      }
    } catch (error) {
      console.log(`❌ Errore nel test pazienti:`, error.message);
    }
  }
  
  // Test 2: Verifica dottori per clinica
  console.log('\n👨‍⚕️ Test dottori per clinica:');
  
  for (const clinicId of clinics) {
    try {
      const { data: doctors, error } = await supabase
        .from('doctors')
        .select('id, first_name, last_name, clinic_id')
        .eq('clinic_id', clinicId);
      
      if (error) {
        console.log(`❌ Errore nel recuperare dottori per clinica ${clinicId}:`, error.message);
      } else {
        console.log(`  Clinica ${clinicId}: ${doctors.length} dottori`);
        
        // Verifica che tutti i dottori appartengano alla clinica corretta
        const wrongClinicDoctors = doctors.filter(d => d.clinic_id !== clinicId);
        if (wrongClinicDoctors.length > 0) {
          console.log(`  ⚠️  PROBLEMA: ${wrongClinicDoctors.length} dottori con clinic_id errato!`);
        } else {
          console.log(`  ✅ Tutti i dottori hanno clinic_id corretto`);
        }
      }
    } catch (error) {
      console.log(`❌ Errore nel test dottori:`, error.message);
    }
  }
  
  // Test 3: Verifica appuntamenti per clinica
  console.log('\n📅 Test appuntamenti per clinica:');
  
  for (const clinicId of clinics) {
    try {
      const { data: appointments, error } = await supabase
        .from('appointments')
        .select('id, date, clinic_id')
        .eq('clinic_id', clinicId);
      
      if (error) {
        console.log(`❌ Errore nel recuperare appuntamenti per clinica ${clinicId}:`, error.message);
      } else {
        console.log(`  Clinica ${clinicId}: ${appointments.length} appuntamenti`);
        
        // Verifica che tutti gli appuntamenti appartengano alla clinica corretta
        const wrongClinicAppointments = appointments.filter(a => a.clinic_id !== clinicId);
        if (wrongClinicAppointments.length > 0) {
          console.log(`  ⚠️  PROBLEMA: ${wrongClinicAppointments.length} appuntamenti con clinic_id errato!`);
        } else {
          console.log(`  ✅ Tutti gli appuntamenti hanno clinic_id corretto`);
        }
      }
    } catch (error) {
      console.log(`❌ Errore nel test appuntamenti:`, error.message);
    }
  }
}

/**
 * Test di verifica che non ci siano leak di dati tra cliniche
 */
async function testDataLeakage() {
  console.log('\n🔒 Test verifica leak di dati...');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  // Verifica che non ci siano record senza clinic_id
  const tablesToCheck = ['patients', 'doctors', 'appointments', 'treatments', 'invoices'];
  
  for (const table of tablesToCheck) {
    try {
      const { data: recordsWithoutClinic, error } = await supabase
        .from(table)
        .select('id')
        .is('clinic_id', null);
      
      if (error) {
        console.log(`❌ Errore nel verificare ${table}:`, error.message);
      } else {
        if (recordsWithoutClinic.length > 0) {
          console.log(`⚠️  PROBLEMA: ${table} ha ${recordsWithoutClinic.length} record senza clinic_id!`);
        } else {
          console.log(`✅ ${table}: tutti i record hanno clinic_id`);
        }
      }
    } catch (error) {
      console.log(`❌ Errore nel test ${table}:`, error.message);
    }
  }
}

/**
 * Funzione principale per eseguire tutti i test
 */
async function runFunctionalTests() {
  console.log('🚀 Iniziando test funzionale isolamento cliniche...\n');
  
  // Test 1: Connessione database
  const isConnected = await testDatabaseConnection();
  if (!isConnected) {
    console.log('\n❌ Test interrotto: impossibile connettersi al database');
    return;
  }
  
  // Test 2: Verifica/crea cliniche di test
  await ensureTestClinicsExist();
  
  // Test 3: Test isolamento diretto
  await testDirectDatabaseIsolation();
  
  // Test 4: Test leak di dati
  await testDataLeakage();
  
  console.log('\n🏁 === TEST FUNZIONALE COMPLETATO ===');
  console.log('Se non ci sono errori o problemi segnalati, l\'isolamento funziona correttamente!');
  console.log('\n💡 Suggerimenti per test aggiuntivi:');
  console.log('1. Avvia l\'applicazione e verifica che ogni utente veda solo i dati della sua clinica');
  console.log('2. Prova a cambiare clinic_id nell\'autenticazione e verifica che i dati cambino');
  console.log('3. Verifica che le policy RLS impediscano l\'accesso a dati di altre cliniche');
}

// Esegui i test
runFunctionalTests().catch(error => {
  console.error('❌ Errore durante i test:', error);
  process.exit(1);
});
