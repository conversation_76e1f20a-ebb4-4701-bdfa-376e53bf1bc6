/**
 * MOKO SOSTANZA Dental CRM - Quick Status Dropdown
 * Dropdown veloce per cambiare stato fattura direttamente dalla lista
 */

import { Dropdown, But<PERSON>, Badge } from "flowbite-react";
import { Icon } from "@iconify/react";
import { Link } from "react-router-dom";
import { InvoiceStatusConfig, getInvoiceStatusBadgeClasses, formatInvoiceStatus } from "../../utils/invoiceStatusUtils";
import InvoiceService, { type InvoiceWithDetails } from "../../services/InvoiceService";

interface QuickStatusDropdownProps {
  invoice: InvoiceWithDetails | null;
  onStatusChanged: () => void;
  onOpenDetailedModal: () => void;
  onOpenEditModal?: () => void; // Opzionale per aprire modal edit
}

const QuickStatusDropdown = ({ invoice, onStatusChanged, onOpenDetailedModal, onOpenEditModal }: QuickStatusDropdownProps) => {
  
  if (!invoice) return null;

  // Azioni rapide più comuni (senza note obbligatorie)
  const getQuickActions = () => {
    const currentStatus = invoice.status || 'draft';
    
    const quickActions: Record<string, Array<{key: string, label: string, icon: string, color: string}>> = {
      draft: [
        { key: 'sent', label: 'Marca come Inviata', icon: 'solar:send-twice-outline', color: 'info' }
      ],
      sent: [
        { key: 'paid', label: 'Marca come Pagata', icon: 'solar:check-circle-outline', color: 'success' }
      ],
      overdue: [
        { key: 'paid', label: 'Marca come Pagata', icon: 'solar:check-circle-outline', color: 'success' }
      ]
    };

    return quickActions[currentStatus] || [];
  };

  const handleQuickStatusChange = async (newStatus: string) => {
    if (!invoice) return;
    
    try {
      console.log(`🔄 [QuickStatusDropdown] Quick status change for invoice ${invoice.id}: ${invoice.status} → ${newStatus}`);
      
      // Se il nuovo stato è "paid", usa markAsPaid
      if (newStatus === 'paid') {
        const today = new Date().toISOString().split('T')[0];
        console.log(`💰 [QuickStatusDropdown] Marking invoice ${invoice.id} as paid`);
        await InvoiceService.markAsPaid(invoice.id, today, 'cash');
      } else {
        // Altrimenti usa updateStatus
        console.log(`📝 [QuickStatusDropdown] Updating invoice ${invoice.id} status to ${newStatus}`);
        await InvoiceService.updateStatus(invoice.id, newStatus);
      }
      
      console.log(`✅ [QuickStatusDropdown] Quick status change completed successfully: ${invoice.status} → ${newStatus}`);
      
      // Forza il reload dell'interfaccia
      onStatusChanged();
    } catch (error: any) {
      const errorMsg = error?.message || 'Errore sconosciuto';
      console.error(`❌ [QuickStatusDropdown] Quick status change failed for invoice ${invoice.id}:`, {
        error: error,
        message: errorMsg,
        fromStatus: invoice.status,
        toStatus: newStatus,
        invoiceId: invoice.id,
        stack: error?.stack
      });
      
      // Show user-friendly error (could be enhanced with toast notification)
      alert(`Errore: ${errorMsg}`);
      // UI state rollback: dropdown closes automatically, status remains unchanged
    }
  };

  const quickActions = getQuickActions();
  const hasQuickActions = quickActions.length > 0;

  return (
    <Dropdown
      label=""
      dismissOnClick={true}
      renderTrigger={() => (
        <Button size="xs" color="light" className="p-1.5">
          <Icon icon="solar:menu-dots-outline" height={16} />
        </Button>
      )}
    >
      {/* Stato Attuale (Header) */}
      <Dropdown.Header className="py-2">
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">Stato attuale:</span>
          <Badge className={`${getInvoiceStatusBadgeClasses(invoice.status || '')} text-xs`} size="sm">
            {formatInvoiceStatus(invoice.status || '')}
          </Badge>
        </div>
      </Dropdown.Header>

      {/* Azioni Rapide */}
      {hasQuickActions && (
        <>
          <Dropdown.Header className="py-1 border-b">
            <span className="text-xs font-medium text-gray-600">Azioni Rapide</span>
          </Dropdown.Header>
          
          {quickActions.map((action) => (
            <Dropdown.Item
              key={action.key}
              onClick={() => handleQuickStatusChange(action.key)}
              className="flex items-center gap-2 hover:bg-gray-50"
            >
              <Icon 
                icon={action.icon} 
                className={`text-${action.color}-600`}
                height={16} 
              />
              <span className="text-sm">{action.label}</span>
            </Dropdown.Item>
          ))}
        </>
      )}

      {/* Sempre presente: Modifica Avanzata */}
      {hasQuickActions && <Dropdown.Divider />}
      
      <Dropdown.Item
        onClick={onOpenDetailedModal}
        className="flex items-center gap-2 hover:bg-gray-50"
      >
        <Icon icon="solar:settings-outline" className="text-gray-600" height={16} />
        <span className="text-sm">Cambia Stato Avanzato...</span>
      </Dropdown.Item>

      {/* Visualizza / Modifica */}
      <Dropdown.Divider />
      <Link to={`/billing/invoices/${invoice.id}`} className="block">
        <Dropdown.Item className="flex items-center gap-2 hover:bg-gray-50">
          <Icon icon="solar:eye-outline" className="text-gray-600" height={16} />
          <span className="text-sm">Visualizza Dettagli</span>
        </Dropdown.Item>
      </Link>
      
      <Dropdown.Item
        onClick={() => onOpenEditModal ? onOpenEditModal() : console.log('Edit modal not implemented')}
        className="flex items-center gap-2 hover:bg-gray-50"
      >
        <Icon icon="solar:pen-outline" className="text-gray-600" height={16} />
        <span className="text-sm">Modifica Fattura</span>
      </Dropdown.Item>
    </Dropdown>
  );
};

export default QuickStatusDropdown;
