/**
 * Crea la clinica demo mancante
 * Risolve il problema dell'Edge Function che non trova la clinica
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🏥 === CREAZIONE CLINICA DEMO ===\n');

async function createDemoClinic() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('1️⃣ Login come utente amministratore...');
    
    // Login con utente esistente
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123456'
    });
    
    if (signInError) {
      console.log('❌ Errore login:', signInError.message);
      return;
    }
    
    console.log('✅ Login riuscito:', signInData.user.email);
    
    console.log('\n2️⃣ Verifica se clinica demo esiste...');
    
    const demoClinicId = '00000000-0000-0000-0000-000000000000';
    
    const { data: existingClinic, error: checkError } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', demoClinicId)
      .maybeSingle();
    
    if (checkError) {
      console.log('❌ Errore verifica clinica:', checkError.message);
      return;
    }
    
    if (existingClinic) {
      console.log('✅ Clinica demo già esiste:');
      console.log(`  - ID: ${existingClinic.id}`);
      console.log(`  - Nome: ${existingClinic.name}`);
      console.log(`  - P.IVA: ${existingClinic.vat_number}`);
      return;
    }
    
    console.log('⚠️ Clinica demo non trovata, creazione in corso...');
    
    console.log('\n3️⃣ Creazione clinica demo...');
    
    const demoClinicData = {
      id: demoClinicId,
      name: 'Demo Clinic',
      vat_number: '12345678901',
      address: 'Via Demo 123',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      phone: '+39 02 1234567',
      email: '<EMAIL>',
      website: 'https://democlinic.it',
      description: 'Clinica demo per testing e sviluppo'
    };
    
    const { data: newClinic, error: createError } = await supabase
      .from('clinics')
      .insert(demoClinicData)
      .select()
      .single();
    
    if (createError) {
      console.log('❌ Errore creazione clinica:', createError.message);
      console.log('📋 Dettagli:', createError);
      
      if (createError.code === '42501') {
        console.log('⚠️ Errore RLS - potrebbe essere necessario disabilitare temporaneamente RLS per clinics');
      }
    } else {
      console.log('🎉 CLINICA DEMO CREATA CON SUCCESSO!');
      console.log(`  - ID: ${newClinic.id}`);
      console.log(`  - Nome: ${newClinic.name}`);
      console.log(`  - P.IVA: ${newClinic.vat_number}`);
      console.log(`  - Città: ${newClinic.city}`);
    }
    
    console.log('\n4️⃣ Test Edge Function dopo creazione clinica...');
    
    // Test Edge Function con un utente temporaneo
    const { data: { user } } = await supabase.auth.getUser();
    
    const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
      body: { user: user }
    });
    
    if (functionError) {
      console.log('❌ Edge Function ancora fallisce:', functionError.message);
    } else {
      console.log('✅ Edge Function ora funziona:', functionResult);
    }
    
    console.log('\n5️⃣ Verifica record utente creato...');
    
    const { data: userRecord, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();
    
    if (userError) {
      console.log('❌ Errore verifica utente:', userError.message);
    } else if (userRecord) {
      console.log('✅ Record utente trovato:');
      console.log(`  - ID: ${userRecord.id}`);
      console.log(`  - Email: ${userRecord.email}`);
      console.log(`  - Clinic ID: ${userRecord.clinic_id}`);
      console.log(`  - Role: ${userRecord.role}`);
    } else {
      console.log('⚠️ Record utente non trovato');
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

createDemoClinic().then(() => {
  console.log('\n🏁 Creazione clinica demo completata');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
