/**
 * MOKO SOSTANZA Dental CRM - Invoice Service
 *
 * Servizio per la gestione delle fatture
 * Integrato con Supabase database
 */

import { supabase } from '../lib/supabase';
import type { Database } from '../types/database';
import { withClinicId, getClinicId } from './ServiceUtils';

// Tipi dal database
type Invoice = Database['public']['Tables']['invoices']['Row'];
type InvoiceInsert = Database['public']['Tables']['invoices']['Insert'];
type InvoiceUpdate = Database['public']['Tables']['invoices']['Update'];
type Patient = Database['public']['Tables']['patients']['Row'];
type Doctor = Database['public']['Tables']['doctors']['Row'];
type Treatment = Database['public']['Tables']['treatments']['Row'];

// Tipo per invoice items
export interface InvoiceItem {
  id?: string;
  invoice_id?: string;
  treatment_id: number;
  quantity: number;
  unit_price: number;
  subtotal: number;
  iva: number;
  total: number;
  treatment?: Treatment;
}

// Tipo per fattura con dati correlati
export interface InvoiceWithDetails extends Invoice {
  patient?: Patient;
  doctor?: Doctor;
  invoice_items?: InvoiceItem[];
}

// Interfacce per le opzioni di ricerca e paginazione
export interface InvoiceSearchFilters {
  search?: string;
  patientId?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  amountFrom?: number;
  amountTo?: number;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface InvoiceListResult {
  invoices: InvoiceWithDetails[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export class InvoiceService {
  // Enum per gli stati delle fatture
  static readonly InvoiceStatus = {
    DRAFT: 'draft',
    SENT: 'sent', 
    PAID: 'paid',
    OVERDUE: 'overdue',
    CANCELLED: 'cancelled'
  } as const;

  // Mappa delle transizioni di stato consentite
  private static readonly ALLOWED_STATUS_TRANSITIONS: Record<string, string[]> = {
    'draft': ['sent', 'cancelled'],
    'sent': ['paid', 'overdue', 'cancelled'],
    'paid': [], // Le fatture pagate non possono cambiare stato
    'overdue': ['paid', 'cancelled'],
    'cancelled': ['draft'] // Possibilità di riattivare una fattura cancellata
  };

  /**
   * Aggiorna lo stato di una fattura con validazione delle transizioni
   */
  static async updateStatus(
    id: string, 
    newStatus: string,
    additionalData?: { payment_date?: string; payment_method?: string }
  ): Promise<InvoiceWithDetails> {
    try {
      // Ottieni lo stato attuale della fattura
      const { data: currentInvoice, error: fetchError } = await supabase
        .from('invoices')
        .select('status')
        .eq('id', id)
        .single();

      if (fetchError || !currentInvoice) {
        throw new Error('Fattura non trovata');
      }

      // Valida la transizione di stato
      const currentStatus = currentInvoice.status || 'draft';
      const allowedTransitions = this.ALLOWED_STATUS_TRANSITIONS[currentStatus] || [];
      if (!allowedTransitions.includes(newStatus)) {
        throw new Error(`Transizione di stato non consentita da "${currentStatus}" a "${newStatus}"`);
      }

      // Prepara i dati di aggiornamento
      const updateData: InvoiceUpdate = {
        status: newStatus as any,
        updated_at: new Date().toISOString(),
        ...additionalData
      };

      // Aggiorna la fattura
      const { data: invoice, error } = await supabase
        .from('invoices')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          patient:patients(*)
        `)
        .single();

      if (error) {
        throw new Error(`Errore nell'aggiornamento dello stato: ${error.message}`);
      }

      if (!invoice) {
        throw new Error('Fattura non trovata dopo l\'aggiornamento');
      }

      return invoice;
    } catch (error) {
      console.error('Errore in updateStatus:', error);
      throw error;
    }
  }

  /**
   * Ottieni tutte le fatture con filtri e paginazione
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getInvoices(
    filters: InvoiceSearchFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<InvoiceListResult> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();

      let query = supabase
        .from('invoices')
        .select(`
          *,
          patient:patients(*)
        `, { count: 'exact' })
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('deleted_at', null);

      // Applica filtri
      if (filters.patientId) {
        query = query.eq('patient_id', filters.patientId);
      }

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.dateFrom) {
        query = query.gte('issue_date', filters.dateFrom);
      }

      if (filters.dateTo) {
        query = query.lte('issue_date', filters.dateTo);
      }

      if (filters.amountFrom !== undefined) {
        query = query.gte('total', filters.amountFrom);
      }

      if (filters.amountTo !== undefined) {
        query = query.lte('total', filters.amountTo);
      }

      // Gestione della ricerca - approccio a due fasi per includere i nomi dei pazienti
      if (filters.search) {
        const searchTerm = filters.search.trim();
        
        // Prima: cerca i pazienti che corrispondono al termine di ricerca
        // Se il termine contiene uno spazio, potrebbe essere "Nome Cognome"
        const searchParts = searchTerm.split(' ').filter(part => part.length > 0);
        let patientSearchConditions: string[] = [];
        
        if (searchParts.length > 1) {
          // Ricerca per nome e cognome separati
          patientSearchConditions.push(`first_name.ilike.%${searchParts[0]}%`);
          patientSearchConditions.push(`last_name.ilike.%${searchParts[searchParts.length - 1]}%`);
        } else {
          // Ricerca singola in nome o cognome
          patientSearchConditions.push(`first_name.ilike.%${searchTerm}%`);
          patientSearchConditions.push(`last_name.ilike.%${searchTerm}%`);
        }
        
        const { data: matchingPatients, error: patientError } = await supabase
          .from('patients')
          .select('id, first_name, last_name')
          .or(patientSearchConditions.join(','));

        if (patientError) {
          console.warn('Warning: Error searching patients:', patientError);
        }

        // Costruisci la condizione di ricerca
        const searchConditions: string[] = [];
        
        // Cerca nei campi diretti della fattura
        searchConditions.push(`invoice_number.ilike.%${searchTerm}%`);
        searchConditions.push(`description.ilike.%${searchTerm}%`);
        
        // Se ci sono pazienti corrispondenti, aggiungi anche i loro ID
        if (matchingPatients && matchingPatients.length > 0) {
          const patientIds = matchingPatients.map(p => p.id);
          searchConditions.push(`patient_id.in.(${patientIds.join(',')})`);
        }
        
        // Applica la ricerca combinata
        query = query.or(searchConditions.join(','));
      }

      // Applica ordinamento
      const sortBy = pagination.sortBy || 'issue_date';
      const sortOrder = pagination.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Applica paginazione
      const page = pagination.page || 1;
      const limit = pagination.limit || 20;
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);

      const { data: invoices, error, count } = await query;

      if (error) {
        console.error('InvoiceService: Supabase query error:', error);
        throw new Error(`Errore nel recupero delle fatture: ${error.message}`);
      }

      const total = count || 0;
      const totalPages = Math.ceil(total / limit);

      const result = {
        invoices: invoices || [],
        pagination: {
          total,
          page,
          limit,
          totalPages
        }
      };
      
      return result;
    } catch (error) {
      console.error('Errore in getInvoices:', error);
      throw error;
    }
  }

  /**
   * Ottieni una fattura per ID con dettagli e items
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getInvoiceById(id: string): Promise<InvoiceWithDetails | null> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();

      const { data: invoice, error } = await supabase
        .from('invoices')
        .select(`
          *,
          patient:patients(*),
          invoice_items(
            *,
            treatment:treatments(*)
          )
        `)
        .eq('id', id)
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('deleted_at', null)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Fattura non trovata
        }
        throw new Error(`Errore nel recupero della fattura: ${error.message}`);
      }

      return invoice;
    } catch (error) {
      console.error('Errore in getInvoiceById:', error);
      throw error;
    }
  }

  /**
   * Crea una nuova fattura con items
   */
  static async createInvoiceWithItems(
    invoiceData: Omit<InvoiceInsert, 'clinic_id' | 'status'>, 
    items: Omit<InvoiceItem, 'id' | 'invoice_id'>[]
  ): Promise<InvoiceWithDetails> {
    try {
      // Genera numero fattura se non fornito
      if (!invoiceData.invoice_number) {
        const invoiceNumber = await this.generateInvoiceNumber();
        invoiceData.invoice_number = invoiceNumber;
      }

      // Inject clinic_id automatically and set default status
      const invoiceToCreate = await withClinicId({
        ...invoiceData,
        status: 'draft' as const
      });

      const { data: invoice, error } = await supabase
        .from('invoices')
        .insert(invoiceToCreate)
        .select(`
          *,
          patient:patients(*)
        `)
        .single();

      if (error) {
        throw new Error(`Errore nella creazione della fattura: ${error.message}`);
      }

      if (!invoice) {
        throw new Error('Fattura creata ma non restituita dal database');
      }

      // Inserisci gli items della fattura
      if (items.length > 0) {
        const itemsToInsert = items.map(item => {
          // Rimuovi il campo 'treatment' che è solo per la UI
          const { treatment, ...itemForDb } = item;
          return {
            ...itemForDb,
            invoice_id: invoice.id
          };
        });

        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(itemsToInsert);

        if (itemsError) {
          // Rollback: elimina la fattura creata
          await supabase.from('invoices').delete().eq('id', invoice.id);
          throw new Error(`Errore nell'inserimento degli items: ${itemsError.message}`);
        }
      }

      return invoice;
    } catch (error) {
      console.error('Errore in createInvoiceWithItems:', error);
      throw error;
    }
  }

  /**
   * Crea una nuova fattura (metodo legacy)
   */
  static async createInvoice(invoiceData: InvoiceInsert): Promise<InvoiceWithDetails> {
    try {
      // Genera numero fattura se non fornito
      if (!invoiceData.invoice_number) {
        const invoiceNumber = await this.generateInvoiceNumber();
        invoiceData.invoice_number = invoiceNumber;
      }

      // Inject clinic_id automatically if not present
      const invoiceToCreate = invoiceData.clinic_id 
        ? invoiceData 
        : await withClinicId(invoiceData);

      const { data: invoice, error } = await supabase
        .from('invoices')
        .insert(invoiceToCreate)
        .select(`
          *,
          patient:patients(*)
        `)
        .single();

      if (error) {
        throw new Error(`Errore nella creazione della fattura: ${error.message}`);
      }

      if (!invoice) {
        throw new Error('Fattura creata ma non restituita dal database');
      }

      return invoice;
    } catch (error) {
      console.error('Errore in createInvoice:', error);
      throw error;
    }
  }

  /**
   * Aggiorna una fattura esistente
   */
  static async updateInvoice(id: string, invoiceData: InvoiceUpdate): Promise<InvoiceWithDetails> {
    try {
      // Preserva clinic_id se non specificato
      const updateData = { ...invoiceData };
      if (!updateData.clinic_id) {
        const { data: currentInvoice } = await supabase
          .from('invoices')
          .select('clinic_id')
          .eq('id', id)
          .single();
        
        if (currentInvoice?.clinic_id) {
          updateData.clinic_id = currentInvoice.clinic_id;
        }
      }

      const { data: invoice, error } = await supabase
        .from('invoices')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          patient:patients(*)
        `)
        .single();

      if (error) {
        throw new Error(`Errore nell'aggiornamento della fattura: ${error.message}`);
      }

      if (!invoice) {
        throw new Error('Fattura non trovata o non aggiornata');
      }

      return invoice;
    } catch (error) {
      console.error('Errore in updateInvoice:', error);
      throw error;
    }
  }

  /**
   * Soft delete di una fattura
   */
  static async softDeleteInvoice(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('invoices')
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', id);

      if (error) {
        throw new Error(`Errore nel soft delete della fattura: ${error.message}`);
      }
    } catch (error) {
      console.error('Errore in softDeleteInvoice:', error);
      throw error;
    }
  }

  /**
   * Ripristina una fattura soft-deleted
   */
  static async restoreInvoice(id: string): Promise<InvoiceWithDetails> {
    try {
      const { data: invoice, error } = await supabase
        .from('invoices')
        .update({ deleted_at: null })
        .eq('id', id)
        .select(`
          *,
          patient:patients(*)
        `)
        .single();

      if (error) {
        throw new Error(`Errore nel ripristino della fattura: ${error.message}`);
      }

      if (!invoice) {
        throw new Error('Fattura non trovata');
      }

      return invoice;
    } catch (error) {
      console.error('Errore in restoreInvoice:', error);
      throw error;
    }
  }

  /**
   * Elimina una fattura
   */
  static async deleteInvoice(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', id);

      if (error) {
        throw new Error(`Errore nell'eliminazione della fattura: ${error.message}`);
      }
    } catch (error) {
      console.error('Errore in deleteInvoice:', error);
      throw error;
    }
  }

  /**
   * Marca una fattura come pagata utilizzando il sistema di transizioni di stato
   */
  static async markAsPaid(
    id: string, 
    paymentDate: string,
    paymentMethod: string
  ): Promise<InvoiceWithDetails> {
    try {
      return await this.updateStatus(id, this.InvoiceStatus.PAID, {
        payment_date: paymentDate,
        payment_method: paymentMethod
      });
    } catch (error) {
      console.error('Errore in markAsPaid:', error);
      throw error;
    }
  }

  /**
   * Ottieni statistiche delle fatture della clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getInvoiceStats(year?: number): Promise<{
    totalInvoices: number;
    paidInvoices: number;
    overdueInvoices: number;
    totalRevenue: number;
    pendingRevenue: number;
    paymentRate: number;
  }> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();
      const currentYear = year || new Date().getFullYear();
      const startOfYear = `${currentYear}-01-01`;
      const endOfYear = `${currentYear}-12-31`;

      // Ottieni tutte le fatture dell'anno per la clinica
      const { data: yearInvoices, error: yearError } = await supabase
        .from('invoices')
        .select('*')
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .gte('issue_date', startOfYear)
        .lte('issue_date', endOfYear)
        .is('deleted_at', null);

      if (yearError) {
        throw new Error(`Errore nel recupero delle fatture dell'anno: ${yearError.message}`);
      }

      // Ottieni fatture scadute
      const today = new Date().toISOString().split('T')[0];
      const { data: overdueInvoicesData, error: overdueError } = await supabase
        .from('invoices')
        .select('*')
        .in('status', ['sent', 'overdue'])
        .lt('due_date', today)
        .is('deleted_at', null);

      if (overdueError) {
        throw new Error(`Errore nel recupero delle fatture scadute: ${overdueError.message}`);
      }

      const totalInvoices = yearInvoices?.length || 0;
      const paidInvoices = yearInvoices?.filter(inv => inv.status === 'paid').length || 0;
      const overdueInvoices = overdueInvoicesData?.length || 0;
      
      const totalRevenue = yearInvoices
        ?.filter(inv => inv.status === 'paid')
        .reduce((sum, inv) => sum + (inv.total || 0), 0) || 0;
        
      const pendingRevenue = yearInvoices
        ?.filter(inv => ['draft', 'sent'].includes(inv.status || ''))
        .reduce((sum, inv) => sum + (inv.total || 0), 0) || 0;

      const paymentRate = totalInvoices > 0 ? (paidInvoices / totalInvoices) * 100 : 0;

      return {
        totalInvoices,
        paidInvoices,
        overdueInvoices,
        totalRevenue,
        pendingRevenue,
        paymentRate
      };
    } catch (error) {
      console.error('Errore in getInvoiceStats:', error);
      throw error;
    }
  }

  /**
   * Ottieni fatture in scadenza della clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getUpcomingDueInvoices(days: number = 7): Promise<InvoiceWithDetails[]> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();
      const today = new Date().toISOString().split('T')[0];
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + days);
      const futureDateStr = futureDate.toISOString().split('T')[0];

      const { data: invoices, error } = await supabase
        .from('invoices')
        .select(`
          *,
          patient:patients(*)
        `)
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .eq('status', 'sent')
        .gte('due_date', today)
        .lte('due_date', futureDateStr)
        .is('deleted_at', null)
        .order('due_date');

      if (error) {
        throw new Error(`Errore nel recupero delle fatture in scadenza: ${error.message}`);
      }

      return invoices || [];
    } catch (error) {
      console.error('Errore in getUpcomingDueInvoices:', error);
      throw error;
    }
  }

  /**
   * Ottieni fatture per paziente
   */
  static async getInvoicesByPatient(patientId: string): Promise<InvoiceWithDetails[]> {
    try {
      const { data: invoices, error } = await supabase
        .from('invoices')
        .select(`
          *,
          patient:patients(*)
        `)
        .eq('patient_id', patientId)
        .is('deleted_at', null)
        .order('issue_date', { ascending: false });

      if (error) {
        throw new Error(`Errore nel recupero delle fatture per paziente: ${error.message}`);
      }

      return invoices || [];
    } catch (error) {
      console.error('Errore in getInvoicesByPatient:', error);
      throw error;
    }
  }

  /**
   * Genera numero fattura progressivo per la clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async generateInvoiceNumber(): Promise<string> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();
      const currentYear = new Date().getFullYear();

      // Ottieni l'ultima fattura dell'anno corrente per la clinica
      const { data: lastInvoice, error } = await supabase
        .from('invoices')
        .select('invoice_number')
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .like('invoice_number', `INV-${currentYear}-%`)
        .is('deleted_at', null)
        .order('invoice_number', { ascending: false })
        .limit(1);

      if (error) {
        throw new Error(`Errore nella generazione del numero fattura: ${error.message}`);
      }

      let nextNumber = 1;
      
      if (lastInvoice && lastInvoice.length > 0) {
        const lastNumber = lastInvoice[0].invoice_number;
        const match = lastNumber?.match(/INV-\d{4}-(\d+)/);
        if (match) {
          nextNumber = parseInt(match[1]) + 1;
        }
      }

      return `INV-${currentYear}-${nextNumber.toString().padStart(3, '0')}`;
    } catch (error) {
      console.error('Errore in generateInvoiceNumber:', error);
      // Fallback: usa timestamp
      const timestamp = Date.now().toString().slice(-6);
      const currentYear = new Date().getFullYear();
      return `INV-${currentYear}-${timestamp}`;
    }
  }

  /**
   * Calcola totale fattura con tasse
   */
  static calculateInvoiceTotal(subtotal: number, taxRate: number = 22): {
    subtotal: number;
    taxRate: number;
    taxAmount: number;
    total: number;
  } {
    const taxAmount = (subtotal * taxRate) / 100;
    const total = subtotal + taxAmount;

    return {
      subtotal,
      taxRate,
      taxAmount: Math.round(taxAmount * 100) / 100,
      total: Math.round(total * 100) / 100
    };
  }

  /**
   * Ottieni ricavi per mese (per grafici)
   */
  static async getMonthlyRevenue(year: number = new Date().getFullYear()): Promise<Array<{
    month: number;
    revenue: number;
    invoiceCount: number;
  }>> {
    try {
      const { data: invoices, error } = await supabase
        .from('invoices')
        .select('issue_date, total, status')
        .gte('issue_date', `${year}-01-01`)
        .lte('issue_date', `${year}-12-31`)
        .eq('status', 'paid')
        .is('deleted_at', null);

      if (error) {
        throw new Error(`Errore nel recupero dei ricavi mensili: ${error.message}`);
      }

      // Raggruppa per mese
      const monthlyData: { [key: number]: { revenue: number; count: number } } = {};
      
      // Inizializza tutti i mesi
      for (let i = 1; i <= 12; i++) {
        monthlyData[i] = { revenue: 0, count: 0 };
      }

      invoices?.forEach(invoice => {
        const month = new Date(invoice.issue_date).getMonth() + 1;
        monthlyData[month].revenue += invoice.total || 0;
        monthlyData[month].count += 1;
      });

      return Object.entries(monthlyData).map(([month, data]) => ({
        month: parseInt(month),
        revenue: data.revenue,
        invoiceCount: data.count
      }));
    } catch (error) {
      console.error('Errore in getMonthlyRevenue:', error);
      throw error;
    }
  }
}

export default InvoiceService;