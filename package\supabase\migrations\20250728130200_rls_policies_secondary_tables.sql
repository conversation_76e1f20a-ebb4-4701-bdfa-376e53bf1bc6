-- Migration: <PERSON><PERSON> Policies for Secondary Tables
-- Created: 2025-07-28 13:02:00
-- Author: AI Assistant

-- Create clinic isolation policies for dental_procedures table (if exists)
CREATE POLICY "clinic_isolation_select" ON public.dental_procedures
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.dental_procedures
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.dental_procedures
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.dental_procedures
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for doctor_availability table (if exists)
CREATE POLICY "clinic_isolation_select" ON public.doctor_availability
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.doctor_availability
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.doctor_availability
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.doctor_availability
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for doctor_specialties table (if exists)
CREATE POLICY "clinic_isolation_select" ON public.doctor_specialties
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.doctor_specialties
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.doctor_specialties
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.doctor_specialties
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for event_attachments table (if exists)
CREATE POLICY "clinic_isolation_select" ON public.event_attachments
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.event_attachments
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.event_attachments
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.event_attachments
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for invoice_items table (if exists)
CREATE POLICY "clinic_isolation_select" ON public.invoice_items
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.invoice_items
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.invoice_items
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.invoice_items
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for medical_devices table (if exists)
CREATE POLICY "clinic_isolation_select" ON public.medical_devices
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.medical_devices
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.medical_devices
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.medical_devices
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Storage policies for patient-files bucket with clinic isolation
DROP POLICY IF EXISTS "clinic_isolation_select" ON storage.objects;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON storage.objects;
DROP POLICY IF EXISTS "clinic_isolation_update" ON storage.objects;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON storage.objects;

CREATE POLICY "clinic_isolation_select" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'patient-files' AND
    EXISTS (
      SELECT 1 FROM public.patient_files 
      WHERE path = name 
      AND clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
    )
  );

CREATE POLICY "clinic_isolation_insert" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'patient-files' AND
    EXISTS (
      SELECT 1 FROM public.patient_files 
      WHERE path = name 
      AND clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
    )
  );

CREATE POLICY "clinic_isolation_update" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'patient-files' AND
    EXISTS (
      SELECT 1 FROM public.patient_files 
      WHERE path = name 
      AND clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
    )
  );

CREATE POLICY "clinic_isolation_delete" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'patient-files' AND
    EXISTS (
      SELECT 1 FROM public.patient_files 
      WHERE path = name 
      AND clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
    )
  );
