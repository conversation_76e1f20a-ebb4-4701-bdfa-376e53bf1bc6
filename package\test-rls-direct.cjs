/**
 * Test diretto delle policy RLS usando query SQL
 */

const { createClient } = require('@supabase/supabase-js');

// Configurazione Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ctfufjfktpbaufwumacq.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN0ZnVmamZrdHBiYXVmd3VtYWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMjk1NDgsImV4cCI6MjA2ODYwNTU0OH0.St-na9CVJYZXKND5ZQRMl4MeeQkF2fFikuLHyTFnJm0';

console.log('🔍 === TEST DIRETTO RLS ===\n');

async function main() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  // Test 1: Verifica se RLS è abilitato
  console.log('1. === VERIFICA RLS ABILITATO ===');
  try {
    const { data, error } = await supabase.rpc('check_rls_status');
    if (error) {
      console.log('❌ Errore nel controllo RLS:', error.message);
      console.log('💡 Proviamo con query SQL diretta...');
      
      // Query SQL diretta per verificare RLS
      const { data: rlsData, error: rlsError } = await supabase
        .rpc('exec_sql', { 
          query: `
            SELECT 
              schemaname,
              tablename,
              rowsecurity as rls_enabled
            FROM pg_tables 
            WHERE schemaname = 'public' 
            AND tablename IN ('patients', 'doctors', 'appointments')
            ORDER BY tablename;
          `
        });
      
      if (rlsError) {
        console.log('❌ Errore nella query SQL diretta:', rlsError.message);
      } else {
        console.log('✅ Stato RLS per tabelle principali:', rlsData);
      }
    } else {
      console.log('✅ Stato RLS:', data);
    }
  } catch (error) {
    console.log('❌ Errore generale:', error.message);
  }

  // Test 2: Verifica policy esistenti
  console.log('\n2. === VERIFICA POLICY ESISTENTI ===');
  try {
    const { data: policies, error } = await supabase
      .rpc('exec_sql', { 
        query: `
          SELECT 
            schemaname,
            tablename,
            policyname,
            permissive,
            roles,
            cmd,
            qual,
            with_check
          FROM pg_policies 
          WHERE schemaname = 'public' 
          AND tablename IN ('patients', 'doctors', 'appointments')
          ORDER BY tablename, policyname;
        `
      });
    
    if (error) {
      console.log('❌ Errore nel recupero policy:', error.message);
    } else {
      console.log('✅ Policy trovate:', policies?.length || 0);
      if (policies && policies.length > 0) {
        policies.forEach(policy => {
          console.log(`   ${policy.tablename}.${policy.policyname}: ${policy.cmd} (${policy.permissive})`);
        });
      }
    }
  } catch (error) {
    console.log('❌ Errore nella query policy:', error.message);
  }

  // Test 3: Test query senza autenticazione
  console.log('\n3. === TEST QUERY SENZA AUTENTICAZIONE ===');
  try {
    const { data: patients, error } = await supabase
      .from('patients')
      .select('id, clinic_id, first_name, last_name')
      .limit(3);
    
    if (error) {
      console.log('✅ Query bloccata da RLS (corretto):', error.message);
    } else {
      console.log('❌ PROBLEMA: Query riuscita senza autenticazione!');
      console.log('   Pazienti trovati:', patients?.length || 0);
      if (patients && patients.length > 0) {
        patients.forEach(p => {
          console.log(`   - ${p.first_name} ${p.last_name} (clinic: ${p.clinic_id})`);
        });
      }
    }
  } catch (error) {
    console.log('✅ Errore di connessione (normale):', error.message);
  }

  // Test 4: Verifica current_setting JWT
  console.log('\n4. === TEST CURRENT_SETTING JWT ===');
  try {
    const { data: jwtSettings, error } = await supabase
      .rpc('exec_sql', { 
        query: "SELECT current_setting('request.jwt.claims', true) as jwt_claims;"
      });
    
    if (error) {
      console.log('❌ Errore nel recupero JWT settings:', error.message);
    } else {
      console.log('JWT Claims correnti:', jwtSettings);
    }
  } catch (error) {
    console.log('❌ Errore nella query JWT:', error.message);
  }

  // Test 5: Verifica utente corrente
  console.log('\n5. === TEST UTENTE CORRENTE ===');
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('✅ Nessun utente autenticato (normale):', error.message);
    } else if (!user) {
      console.log('✅ Nessun utente autenticato (normale)');
    } else {
      console.log('🔐 Utente autenticato:', user.email);
      console.log('   App metadata:', user.app_metadata);
      console.log('   User metadata:', user.user_metadata);
    }
  } catch (error) {
    console.log('❌ Errore nel controllo utente:', error.message);
  }

  console.log('\n🎯 === CONCLUSIONI ===');
  console.log('Se le query sui pazienti riescono senza autenticazione,');
  console.log('significa che RLS non è configurato correttamente o non è abilitato.');
  console.log('');
  console.log('Possibili cause:');
  console.log('1. RLS non abilitato sulle tabelle');
  console.log('2. Policy troppo permissive');
  console.log('3. Ruolo anon ha accesso diretto');
  console.log('4. Migrazioni RLS non applicate correttamente');
}

main();
