/**
 * Script di debug per identificare problemi con l'isolamento delle cliniche
 */

const { createClient } = require('@supabase/supabase-js');

// Configurazione Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

console.log('🔍 === DEBUG ISOLAMENTO CLINICHE ===\n');

async function debugUserAuth() {
  console.log('👤 === DEBUG AUTENTICAZIONE UTENTE ===');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Ottieni l'utente corrente
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('❌ Errore nell\'ottenere l\'utente:', error.message);
      return null;
    }
    
    if (!user) {
      console.log('❌ Nessun utente autenticato');
      return null;
    }
    
    console.log('✅ Utente autenticato:');
    console.log('  - ID:', user.id);
    console.log('  - Email:', user.email);
    console.log('  - User Metadata:', JSON.stringify(user.user_metadata, null, 2));
    console.log('  - App Metadata:', JSON.stringify(user.app_metadata, null, 2));
    
    // Verifica clinic_id
    const clinicId = user.user_metadata?.clinic_id;
    if (clinicId) {
      console.log('✅ Clinic ID trovato:', clinicId);
    } else {
      console.log('❌ PROBLEMA: Clinic ID non trovato nei metadati utente!');
      console.log('💡 L\'utente deve avere clinic_id nei user_metadata');
    }
    
    return user;
    
  } catch (error) {
    console.log('❌ Errore nel debug autenticazione:', error.message);
    return null;
  }
}

async function debugRLSPolicies() {
  console.log('\n🛡️ === DEBUG POLICY RLS ===');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Verifica se RLS è abilitato sulle tabelle principali
    const { data: tables, error } = await supabase
      .from('pg_tables')
      .select('tablename, rowsecurity')
      .eq('schemaname', 'public')
      .in('tablename', ['patients', 'doctors', 'appointments', 'treatments', 'invoices']);
    
    if (error) {
      console.log('❌ Errore nel verificare RLS:', error.message);
      return;
    }
    
    console.log('📋 Stato RLS per tabelle principali:');
    tables.forEach(table => {
      const status = table.rowsecurity ? '✅ ABILITATO' : '❌ DISABILITATO';
      console.log(`  - ${table.tablename}: ${status}`);
    });
    
    // Verifica policy esistenti
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('tablename, policyname, cmd')
      .eq('schemaname', 'public')
      .like('policyname', '%clinic_isolation%');
    
    if (policiesError) {
      console.log('❌ Errore nel verificare policy:', policiesError.message);
      return;
    }
    
    console.log('\n📜 Policy di isolamento trovate:');
    if (policies.length === 0) {
      console.log('❌ PROBLEMA: Nessuna policy di isolamento trovata!');
      console.log('💡 Le policy RLS potrebbero non essere state create correttamente');
    } else {
      const groupedPolicies = policies.reduce((acc, policy) => {
        if (!acc[policy.tablename]) acc[policy.tablename] = [];
        acc[policy.tablename].push(`${policy.cmd}: ${policy.policyname}`);
        return acc;
      }, {});
      
      Object.entries(groupedPolicies).forEach(([table, tablePolicies]) => {
        console.log(`  📋 ${table}:`);
        tablePolicies.forEach(policy => console.log(`    - ${policy}`));
      });
    }
    
  } catch (error) {
    console.log('❌ Errore nel debug RLS:', error.message);
  }
}

async function debugDirectQuery() {
  console.log('\n🔍 === DEBUG QUERY DIRETTE ===');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Query diretta senza filtri per vedere tutti i pazienti
    console.log('📊 Query diretta pazienti (senza filtri):');
    const { data: allPatients, error: allError } = await supabase
      .from('patients')
      .select('id, first_name, last_name, clinic_id')
      .limit(10);
    
    if (allError) {
      console.log('❌ Errore nella query diretta:', allError.message);
    } else {
      console.log(`  Totale pazienti visibili: ${allPatients.length}`);
      
      // Raggruppa per clinic_id
      const byClinic = allPatients.reduce((acc, patient) => {
        const clinicId = patient.clinic_id || 'NULL';
        if (!acc[clinicId]) acc[clinicId] = 0;
        acc[clinicId]++;
        return acc;
      }, {});
      
      console.log('  Distribuzione per clinic_id:');
      Object.entries(byClinic).forEach(([clinicId, count]) => {
        console.log(`    - ${clinicId}: ${count} pazienti`);
      });
      
      if (Object.keys(byClinic).length > 1) {
        console.log('❌ PROBLEMA: L\'utente vede pazienti di cliniche diverse!');
        console.log('💡 Le policy RLS non stanno funzionando correttamente');
      } else {
        console.log('✅ L\'utente vede pazienti di una sola clinica');
      }
    }
    
    // Test con filtro manuale clinic_id
    const { data: { user } } = await supabase.auth.getUser();
    const clinicId = user?.user_metadata?.clinic_id;
    
    if (clinicId) {
      console.log(`\n📊 Query con filtro manuale clinic_id (${clinicId}):`);
      const { data: filteredPatients, error: filteredError } = await supabase
        .from('patients')
        .select('id, first_name, last_name, clinic_id')
        .eq('clinic_id', clinicId)
        .limit(10);
      
      if (filteredError) {
        console.log('❌ Errore nella query filtrata:', filteredError.message);
      } else {
        console.log(`  Pazienti con filtro manuale: ${filteredPatients.length}`);
        
        // Verifica che tutti abbiano il clinic_id corretto
        const wrongClinic = filteredPatients.filter(p => p.clinic_id !== clinicId);
        if (wrongClinic.length > 0) {
          console.log(`❌ PROBLEMA: ${wrongClinic.length} pazienti con clinic_id errato!`);
        } else {
          console.log('✅ Tutti i pazienti hanno clinic_id corretto');
        }
      }
    }
    
  } catch (error) {
    console.log('❌ Errore nel debug query:', error.message);
  }
}

async function debugServiceUtils() {
  console.log('\n🔧 === DEBUG SERVICE UTILS ===');
  
  try {
    // Simula l'importazione di ServiceUtils
    console.log('📁 Verificando ServiceUtils...');
    
    const fs = require('fs');
    const path = require('path');
    
    const serviceUtilsPath = path.join(__dirname, 'src', 'services', 'ServiceUtils.ts');
    
    if (!fs.existsSync(serviceUtilsPath)) {
      console.log('❌ PROBLEMA: ServiceUtils.ts non trovato!');
      return;
    }
    
    const content = fs.readFileSync(serviceUtilsPath, 'utf8');
    
    // Verifica funzioni chiave
    const hasGetClinicId = content.includes('export async function getClinicId');
    const hasWithClinicId = content.includes('export async function withClinicId');
    
    console.log(`  - getClinicId function: ${hasGetClinicId ? '✅ PRESENTE' : '❌ MANCANTE'}`);
    console.log(`  - withClinicId function: ${hasWithClinicId ? '✅ PRESENTE' : '❌ MANCANTE'}`);
    
    // Verifica implementazione getClinicId
    if (content.includes('user_metadata?.clinic_id')) {
      console.log('  - Implementazione getClinicId: ✅ Usa user_metadata.clinic_id');
    } else {
      console.log('  - Implementazione getClinicId: ❌ Potrebbe non usare user_metadata');
    }
    
  } catch (error) {
    console.log('❌ Errore nel debug ServiceUtils:', error.message);
  }
}

async function suggestFixes() {
  console.log('\n🔧 === POSSIBILI SOLUZIONI ===');
  
  console.log('1. 👤 PROBLEMA UTENTE:');
  console.log('   Se l\'utente non ha clinic_id nei metadati:');
  console.log('   - Aggiorna i metadati utente con clinic_id');
  console.log('   - Oppure modifica getClinicId() per usare un fallback');
  
  console.log('\n2. 🛡️ PROBLEMA RLS:');
  console.log('   Se RLS non è abilitato o le policy mancano:');
  console.log('   - Esegui le migrazioni RLS: npx supabase db reset');
  console.log('   - Verifica che le policy siano create correttamente');
  
  console.log('\n3. 🔧 PROBLEMA SERVIZI:');
  console.log('   Se i servizi non usano getClinicId():');
  console.log('   - Verifica che tutti i servizi importino getClinicId');
  console.log('   - Verifica che usino .eq("clinic_id", clinicId)');
  
  console.log('\n4. 🗄️ PROBLEMA DATABASE:');
  console.log('   Se le tabelle non hanno clinic_id:');
  console.log('   - Esegui le migrazioni: npx supabase db reset');
  console.log('   - Verifica che tutte le tabelle abbiano clinic_id');
  
  console.log('\n💡 PROSSIMI PASSI RACCOMANDATI:');
  console.log('1. Controlla i risultati di questo debug');
  console.log('2. Risolvi i problemi identificati');
  console.log('3. Testa nuovamente con utenti diversi');
  console.log('4. Verifica che ogni utente veda solo i propri dati');
}

async function runDebug() {
  console.log('🚀 Iniziando debug completo isolamento cliniche...\n');
  
  const user = await debugUserAuth();
  await debugRLSPolicies();
  await debugDirectQuery();
  await debugServiceUtils();
  await suggestFixes();
  
  console.log('\n🏁 === DEBUG COMPLETATO ===');
  console.log('Analizza i risultati sopra per identificare il problema specifico.');
}

// Esegui il debug
runDebug().catch(error => {
  console.error('❌ Errore durante il debug:', error);
  process.exit(1);
});
