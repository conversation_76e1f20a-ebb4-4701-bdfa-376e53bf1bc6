/**
 * 🔍 DIAGNOSI COMPLETA RLS - ESECUZIONE DA VS CODE
 * 
 * Questo script esegue tutte le query richieste per la diagnosi RLS
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 === DIAGNOSI COMPLETA RLS - ESECUZIONE DA VS CODE ===\n');

async function diagnosi() {
  const supabaseService = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('1️⃣ === STATO RLS REALE SULLE TABELLE CORE ===\n');

    console.log('1.1 – Elenco policy applicate:');

    // Usa query SQL diretta con il service role
    try {
      const { data: policies, error: policiesError } = await supabaseService
        .from('information_schema.table_privileges')
        .select('*')
        .limit(1);

      console.log('❌ Non possiamo accedere alle system tables via REST API');
      console.log('📋 Dobbiamo usare il dashboard Supabase per le query di sistema');

    } catch (err) {
      console.log('❌ Errore accesso system tables:', err.message);
    }

    console.log('\n1.2 – Flag RLS ON/OFF e owner:');
    console.log('❌ Anche questa richiede accesso diretto al database PostgreSQL');
    console.log('📋 Usa il dashboard Supabase SQL Editor per eseguire:');
    console.log(`
    SELECT polname, cmd, permissive, roles, qual, with_check
    FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename IN ('patients','doctors','appointments')
    ORDER BY tablename, cmd;

    SELECT relname, relrowsecurity AS rls_enabled, relforcerowsecurity AS rls_forced, pg_get_userbyid(relowner) AS owner
    FROM pg_class
    WHERE relname IN ('patients','doctors','appointments');
    `);


    console.log('\n2️⃣ === SIMULAZIONE JWT VIA REQUEST.JWT.CLAIMS ===\n');

    console.log('2.1 – Test INSERT diretto con Service Role (bypassa RLS):');

    // Test INSERT diretto con service role (dovrebbe sempre funzionare)
    const { data: insertDemo, error: insertDemoError } = await supabaseService
      .from('patients')
      .insert({
        id: crypto.randomUUID(),
        first_name: 'Test-ServiceRole',
        clinic_id: '00000000-0000-0000-0000-000000000000'
      })
      .select();

    if (insertDemoError) {
      console.log('❌ ERRORE INSERT SERVICE ROLE:', insertDemoError.message);
      console.log('📋 Codice:', insertDemoError.code);
    } else {
      console.log('✅ INSERT SERVICE ROLE RIUSCITO:', insertDemo);
    }

    console.log('\n2.2 – Test con client anonimo (dovrebbe rispettare RLS):');

    // Test con client anonimo
    const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

    const { data: insertAnon, error: insertAnonError } = await supabaseAnon
      .from('patients')
      .insert({
        id: crypto.randomUUID(),
        first_name: 'Test-Anon',
        clinic_id: '00000000-0000-0000-0000-000000000000'
      })
      .select();

    if (insertAnonError) {
      console.log('✅ INSERT ANONIMO FALLITO COME ATTESO:', insertAnonError.message);
      console.log('📋 Codice:', insertAnonError.code);
    } else {
      console.log('❌ INSERT ANONIMO NON DOVEVA RIUSCIRE:', insertAnon);
    }

    console.log('\n4️⃣ === CONTROLLO DATI REALI ===\n');

    // Query dati reali
    const { data: recentPatients, error: patientsError } = await supabaseService
      .from('patients')
      .select('id, clinic_id, created_at')
      .order('created_at', { ascending: false })
      .limit(5);

    if (patientsError) {
      console.log('❌ Errore query pazienti:', patientsError.message);
    } else {
      console.log('📋 Ultimi 5 pazienti:');
      console.table(recentPatients || []);
    }

    console.log('\n🎯 === DIAGNOSI COMPLETATA ===');

  } catch (error) {
    console.error('💥 Errore durante la diagnosi:', error);
    console.log('\n📋 Stack trace:', error.stack);
  }
}

// Esegui la diagnosi
diagnosi();
