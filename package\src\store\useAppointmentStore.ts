/**
 * MOKO SOSTANZA - Appointment Store
 * 
 * Zustand store per la gestione degli appuntamenti con optimistic updates
 */

import { create } from 'zustand';
import { AppointmentService, AppointmentWithDetails, AppointmentInsert, AppointmentUpdate } from '../services/AppointmentService';

interface AppointmentStore {
  // State
  appointments: AppointmentWithDetails[];
  loading: boolean;
  error: string | null;

  // Actions
  loadByDateRange: (startDate: string, endDate: string) => Promise<void>;
  create: (data: AppointmentInsert) => Promise<AppointmentWithDetails>;
  update: (id: number, data: AppointmentUpdate) => Promise<AppointmentWithDetails>;
  softDelete: (id: number) => Promise<void>;
  restore: (id: number) => Promise<void>;
  
  // Utilities
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAppointmentStore = create<AppointmentStore>((set, get) => ({
  // Initial state
  appointments: [],
  loading: false,
  error: null,

  // Load appointments by date range
  loadByDateRange: async (startDate: string, endDate: string) => {
    set({ loading: true, error: null });
    try {
      const appointments = await AppointmentService.getAppointmentsByDateRange(startDate, endDate);
      set({ appointments, loading: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Errore nel caricamento appuntamenti';
      set({ error: errorMessage, loading: false });
      throw error;
    }
  },

  // Create appointment with optimistic update
  create: async (data: AppointmentInsert) => {
    const { appointments } = get();
    let tempId: number | null = null;
    
    try {
      // Optimistic update: crea un appuntamento temporaneo
      tempId = Date.now(); // ID temporaneo
      const tempAppointment: AppointmentWithDetails = {
        id: tempId,
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // Placeholder per le relazioni (verranno aggiornate dopo la risposta del server)
        patient: undefined,
        doctor: undefined,
        treatment: undefined,
      } as any;

      set({ appointments: [tempAppointment, ...appointments] });

      // Server call
      const newAppointment = await AppointmentService.createAppointment(data);

      // Update con i dati reali dal server
      set({
        appointments: appointments.map(app => 
          app.id === tempId ? newAppointment : app
        )
      });

      return newAppointment;
    } catch (error) {
      // Rollback optimistic update se tempId è stato creato
      if (tempId !== null) {
        set({ appointments: appointments.filter(app => app.id !== tempId) });
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Errore nella creazione appuntamento';
      set({ error: errorMessage });
      throw error;
    }
  },

  // Update appointment with optimistic update
  update: async (id: number, data: AppointmentUpdate) => {
    const { appointments } = get();
    const originalAppointment = appointments.find(app => app.id === id);
    
    if (!originalAppointment) {
      throw new Error('Appuntamento non trovato');
    }

    try {
      // Optimistic update
      const updatedAppointment = { ...originalAppointment, ...data };
      set({
        appointments: appointments.map(app =>
          app.id === id ? updatedAppointment : app
        )
      });

      // Server call
      const serverAppointment = await AppointmentService.updateAppointment(id, data);

      // Update con i dati reali dal server
      set({
        appointments: appointments.map(app =>
          app.id === id ? serverAppointment : app
        )
      });

      return serverAppointment;
    } catch (error) {
      // Rollback optimistic update
      set({
        appointments: appointments.map(app =>
          app.id === id ? originalAppointment : app
        )
      });

      const errorMessage = error instanceof Error ? error.message : 'Errore nell\'aggiornamento appuntamento';
      set({ error: errorMessage });
      throw error;
    }
  },

  // Soft delete appointment with optimistic update
  softDelete: async (id: number) => {
    const { appointments } = get();
    const appointmentToDelete = appointments.find(app => app.id === id);
    
    if (!appointmentToDelete) {
      throw new Error('Appuntamento non trovato');
    }

    try {
      // Optimistic update: rimuovi dalla lista
      set({
        appointments: appointments.filter(app => app.id !== id)
      });

      // Server call
      await AppointmentService.deleteAppointment(id);
      
      // Success - l'appuntamento è già stato rimosso dalla lista
    } catch (error) {
      // Rollback optimistic update
      set({ appointments: [...appointments] });

      const errorMessage = error instanceof Error ? error.message : 'Errore nell\'eliminazione appuntamento';
      set({ error: errorMessage });
      throw error;
    }
  },

  // Restore deleted appointment
  restore: async (id: number) => {
    try {
      await AppointmentService.restoreAppointment(id);
      
      // Refetch per aggiornare la lista (il restore potrebbe riportare appuntamenti non visibili)
      const { loadByDateRange } = get();
      const today = new Date();
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0];
      
      await loadByDateRange(startOfMonth, endOfMonth);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Errore nel ripristino appuntamento';
      set({ error: errorMessage });
      throw error;
    }
  },

  // Utility actions
  clearError: () => set({ error: null }),
  setLoading: (loading: boolean) => set({ loading }),
}));
