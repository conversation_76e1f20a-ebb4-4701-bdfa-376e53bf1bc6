/**
 * <PERSON><PERSON><PERSON> SOSTANZA Dental CRM - File Path Migration Script
 * Migra i percorsi file esistenti dal formato vecchio a quello con clinic isolation
 * 
 * OLD: ${patientId}/${category}/${filename}
 * NEW: ${clinicId}/${patientId}/${category}/${filename}
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface MigrationResult {
  success: boolean;
  movedFiles: number;
  errors: string[];
  skippedFiles: number;
}

async function migrateFilesPaths(): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: true,
    movedFiles: 0,
    errors: [],
    skippedFiles: 0
  };

  try {
    console.log('🔄 [FilePathMigration] Starting file path migration...');

    // Get Demo Clinic ID
    const { data: demoClinic, error: clinicError } = await supabase
      .from('clinics')
      .select('id')
      .eq('name', 'Demo Clinic')
      .single();

    if (clinicError || !demoClinic) {
      throw new Error('Demo Clinic not found');
    }

    const clinicId = demoClinic.id;
    console.log(`🏥 [FilePathMigration] Using clinic ID: ${clinicId}`);

    // Get all patient files that need migration
    const { data: patientFiles, error: filesError } = await supabase
      .from('patient_files')
      .select('*')
      .is('clinic_id', null); // Files without clinic_id need migration

    if (filesError) {
      throw new Error(`Failed to fetch patient files: ${filesError.message}`);
    }

    if (!patientFiles || patientFiles.length === 0) {
      console.log('✅ [FilePathMigration] No files need migration');
      return result;
    }

    console.log(`📁 [FilePathMigration] Found ${patientFiles.length} files to migrate`);

    // Process each file
    for (const file of patientFiles) {
      try {
        const oldPath = file.path;
        const newPath = `${clinicId}/${oldPath}`;

        console.log(`🔄 [FilePathMigration] Migrating: ${oldPath} -> ${newPath}`);

        // Check if old file exists in storage
        const { data: oldFileData, error: downloadError } = await supabase.storage
          .from('patient-files')
          .download(oldPath);

        if (downloadError) {
          console.warn(`⚠️ [FilePathMigration] Old file not found in storage: ${oldPath}`);
          result.skippedFiles++;
          
          // Update database record with new path and clinic_id anyway
          await supabase
            .from('patient_files')
            .update({ 
              path: newPath, 
              clinic_id: clinicId 
            })
            .eq('id', file.id);
          
          continue;
        }

        // Upload file to new location
        const { error: uploadError } = await supabase.storage
          .from('patient-files')
          .upload(newPath, oldFileData, {
            cacheControl: '3600',
            upsert: true
          });

        if (uploadError) {
          const errorMsg = `Failed to upload to new path ${newPath}: ${uploadError.message}`;
          result.errors.push(errorMsg);
          console.error(`❌ [FilePathMigration] ${errorMsg}`);
          continue;
        }

        // Update database record
        const { error: updateError } = await supabase
          .from('patient_files')
          .update({ 
            path: newPath, 
            clinic_id: clinicId 
          })
          .eq('id', file.id);

        if (updateError) {
          const errorMsg = `Failed to update database for file ${file.id}: ${updateError.message}`;
          result.errors.push(errorMsg);
          console.error(`❌ [FilePathMigration] ${errorMsg}`);
          continue;
        }

        // Remove old file
        const { error: removeError } = await supabase.storage
          .from('patient-files')
          .remove([oldPath]);

        if (removeError) {
          console.warn(`⚠️ [FilePathMigration] Failed to remove old file ${oldPath}: ${removeError.message}`);
          // Not critical, continue
        }

        result.movedFiles++;
        console.log(`✅ [FilePathMigration] Successfully migrated: ${oldPath} -> ${newPath}`);

      } catch (error: any) {
        const errorMsg = `Error processing file ${file.id}: ${error.message}`;
        result.errors.push(errorMsg);
        console.error(`❌ [FilePathMigration] ${errorMsg}`);
      }
    }

    if (result.errors.length > 0) {
      result.success = false;
    }

    console.log(`🏁 [FilePathMigration] Migration completed:`);
    console.log(`   ✅ Moved files: ${result.movedFiles}`);
    console.log(`   ⏭️ Skipped files: ${result.skippedFiles}`);
    console.log(`   ❌ Errors: ${result.errors.length}`);

    if (result.errors.length > 0) {
      console.log(`\n🚨 [FilePathMigration] Errors:`);
      result.errors.forEach(error => console.log(`   - ${error}`));
    }

    return result;

  } catch (error: any) {
    console.error(`💥 [FilePathMigration] Fatal error:`, error);
    result.success = false;
    result.errors.push(error.message);
    return result;
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateFilesPaths()
    .then(result => {
      if (result.success) {
        console.log('✅ [FilePathMigration] Migration completed successfully');
        process.exit(0);
      } else {
        console.log('❌ [FilePathMigration] Migration completed with errors');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 [FilePathMigration] Migration failed:', error);
      process.exit(1);
    });
}

export { migrateFilesPaths };
