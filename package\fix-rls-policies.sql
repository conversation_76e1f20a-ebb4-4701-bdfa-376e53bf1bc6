-- Fix definitivo policy RLS per isolamento cliniche
-- Problema: Le policy usano il path JWT sbagliato

-- 1. Verifica policy attuali
SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients' 
  AND policyname LIKE 'clinic_isolation_%'
ORDER BY cmd;

-- 2. Rimuovi policy esistenti per patients
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

-- 3. <PERSON>rea policy corrette con path JWT giusto
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- 4. Verifica che le policy siano state create correttamente
SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients' 
  AND policyname LIKE 'clinic_isolation_%'
ORDER BY cmd;
