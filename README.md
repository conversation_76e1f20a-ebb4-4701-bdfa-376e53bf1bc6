# 🏥 Gestionale Odontoiatrico - Sistema di Gestione per Studi Dentistici

![Gestionale Odontoiatrico](./package/src/assets/images/logos/dental-crm-logo-basic.svg)

Un sistema di gestione completo e moderno per studi dentistici e cliniche odontoiatriche, sviluppato con React, TypeScript, Tailwind CSS e Supabase Cloud.

## 🌟 **MIGRAZIONE COMPLETATA A SUPABASE CLOUD**

✅ Database migrato su cloud per collaborazione senza Docker  
✅ Setup semplificato per nuovi sviluppatori  
✅ Dati sincronizzati in tempo reale tra team

## 🚀 Caratteristiche Principali

- **🎯 Dashboard Intelligente**: Metriche in tempo reale su appuntamenti, pazienti e fatturato
- **👥 Gestione Pazienti Completa**: Anagrafica, cartella clinica, storia trattamenti e documenti
- **👨‍⚕️ Gestione Dottori Avanzata**: Anagrafica FNOMCeO compliant con specializzazioni e qualifiche professionali
- **📅 Sistema Appuntamenti Avanzato**: Calendario interattivo con soft-delete, multi-clinic support e autocompletamento pazienti
- **⏰ Sistema Promemoria**: Promemoria personalizzati collegati ai pazienti con categorizzazione e toast di conferma
- **📁 Allegati Diagnostici**: Upload e gestione file OTP, TAC, Cone Beam con preview inline
- **💰 Fatturazione Avanzata**: Creazione fatture, tracking pagamenti e reportistica finanziaria
- **🏥 Gestione Trattamenti**: Catalogo procedure odontoiatriche con prezzi e durate
- **📦 Inventario**: Monitoraggio materiali e scorte con alert automatici
- **👨‍⚕️ Multi-Ruolo**: Dashboard separate per dentisti e amministratori clinica
- **🔔 Sistema Toast**: Feedback immediato per tutte le operazioni CRUD con notifiche di successo/errore
- **🏥 Isolamento Multi-Clinica**: Architettura multi-tenant con isolamento completo dei dati per clinica

## � Indice

- [🚀 Caratteristiche Principali](#-caratteristiche-principali)
- [📊 Stato del Progetto](#-stato-del-progetto)
  - [✅ Completato (Database Integration)](#-completato-database-integration)
  - [🔄 In Progress](#-in-progress)
- [🏗️ Architettura del Progetto](#️-architettura-del-progetto)
- [🚀 Quick Start](#-quick-start)
  - [Prerequisiti](#prerequisiti)
  - [1. Clone e Setup](#1-clone-e-setup)
  - [2. Configurazione Database](#2-configurazione-database)
  - [3. Avvio Applicazione](#3-avvio-applicazione)
- [🛠️ Stack Tecnologico](#️-stack-tecnologico)
  - [Frontend](#frontend)
  - [Backend & Database](#backend--database)
  - [DevOps & Tools](#devops--tools)
- [🏥 Isolamento Multi-Clinica](#-isolamento-multi-clinica)
  - [Architettura Multi-Tenant](#architettura-multi-tenant)
  - [Row Level Security (RLS)](#row-level-security-rls)
  - [Implementazione Servizi](#implementazione-servizi)
  - [Testing Isolamento](#testing-isolamento)
- [🌐 Demo e Deployment](#-demo-e-deployment)
  - [🔗 Live Demo](#-live-demo)
  - [🧪 Test Account](#-test-account)
  - [📱 Responsive Testing](#-responsive-testing)
- [📚 Documentazione](#-documentazione)
  - [🏗️ Services Layer](#️-services-layer)
  - [🗄️ Database Schema](#️-database-schema)
  - [📋 Development Docs](#-development-docs)
- [🤝 Contributing](#-contributing)
  - [Workflow Development](#workflow-development)
  - [Code Style](#code-style)
  - [Database Changes](#database-changes)
- [📞 Support & Contact](#-support--contact)
  - [🐛 Issue Reporting](#-issue-reporting)
  - [📧 Contact](#-contact)
- [📄 License](#-license)

## �🚀 Caratteristiche Principali

- **🎯 Dashboard Intelligente**: Metriche in tempo reale su appuntamenti, pazienti e fatturato
- **👥 Gestione Pazienti Completa**: Anagrafica, cartella clinica, storia trattamenti e documenti
- **📅 Sistema Appuntamenti**: Calendario interattivo con notifiche e promemoria
- **⏰ Sistema Promemoria**: Promemoria personalizzati collegati ai pazienti con categorizzazione
- **� Allegati Diagnostici**: Upload e gestione file OTP, TAC, Cone Beam con preview inline
- **�💰 Fatturazione Avanzata**: Creazione fatture, tracking pagamenti e reportistica finanziaria
- **🏥 Gestione Trattamenti**: Catalogo procedure odontoiatriche con prezzi e durate
- **📦 Inventario**: Monitoraggio materiali e scorte con alert automatici
- **👨‍⚕️ Multi-Ruolo**: Dashboard separate per dentisti e amministratori clinica
- **🌙 Tema Dinamico**: Supporto modalità chiara e scura
- **☁️ Supabase Cloud**: Database completamente migrato su cloud per collaborazione senza Docker ✨ **MIGRATO LUGLIO 2025**
- **🤝 Team Collaboration**: Setup semplificato per sviluppatori senza Docker

## 📊 Stato del Progetto

### ✅ Completato (Cloud Migration + Auth System + Database Integration)

- **✅ Nuovo Sistema Autenticazione**: Email/Password completo con registrazione clinica ✨ **NUOVO GENNAIO 2025**
  - **✅ Registrazione Manager**: Form completo dati personali + clinica con validazione robusta
  - **✅ Login/Logout**: Autenticazione classica con gestione errori avanzata
  - **✅ Reset Password**: Flusso completo forgot/reset con email sicura
  - **✅ Database Trigger**: Creazione automatica clinica + sincronizzazione utenti (4/5 test superati)
  - **✅ Email Benvenuto**: Template HTML personalizzato con dati dinamici
- **✅ Supabase Cloud Migration**: Database completamente migrato su cloud con dati di prova ✨ **NUOVO LUGLIO 2025**
- **✅ Team Collaboration Setup**: Documentazione e configurazione per collaboratori senza Docker ✨ **NUOVO**
- **✅ PatientService**: CRUD completo con Supabase integration
- **✅ InvoiceService**: Sistema fatturazione con statistiche avanzate
- **✅ DoctorService**: Gestione dottori avanzata FNOMCeO compliant con specializzazioni e qualifiche ✨ **MIGRATO LUGLIO 2025**
- **✅ TreatmentService**: Catalogo trattamenti e pricing
- **✅ AppointmentService**: Sistema prenotazioni refactored con soft-delete, multi-clinic, validazione avanzata ✨ **REFACTORED LUGLIO 2025**
- **✅ ReminderService**: Sistema promemoria collegati ai pazienti con categorizzazione
- **✅ FileService**: Gestione allegati diagnostici con upload sicuro e soft delete
- **✅ Database Schema**: Struttura completa Supabase con relazioni ottimizzate e storage bucket
- **✅ PatientForm**: Workflow creazione/modifica pazienti con allegati integrati
- **✅ Billing Interface**: Dashboard fatturazione con dati reali
- **✅ Calendar Integration**: Appuntamenti sincronizzati dal database
- **✅ Reminder System**: Promemoria con quick actions e gestione avanzata
- **✅ File Management**: Upload/download/preview di file diagnostici (OTP, TAC, Cone Beam)
- **✅ Doctors Module**: Form semplificati, gestione qualifiche, store Zustand, routing corretto ✨ **AGGIORNATO**
- **✅ Database Migration**: Schema dottori migrato con campi FNOMCeO, rimozione campi obsoleti ✨ **NUOVO**
- **✅ Appointment System Refactor**: Service layer, Zustand store, soft-delete, restore, UI components, tests ✨ **NUOVO LUGLIO 2025**
- **✅ Patient Autocomplete**: Campo paziente nell'appointment modal con autocompletamento e suggerimenti in tempo reale ✨ **NUOVO**
- **✅ Toast System**: Feedback completo per operazioni CRUD con notifiche di successo/errore ✨ **NUOVO**
- **✅ QuickPatientModal**: Creazione rapida pazienti dal modale appuntamento con validazione completa ✨ **NUOVO**
- **✅ Invoice Action Modal**: Modal fatture context-aware con logica email intelligente, rollback automatico e UX avanzata ✨ **NUOVO LUGLIO 2025**

### 🔄 In Progress

- **🔄 Dashboard Analytics**: Sostituzione dati mock con statistiche reali
- **🔄 ProductService**: Sistema inventario completo

## 🏗️ Architettura del Progetto

```
gestionale-odontoiatrico/
├── 📁 package/                    # Main application
│   ├── 📁 src/
│   │   ├── 📁 components/         # Componenti riutilizzabili
│   │   ├── 📁 services/           # Layer di accesso ai dati
│   │   ├── 📁 views/              # Pagine dell'applicazione
│   │   ├── 📁 types/              # Definizioni TypeScript
│   │   └── 📁 utils/              # Utilities e helpers
│   ├── 📁 public/                 # Assets statici
│   └── 📄 package.json            # Dipendenze frontend
├── 📁 supabase/                   # Database configuration
│   ├── 📁 migrations/             # Schema migrations
│   └── 📄 config.toml             # Supabase settings
├── 📁 src/                        # Additional components
└── 📄 README.md                   # Questo file
```

## 🚀 Quick Start

### Prerequisiti

- Node.js 18+
- npm 9+
- Docker Desktop (per Supabase locale)
- Supabase CLI (`npm install -g @supabase/cli`)

### 1. Clone e Setup

```bash
# Clone della repository
git clone https://github.com/daviducciope/GestionaleOdontoiatrico.git
cd GestionaleOdontoiatrico/package

# Installa dipendenze
npm install --legacy-peer-deps
```

### 2. Configurazione Database

#### Opzione A: Database Locale (Docker) - Raccomandato per sviluppo

````bash
#### Opzione A: Supabase Cloud (Raccomandato) ✨

Il progetto è migrato su **Supabase Cloud** per facilitare la collaborazione. Basta copiare le credenziali già configurate:

```bash
# Crea file .env nella cartella package/
cd package
echo 'VITE_SUPABASE_URL=https://ctfufjfktpbaufwumacq.supabase.co
VITE_SUPABASE_ANON_KEY=sb_publishable_EbTdK3H7j6fqpY4XePhq7A_8UDBVqLV' > .env
````

**✅ Database già pronto con dati di esempio:**

- 4 Dottori con dati professionali completi
- 5 Pazienti con informazioni anagrafiche
- 6 Appuntamenti con stato e note
- 8 Trattamenti odontoiatrici
- 3 Promemoria di esempio
- 2 Fatture di test

**📊 Dashboard Supabase**: [https://supabase.com/dashboard/project/ctfufjfktpbaufwumacq](https://supabase.com/dashboard/project/ctfufjfktpbaufwumacq)

#### Opzione B: Supabase Locale (Solo per sviluppo avanzato)

```bash
# Avvia Supabase locale (Docker deve essere attivo)
npx supabase start

# Copia le credenziali mostrate nel terminale e crea .env
# Il file .env dovrebbe contenere:
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=<anon_key_from_supabase_start>
```

**URLs Locali:**

- **API**: http://127.0.0.1:54321
- **Studio**: http://127.0.0.1:54323
- **Database**: postgresql://postgres:postgres@127.0.0.1:54322/postgres

#### Setup per Nuovi Collaboratori

Se sei un nuovo collaboratore senza Docker, consulta la guida: **[COLLABORATOR_SETUP.md](./COLLABORATOR_SETUP.md)**

### 3. Avvio Applicazione

```bash
# Avvia development server
npm run dev

# Apri browser su:
# http://localhost:5174 (o altra porta disponibile)
```

### 4. Reset Database (se necessario)

```bash
# Se hai problemi con container vecchi:
docker stop $(docker ps -q --filter "name=supabase")
docker rm $(docker ps -aq --filter "name=supabase")
npx supabase start
```

## 🛠️ Stack Tecnologico

### Frontend

- **⚛️ React 18** + **TypeScript** - UI moderna e type-safe
- **⚡ Vite** - Build tool ultra-veloce
- **🎨 Tailwind CSS** - Styling utilitario
- **🌊 Flowbite React** - Componenti UI pre-costruiti
- **📊 ApexCharts** - Grafici e visualizzazioni
- **🗂️ React Router v7** - Routing avanzato
- **🏪 Zustand** - State management leggero

### Backend & Database

- **🗄️ Supabase** - Backend-as-a-Service con PostgreSQL
- **🔄 Real-time sync** - Aggiornamenti automatici cross-client
- **🔐 Row Level Security** - Sicurezza granulare dei dati
- **📁 Storage** - Upload file e documenti

### DevOps & Tools

- **🚀 Vercel** - Deployment automatico
- **📋 ESLint + Prettier** - Code quality
- **🧪 Vitest** - Testing framework
- **📖 TypeScript** - Documentazione vivente del codice

## 🏥 Isolamento Multi-Clinica

Il sistema implementa un'architettura **multi-tenant** completa che garantisce l'isolamento totale dei dati tra diverse cliniche odontoiatriche. Ogni clinica può utilizzare lo stesso sistema mantenendo i propri dati completamente separati e sicuri.

### Architettura Multi-Tenant

Il sistema utilizza un approccio **multi-tenant con isolamento a livello di riga** (Row Level Security) che garantisce:

- **🔒 Isolamento Completo**: Ogni clinica vede solo i propri dati
- **🛡️ Sicurezza Automatica**: Le policy RLS impediscono accessi non autorizzati
- **⚡ Performance Ottimali**: Un singolo database condiviso con filtri automatici
- **🔧 Manutenzione Semplificata**: Un'unica istanza dell'applicazione per tutte le cliniche

#### Tabelle con Isolamento Clinic_ID

Tutte le tabelle principali includono una colonna `clinic_id` per l'isolamento:

**Tabelle Core:**

- `patients` - Pazienti della clinica
- `doctors` - Dottori della clinica
- `appointments` - Appuntamenti della clinica
- `treatments` - Trattamenti della clinica
- `invoices` - Fatture della clinica
- `reminders` - Promemoria della clinica
- `patient_events` - Eventi pazienti della clinica
- `patient_files` - File pazienti della clinica

**Tabelle Secondarie:**

- `dental_procedures` - Procedure odontoiatriche per clinica
- `doctor_availability` - Disponibilità dottori per clinica
- `doctor_specialties` - Specializzazioni dottori per clinica
- `event_attachments` - Allegati eventi per clinica
- `invoice_items` - Voci fatture per clinica

**Tabelle Inventario:**

- `products` - Prodotti inventario per clinica
- `medical_devices` - Dispositivi medici per clinica

### Row Level Security (RLS)

Il sistema utilizza le **Row Level Security Policies** di PostgreSQL per garantire l'isolamento automatico:

```sql
-- Esempio di policy per la tabella patients
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
```

**Vantaggi delle Policy RLS:**

- **🔐 Sicurezza a Livello Database**: Impossibile bypassare l'isolamento
- **🚀 Performance**: Filtri applicati automaticamente dal database
- **🛠️ Trasparenza**: I servizi non devono gestire manualmente l'isolamento
- **🔄 Consistenza**: Tutte le operazioni CRUD sono automaticamente filtrate

### Implementazione Servizi

Tutti i servizi sono stati aggiornati per supportare l'isolamento delle cliniche:

#### Pattern di Implementazione

```typescript
import { getClinicId, withClinicId } from './ServiceUtils';

// Per operazioni di lettura - aggiunge automaticamente il filtro clinic_id
static async getPatients(): Promise<PaginatedResponse<Patient>> {
  const clinicId = await getClinicId(); // Ottiene clinic_id dal contesto utente

  let query = supabase
    .from('patients')
    .select('*')
    .eq('clinic_id', clinicId); // Filtro automatico per isolamento

  // ... resto della logica
}

// Per operazioni di scrittura - aggiunge automaticamente clinic_id
static async createPatient(patientData: CreatePatientData): Promise<Patient> {
  const dataWithClinic = await withClinicId(patientData); // Aggiunge clinic_id automaticamente

  const { data, error } = await supabase
    .from('patients')
    .insert(dataWithClinic)
    .select()
    .single();

  // ... resto della logica
}
```

#### Servizi Aggiornati

✅ **PatientService** - 10 filtri clinic_id implementati
✅ **DoctorService** - 6 filtri clinic_id implementati
✅ **AppointmentService** - 7 filtri clinic_id implementati
✅ **TreatmentService** - 2 filtri clinic_id implementati
✅ **InvoiceService** - 5 filtri clinic_id implementati
✅ **FileService** - 2 filtri clinic_id implementati
✅ **ReminderService** - 7 filtri clinic_id implementati
✅ **PatientEventService** - 2 filtri clinic_id implementati

### Testing Isolamento

Il sistema include test completi per verificare l'isolamento:

#### Test Automatici

```bash
# Test di verifica configurazione servizi
node test-isolation.cjs

# Test funzionale con dati reali (richiede database attivo)
node test-functional-isolation.cjs
```

#### Test Manuali

1. **Verifica Isolamento Dati**: Ogni clinica vede solo i propri record
2. **Test Cross-Clinic**: Impossibile accedere a dati di altre cliniche
3. **Verifica Statistiche**: Le metriche sono isolate per clinica
4. **Test Policy RLS**: Le policy impediscono accessi non autorizzati

#### Risultati Test

```
📊 === RISULTATI FINALI ===
Servizi verificati: 8
Servizi con isolamento: 8
Percentuale successo: 100%

🎉 TUTTI I SERVIZI SONO CONFIGURATI CORRETTAMENTE!
L'isolamento delle cliniche dovrebbe funzionare come previsto.
```

## 📧 Invoice Management & Email Features

### Context-Aware Invoice Action Modal

Il sistema di fatturazione presenta un modal sofisticato per le azioni sulle fatture con funzionalità email intelligenti:

#### 🎯 Email State Awareness

- **Fatture Bozza**: Mostra messaggio "Prima invio" con pulsante standard "Invia Email"
- **Fatture Inviate/Pagate**: Banner ambra "La mail è già stata inviata" con pulsante "Invia di Nuovo"
- **Conferma Post-Invio**: Messaggio verde "Email inviata!" dopo invio completato con successo

#### 🛡️ Robust Error Handling

- **Aggiornamenti Ottimistici**: UI aggiornata immediatamente per migliore esperienza utente
- **Rollback Automatico**: Stato ripristinato ai valori precedenti se le operazioni falliscono
- **Notifiche Toast**: Messaggi chiari di successo/errore per tutte le operazioni
- **Stati di Caricamento**: Feedback visivo durante operazioni API

#### 🔄 Smart State Transitions

- **Validazione Stato**: Previene transizioni di stato invalide (es. pagata → inviata)
- **Sincronizzazione Database**: Tutti gli stati UI basati su valori effettivi del database, non flag temporanei
- **Aggiornamenti Real-time**: Lista fatture si aggiorna automaticamente dopo cambio stato

#### 🎨 User Experience

- **Interfaccia Context-Aware**: UI si adatta automaticamente allo stato corrente della fattura
- **Feedback Immediato**: Conferme visive immediate per tutte le azioni dell'utente
- **Gestione Errori Elegante**: Messaggi di errore chiari senza compromettere l'esperienza utente

## 🌐 Demo e Deployment

### 🔗 Live Demo

**Production URL**: [https://gestionale-odontoiatrico.vercel.app](https://gestionale-odontoiatrico.vercel.app)

### 🧪 Test Account

```javascript
// Dentista
email: <EMAIL>
role: dentist

// Amministratore Clinica
email: <EMAIL>
role: clinic
```

### 📱 Responsive Testing

- **Desktop**: Layout completo con sidebar multiple
- **Tablet**: Layout adattivo con drawer collassabile
- **Mobile**: Menu full-featured ottimizzato per touch

## 📚 Documentazione

### 🏗️ Services Layer

- **PatientService**: [/package/src/services/PatientService.ts](./package/src/services/PatientService.ts)
- **InvoiceService**: [/package/src/services/InvoiceService.ts](./package/src/services/InvoiceService.ts)
- **AppointmentService**: [/package/src/services/AppointmentService.ts](./package/src/services/AppointmentService.ts)

### 🗄️ Database Schema

- **Migration Files**: [/supabase/migrations/](./supabase/migrations/)
- **Type Definitions**: [/package/src/types/database.ts](./package/src/types/database.ts)

### 📋 Development Docs

- **TODO Integration**: [/package/TODO-DATABASE-INTEGRATION.md](./package/TODO-DATABASE-INTEGRATION.md)
- **Setup Supabase**: [/package/DATABASE_SETUP.md](./package/DATABASE_SETUP.md)

## 🤝 Contributing

### Workflow Development

1. **Fork** del repository
2. **Clone** locale: `git clone https://github.com/USERNAME/GestionaleOdontoiatrico.git`
3. **Branch feature**: `git checkout -b feature/amazing-feature`
4. **Development**: Modifica, test, commit
5. **Push**: `git push origin feature/amazing-feature`
6. **Pull Request**: Apri PR con descrizione dettagliata

### Code Style

- **ESLint**: `npm run lint`
- **Prettier**: `npm run format`
- **TypeScript**: Strict mode abilitato
- **Commits**: Conventional Commits format (`feat:`, `fix:`, `docs:`, `style:`)

### Database Changes

- **Local Migration**: `npx supabase migration new your_migration_name`
- **Apply Changes**: `npx supabase db reset`
- **Production**: Deploy via Supabase dashboard

## 🔧 Troubleshooting

### 🐳 Problemi Docker/Supabase

**Container con nomi vecchi (Moko-Sostanza)**

```bash
# Reset completo container
docker stop $(docker ps -q --filter "name=supabase")
docker rm $(docker ps -aq --filter "name=supabase")
npx supabase start
```

**Porta già in uso**

```bash
# Controlla processi su porta 54321
netstat -ano | findstr :54321
# Termina processo se necessario
```

**Supabase CLI non trovato**

```bash
# Installa globalmente
npm install -g @supabase/cli
# Oppure usa npx
npx supabase --version
```

### ⚛️ Problemi Frontend

**Errori dipendenze npm**

```bash
# Pulisci cache e reinstalla
npm cache clean --force
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

**Variabili ambiente non caricate**

```bash
# Verifica file .env in /package
cat .env
# Riavvia server dev
npm run dev
```

**Errori TypeScript**

```bash
# Controlla configurazione
npx tsc --noEmit
# Verifica types database
ls -la src/types/database.ts
```

### 👥 Collaborazione e Setup Multi-Sviluppatore

**Collaboratore senza Docker**

Se un membro del team non può usare Docker Desktop:

1. **Supabase Cloud Setup**:

```bash
# Il collaboratore crea account su supabase.com
# Condividi project-id e credenziali via canali sicuri
# Oppure aggiungi come collaboratore al progetto Supabase
```

2. **Configurazione Locale**:

```bash
# Crea .env con credenziali cloud
VITE_SUPABASE_URL=https://YOUR-PROJECT-ID.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Connetti al progetto condiviso
npx supabase login
npx supabase link --project-ref YOUR-PROJECT-ID
```

3. **Sincronizzazione Schema**:

```bash
# Il collaboratore può sincronizzare lo schema
npx supabase db pull
npx supabase db push  # Se ha modifiche da applicare
```

**Condivisione Dati di Test**

Per team che condividono lo stesso dataset:

```bash
# Esporta dati locali (chi ha Docker)
npx supabase db dump --data-only > shared-data.sql

# Importa dati (chi usa Cloud)
psql -h db.YOUR-PROJECT-ID.supabase.co -U postgres -f shared-data.sql
```

### 🗄️ Problemi Database

**Connection refused**

```bash
# Verifica status Supabase
npx supabase status
# Controlla logs
npx supabase logs
```

**Schema non trovato**

```bash
# Applica migrations
npx supabase db reset
# Verifica in Studio
# http://localhost:54323
```

## 📞 Support & Contact

### 🐛 Issue Reporting

- **GitHub Issues**: [Report Bug/Feature Request](https://github.com/daviducciope/GestionaleOdontoiatrico/issues)
- **Discussioni**: [GitHub Discussions](https://github.com/daviducciope/GestionaleOdontoiatrico/discussions)

### 📧 Contact

- **Developer**: David Ucciope
- **Email**: <EMAIL>
- **Repository**: [GestionaleOdontoiatrico](https://github.com/daviducciope/GestionaleOdontoiatrico)
- **Live Demo**: [https://gestionale-odontoiatrico.vercel.app](https://gestionale-odontoiatrico.vercel.app)

## � Quick Links

### 📚 Documentazione

- [Frontend README](./package/README.md) - Setup dettagliato frontend
- [Database Setup](./package/DATABASE_SETUP.md) - Configurazione database locale
- [Development TODO](./package/TODO-DATABASE-INTEGRATION.md) - Stato integrazione
- [**👥 Setup Collaboratori**](./COLLABORATOR_SETUP.md) - **Guida per chi non ha Docker** ⭐

### 🌐 URLs Sviluppo Locale

- **Frontend**: http://localhost:5174
- **Database Studio**: http://localhost:54323
- **API**: http://127.0.0.1:54321

### 🛠️ Repository Structure

```
GestionaleOdontoiatrico/
├── 📁 package/              # Frontend React app
├── 📁 supabase/             # Database config & migrations
├── 📁 src/                  # Additional components
└── 📄 README.md             # Questo file
```

## �📄 License

Questo progetto è distribuito sotto licenza **MIT**. Vedi [LICENSE](./LICENSE) per dettagli.

---

<div align="center">
  <p><strong>🦷 Sviluppato con ❤️ per il settore odontoiatrico</strong></p>
  <p><em>Powered by React + TypeScript + Supabase</em></p>
  
  [🚀 Demo Live](https://gestionale-odontoiatrico.vercel.app) • [📚 Docs](./package/README.md) • [🐛 Issues](https://github.com/daviducciope/GestionaleOdontoiatrico/issues) • [💬 Discussions](https://github.com/daviducciope/GestionaleOdontoiatrico/discussions)
</div>
