-- Migration: <PERSON><PERSON> Policies Part 2 - Remaining tables
-- Created: 2025-07-28 13:01:00
-- Author: AI Assistant

-- Create clinic isolation policies for invoices table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.invoices;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.invoices;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.invoices;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.invoices;

CREATE POLICY "clinic_isolation_select" ON public.invoices
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.invoices
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.invoices
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.invoices
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for reminders table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.reminders;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.reminders;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.reminders;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.reminders;

CREATE POLICY "clinic_isolation_select" ON public.reminders
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.reminders
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.reminders
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.reminders
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for patient_files table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patient_files;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patient_files;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patient_files;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patient_files;

CREATE POLICY "clinic_isolation_select" ON public.patient_files
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.patient_files
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.patient_files
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.patient_files
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for patient_events table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patient_events;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patient_events;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patient_events;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patient_events;

CREATE POLICY "clinic_isolation_select" ON public.patient_events
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.patient_events
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.patient_events
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.patient_events
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for products table (inventory)
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.products;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.products;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.products;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.products;

CREATE POLICY "clinic_isolation_select" ON public.products
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.products
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.products
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.products
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for users table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.users;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.users;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.users;

CREATE POLICY "clinic_isolation_select" ON public.users
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.users
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.users
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for clinics table (users can only see their own clinic)
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.clinics;

CREATE POLICY "clinic_isolation_select" ON public.clinics
  FOR SELECT USING (id = (auth.jwt() ->> 'clinic_id')::uuid);
