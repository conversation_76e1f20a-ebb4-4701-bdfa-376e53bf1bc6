-- Migration: Add clinic_id to secondary tables for multi-tenant isolation
-- Created: 2025-07-28 12:01:00
-- Author: AI Assistant

-- Add clinic_id column to dental_procedures table (if exists)
ALTER TABLE public.dental_procedures
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for dental_procedures clinic isolation
CREATE INDEX IF NOT EXISTS idx_dental_procedures_clinic
  ON public.dental_procedures(clinic_id);

-- Update existing dental_procedures with demo clinic
UPDATE public.dental_procedures
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to doctor_availability table (if exists)
ALTER TABLE public.doctor_availability
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for doctor_availability clinic isolation
CREATE INDEX IF NOT EXISTS idx_doctor_availability_clinic
  ON public.doctor_availability(clinic_id);

-- Update existing doctor_availability with demo clinic
UPDATE public.doctor_availability
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to doctor_specialties table (if exists)
ALTER TABLE public.doctor_specialties
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for doctor_specialties clinic isolation
CREATE INDEX IF NOT EXISTS idx_doctor_specialties_clinic
  ON public.doctor_specialties(clinic_id);

-- Update existing doctor_specialties with demo clinic
UPDATE public.doctor_specialties
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to event_attachments table (if exists)
ALTER TABLE public.event_attachments
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for event_attachments clinic isolation
CREATE INDEX IF NOT EXISTS idx_event_attachments_clinic
  ON public.event_attachments(clinic_id);

-- Update existing event_attachments with demo clinic
UPDATE public.event_attachments
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to invoice_items table (if exists)
ALTER TABLE public.invoice_items
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for invoice_items clinic isolation
CREATE INDEX IF NOT EXISTS idx_invoice_items_clinic
  ON public.invoice_items(clinic_id);

-- Update existing invoice_items with demo clinic
UPDATE public.invoice_items
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.dental_procedures.clinic_id IS 'FK to clinics table - isolates dental procedures per clinic';
COMMENT ON COLUMN public.doctor_availability.clinic_id IS 'FK to clinics table - isolates doctor availability per clinic';
COMMENT ON COLUMN public.doctor_specialties.clinic_id IS 'FK to clinics table - isolates doctor specialties per clinic';
COMMENT ON COLUMN public.event_attachments.clinic_id IS 'FK to clinics table - isolates event attachments per clinic';
COMMENT ON COLUMN public.invoice_items.clinic_id IS 'FK to clinics table - isolates invoice items per clinic';
