-- Force fix delle policy RLS per patients
-- Le policy non sono state aggiornate correttamente via Dashboard
-- Data: 2025-07-28 14:20:00

-- PROBLEMA CONFERMATO:
-- Utenti esistenti funzionano, nuovi utenti falliscono
-- St<PERSON>o JWT claims, ma policy RLS non riconoscono nuovi utenti
-- Le policy per patients usano ancora il path JWT sbagliato

-- SOLUZIONE FORZATA:
-- <PERSON><PERSON><PERSON><PERSON> completamente tutte le policy con path corretto

-- 1. Disabilita temporaneamente RLS per patients (sicurezza)
ALTER TABLE public.patients DISABLE ROW LEVEL SECURITY;

-- 2. Rimuovi TUTTE le policy esistenti per patients
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

-- Rimuovi anche eventuali policy con nomi diversi
DROP POLICY IF EXISTS "patients_clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_delete" ON public.patients;

-- 3. Riabilita RLS per patients
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;

-- 4. Crea policy SELECT con path JWT corretto
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- 5. Crea policy INSERT con path JWT corretto
CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- 6. Crea policy UPDATE con path JWT corretto
CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- 7. Crea policy DELETE con path JWT corretto
CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- 8. Verifica che RLS sia abilitato
SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- 9. Verifica che le policy siano state create
SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
ORDER BY cmd;

-- Log dell'operazione
DO $$
BEGIN
  RAISE NOTICE 'Policy RLS per patients ricreate completamente';
  RAISE NOTICE 'Path JWT corretto: auth.jwt() -> ''app_metadata'' ->> ''clinic_id''';
  RAISE NOTICE 'Tutti gli utenti (esistenti e nuovi) dovrebbero ora funzionare';
END $$;
