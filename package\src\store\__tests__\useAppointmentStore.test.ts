import { renderHook, act } from '@testing-library/react';
import { useAppointmentStore } from '../useAppointmentStore';
import { AppointmentService } from '../../services/AppointmentService';
import type { AppointmentWithDetails } from '../../services/AppointmentService';

// Mock AppointmentService
jest.mock('../../services/AppointmentService', () => ({
  AppointmentService: {
    getAppointmentsByDateRange: jest.fn(),
    createAppointment: jest.fn(),
    updateAppointment: jest.fn(), 
    deleteAppointment: jest.fn(),
    restoreAppointment: jest.fn(),
  }
}));

const createMockAppointment = (id: number, overrides = {}): any => ({
  id,
  patient_id: `patient-${id}`,
  doctor_id: 1,
  treatment_id: 1,
  date: '2024-01-15',
  start_time: '09:00',
  end_time: '10:00',
  status: 'confermato',
  notes: 'Test appointment',
  created_at: '2024-01-01T00:00:00.000Z',
  updated_at: '2024-01-01T00:00:00.000Z',
  // Mock relations
  patient: {
    first_name: '<PERSON>',
    last_name: '<PERSON>'
  },
  doctor: {
    first_name: 'Dr.',
    last_name: 'Smith',
    calendar_color: '#FF0000'
  },
  treatment: {
    name: 'Pulizia',
    duration: 60
  },
  ...overrides
});

describe('useAppointmentStore', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state
    useAppointmentStore.setState({
      appointments: [],
      loading: false,
      error: null
    });
  });

  describe('loadByDateRange', () => {
    it('should load appointments successfully', async () => {
      const mockAppointments = [createMockAppointment(1), createMockAppointment(2)];
      
      (AppointmentService.getAppointmentsByDateRange as jest.Mock)
        .mockResolvedValue(mockAppointments);

      const { result } = renderHook(() => useAppointmentStore());

      await act(async () => {
        await result.current.loadByDateRange('2024-01-01', '2024-01-31');
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.appointments).toEqual(mockAppointments);
      expect(result.current.error).toBeNull();
    });

    it('should handle loading errors', async () => {
      const errorMessage = 'Failed to load appointments';
      (AppointmentService.getAppointmentsByDateRange as jest.Mock)
        .mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useAppointmentStore());

      await act(async () => {
        await result.current.loadByDateRange('2024-01-01', '2024-01-31');
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('create', () => {
    it('should create appointment successfully', async () => {
      const newAppointmentData = {
        patient_id: 'patient-3',
        doctor_id: 1,
        treatment_id: 1,
        date: '2024-01-17',
        start_time: '09:00',
        end_time: '10:00',
        status: 'confermato',
        notes: 'New test appointment'
      };

      const createdAppointment = createMockAppointment(3, newAppointmentData);

      (AppointmentService.createAppointment as jest.Mock)
        .mockResolvedValue(createdAppointment);

      const { result } = renderHook(() => useAppointmentStore());

      await act(async () => {
        await result.current.create(newAppointmentData as any);
      });

      expect(result.current.appointments).toHaveLength(1);
      expect(result.current.appointments[0]).toEqual(createdAppointment);
    });
  });

  describe('softDelete', () => {
    it('should soft delete appointment', async () => {
      const mockAppointment = createMockAppointment(1);
      const deletedAppointment = createMockAppointment(1, {
        deleted_at: '2024-01-15T10:00:00.000Z'
      });

      (AppointmentService.deleteAppointment as jest.Mock)
        .mockResolvedValue(deletedAppointment);

      const { result } = renderHook(() => useAppointmentStore());
      
      // Set initial state
      act(() => {
        useAppointmentStore.setState({ appointments: [mockAppointment] });
      });

      await act(async () => {
        await result.current.softDelete(1);
      });

      // Should be filtered out from appointments list (store handles filtering)
      expect(AppointmentService.deleteAppointment).toHaveBeenCalledWith(1);
    });
  });

  describe('setLoading', () => {
    it('should set loading state', () => {
      const { result } = renderHook(() => useAppointmentStore());

      act(() => {
        result.current.setLoading(true);
      });

      expect(result.current.loading).toBe(true);
    });
  });
});
