-- ===================================================================
-- CREAZIONE CLINICA DEMO - ESECUZIONE DASHBOARD
-- ===================================================================
-- 
-- PROBLEMA IDENTIFICATO:
-- La clinica demo con ID '00000000-0000-0000-0000-000000000000' NON ESISTE
-- Tutti i nuovi utenti vengono assegnati a questa clinica
-- Ma la clinica non esiste, quindi i pazienti non possono essere creati
-- 
-- SOLUZIONE:
-- Creare la clinica demo direttamente nella Dashboard
-- ===================================================================

-- 1. VERIFICA SE LA CLINICA DEMO ESISTE
SELECT 'VERIFICA CLINICA DEMO ESISTENTE' as step;

SELECT 
  id,
  name,
  vat_number,
  city
FROM public.clinics 
WHERE id = '00000000-0000-0000-0000-000000000000';

-- ===================================================================
-- 2. DISABILITA TEMPORANEAMENTE RLS PER CLINICS (SE NECESSARIO)
-- ===================================================================

SELECT 'DISABILITA RLS CLINICS TEMPORANEAMENTE' as step;

-- Verifica se RLS è abilitato per clinics
SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'clinics';

-- Disabilita RLS temporaneamente per permettere inserimento
ALTER TABLE public.clinics DISABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 3. INSERISCI LA CLINICA DEMO
-- ===================================================================

SELECT 'INSERIMENTO CLINICA DEMO' as step;

-- Inserisci la clinica demo con ID fisso
INSERT INTO public.clinics (
  id,
  name,
  vat_number,
  address,
  city,
  postal_code,
  province,
  phone,
  email,
  website,
  description,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000000',
  'Demo Clinic',
  '12345678901',
  'Via Demo 123',
  'Milano',
  '20100',
  'MI',
  '+39 02 1234567',
  '<EMAIL>',
  'https://democlinic.it',
  'Clinica demo per testing e sviluppo del sistema gestionale',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  updated_at = NOW();

-- ===================================================================
-- 4. RIABILITA RLS PER CLINICS
-- ===================================================================

SELECT 'RIABILITA RLS CLINICS' as step;

-- Riabilita RLS per clinics
ALTER TABLE public.clinics ENABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 5. VERIFICA INSERIMENTO
-- ===================================================================

SELECT 'VERIFICA INSERIMENTO COMPLETATO' as step;

-- Verifica che la clinica sia stata creata
SELECT 
  id,
  name,
  vat_number,
  city,
  created_at
FROM public.clinics 
WHERE id = '00000000-0000-0000-0000-000000000000';

-- ===================================================================
-- 6. VERIFICA STATO RLS FINALE
-- ===================================================================

SELECT 'VERIFICA STATO RLS FINALE' as step;

-- Verifica RLS per tutte le tabelle principali
SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('clinics', 'patients', 'users', 'doctors')
ORDER BY tablename;

-- ===================================================================
-- 7. LOG FINALE
-- ===================================================================

DO $$
BEGIN
  RAISE NOTICE '=== CLINICA DEMO CREATA ===';
  RAISE NOTICE 'ID: 00000000-0000-0000-0000-000000000000';
  RAISE NOTICE 'Nome: Demo Clinic';
  RAISE NOTICE 'Tutti i nuovi utenti saranno assegnati a questa clinica';
  RAISE NOTICE '=== TESTA CREAZIONE PAZIENTE ===';
END $$;

-- ===================================================================
-- ISTRUZIONI:
-- ===================================================================
-- 
-- 1. Esegui questo script COMPLETO nella Dashboard
-- 2. Verifica che la clinica demo sia stata creata
-- 3. Testa la registrazione di un nuovo utente
-- 4. Testa la creazione di un paziente
-- 
-- QUESTO SCRIPT:
-- - Crea la clinica demo mancante
-- - Gestisce RLS temporaneamente per permettere inserimento
-- - Verifica che tutto sia configurato correttamente
-- 
-- DOPO L'ESECUZIONE:
-- - Nuovi utenti avranno una clinica valida
-- - Creazione pazienti dovrebbe funzionare
-- - Sistema completamente operativo
-- 
-- ===================================================================
