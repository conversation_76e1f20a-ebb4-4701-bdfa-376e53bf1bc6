import { Button, Label, TextInput, Alert, Textarea } from "flowbite-react";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { supabase } from "../../../lib/supabase";

/**
 * Form di registrazione per titolari di clinica (manager)
 * Crea automaticamente clinica + utente manager con login immediato
 */
const AuthRegister = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    // Dati personali manager
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",

    // Dati clinica
    clinic_name: "",
    clinic_vat_number: "",
    clinic_address: "",
    clinic_city: "",
    clinic_postal_code: "",
    clinic_province: "",
    clinic_phone: "",
    clinic_email: "",
    clinic_description: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    // Validazione campi obbligatori manager
    if (!formData.first_name.trim()) return "Nome è obbligatorio";
    if (!formData.last_name.trim()) return "Cognome è obbligatorio";
    if (!formData.email.trim()) return "Email è obbligatoria";
    if (!formData.phone.trim()) return "Telefono è obbligatorio";
    if (!formData.password.trim()) return "Password è obbligatoria";
    if (formData.password !== formData.confirmPassword) return "Le password non coincidono";

    // Validazione password sicura
    if (formData.password.length < 8) return "Password deve essere di almeno 8 caratteri";
    if (!/\d/.test(formData.password)) return "Password deve contenere almeno un numero";
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(formData.password)) return "Password deve contenere almeno un simbolo";

    // Validazione campi clinica obbligatori
    if (!formData.clinic_name.trim()) return "Nome clinica è obbligatorio";
    if (!formData.clinic_vat_number.trim()) return "P.IVA è obbligatoria";
    if (formData.clinic_vat_number.length !== 11) return "P.IVA deve essere di 11 cifre";
    if (!formData.clinic_address.trim()) return "Indirizzo clinica è obbligatorio";
    if (!formData.clinic_city.trim()) return "Città è obbligatoria";
    if (!formData.clinic_postal_code.trim()) return "CAP è obbligatorio";
    if (!formData.clinic_province.trim()) return "Provincia è obbligatoria";
    if (!formData.clinic_phone.trim()) return "Telefono clinica è obbligatorio";
    if (!formData.clinic_email.trim()) return "Email clinica è obbligatoria";

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('🏥 [RegisterClinic] Starting clinic registration...');

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Registrazione utente con metadata clinica per il trigger
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            // Dati utente per public.users
            first_name: formData.first_name,
            last_name: formData.last_name,
            phone: formData.phone,

            // Dati clinica per public.clinics (trigger li userà)
            clinic_name: formData.clinic_name,
            clinic_vat_number: formData.clinic_vat_number,
            clinic_address: formData.clinic_address,
            clinic_city: formData.clinic_city,
            clinic_postal_code: formData.clinic_postal_code,
            clinic_province: formData.clinic_province,
            clinic_phone: formData.clinic_phone,
            clinic_email: formData.clinic_email,
            clinic_description: formData.clinic_description,
          }
        }
      });

      if (authError) {
        throw authError;
      }

      console.log('✅ [RegisterClinic] User registered successfully:', authData.user?.email);

      // Login automatico già avvenuto con signUp
      // Il trigger ha creato clinica + record utente
      console.log('🔄 [RegisterClinic] Redirecting to dashboard...');

      // Redirect immediato al dashboard
      navigate('/dashboard', { replace: true });

    } catch (error: any) {
      console.error('❌ [RegisterClinic] Registration failed:', error);

      if (error.message?.includes('User already registered')) {
        setError('Email già registrata. Prova ad accedere o usa un\'altra email.');
      } else if (error.message?.includes('Invalid email')) {
        setError('Email non valida. Controlla il formato.');
      } else if (error.message?.includes('Password')) {
        setError('Password non valida. Deve essere di almeno 8 caratteri con numero e simbolo.');
      } else {
        setError(error.message || 'Errore durante la registrazione. Riprova.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-h-[80vh] overflow-y-auto pr-2">
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <Alert color="failure" className="mb-4">
            <span className="font-medium">Errore!</span> {error}
          </Alert>
        )}

        {/* Sezione Dati Manager */}
        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
          <h3 className="text-base font-semibold mb-2 text-gray-900 dark:text-white">
            👤 Dati Amministratore
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <Label htmlFor="first_name" value="Nome *" />
            <TextInput
              id="first_name"
              name="first_name"
              type="text"
              required
              value={formData.first_name}
              onChange={handleInputChange}
              placeholder="Mario"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="last_name" value="Cognome *" />
            <TextInput
              id="last_name"
              name="last_name"
              type="text"
              required
              value={formData.last_name}
              onChange={handleInputChange}
              placeholder="Rossi"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="email" value="Email *" />
            <TextInput
              id="email"
              name="email"
              type="email"
              required
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="phone" value="Telefono *" />
            <TextInput
              id="phone"
              name="phone"
              type="tel"
              required
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="+39 ************"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="password" value="Password *" />
            <TextInput
              id="password"
              name="password"
              type="password"
              required
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Min. 8 caratteri, 1 numero, 1 simbolo"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="confirmPassword" value="Conferma Password *" />
            <TextInput
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              required
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="Ripeti la password"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>
          </div>
        </div>

        {/* Sezione Dati Clinica */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
          <h3 className="text-base font-semibold mb-2 text-gray-900 dark:text-white">
            🏥 Dati Clinica
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="md:col-span-2">
            <Label htmlFor="clinic_name" value="Nome Clinica *" />
            <TextInput
              id="clinic_name"
              name="clinic_name"
              type="text"
              required
              value={formData.clinic_name}
              onChange={handleInputChange}
              placeholder="Studio Dentistico Rossi"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="clinic_vat_number" value="Partita IVA *" />
            <TextInput
              id="clinic_vat_number"
              name="clinic_vat_number"
              type="text"
              required
              maxLength={11}
              value={formData.clinic_vat_number}
              onChange={handleInputChange}
              placeholder="12345678901"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="clinic_phone" value="Telefono Clinica *" />
            <TextInput
              id="clinic_phone"
              name="clinic_phone"
              type="tel"
              required
              value={formData.clinic_phone}
              onChange={handleInputChange}
              placeholder="+39 02 1234567"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="clinic_address" value="Indirizzo *" />
            <TextInput
              id="clinic_address"
              name="clinic_address"
              type="text"
              required
              value={formData.clinic_address}
              onChange={handleInputChange}
              placeholder="Via Roma 123"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="clinic_city" value="Città *" />
            <TextInput
              id="clinic_city"
              name="clinic_city"
              type="text"
              required
              value={formData.clinic_city}
              onChange={handleInputChange}
              placeholder="Milano"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="clinic_postal_code" value="CAP *" />
            <TextInput
              id="clinic_postal_code"
              name="clinic_postal_code"
              type="text"
              required
              maxLength={5}
              value={formData.clinic_postal_code}
              onChange={handleInputChange}
              placeholder="20100"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="clinic_province" value="Provincia *" />
            <TextInput
              id="clinic_province"
              name="clinic_province"
              type="text"
              required
              maxLength={2}
              value={formData.clinic_province}
              onChange={handleInputChange}
              placeholder="MI"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div>
            <Label htmlFor="clinic_email" value="Email Clinica *" />
            <TextInput
              id="clinic_email"
              name="clinic_email"
              type="email"
              required
              value={formData.clinic_email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="clinic_description" value="Descrizione (opzionale)" />
            <Textarea
              id="clinic_description"
              name="clinic_description"
              rows={3}
              value={formData.clinic_description}
              onChange={handleInputChange}
              placeholder="Breve descrizione della clinica..."
              disabled={isLoading}
              className="form-control form-rounded-xl"
            />
          </div>
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          color="primary"
          className="w-full mt-6"
          disabled={isLoading}
        >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creazione account...
          </>
        ) : (
          'Crea Account e Clinica'
        )}
        </Button>

        {/* Link al login */}
        <div className="text-center mt-4">
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Hai già un account?{" "}
            <Link
              to="/auth/login"
              className="text-primary hover:underline"
            >
              Accedi qui
            </Link>
          </p>
        </div>

        {/* Note informative */}
        <div className="border-t pt-3 mt-4">
          <p className="text-xs text-gray-500 text-center">
            🔒 Registrandoti accetti i nostri termini di servizio.
            <br />
            Verrà creata automaticamente la tua clinica e riceverai una email di benvenuto.
          </p>
        </div>
      </form>
    </div>
  );
};

export default AuthRegister;
