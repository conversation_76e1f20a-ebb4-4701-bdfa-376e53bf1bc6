/**
 * MOKO SOSTANZA Dental CRM - Patient Service
 * 
 * Servizio per la gestione dei pazienti
 * Connected to Supabase database
 */

import { supabase } from '../lib/supabase';
import { Database } from '../types/database';
import { withClinicId, getClinicId } from './ServiceUtils';

type Patient = Database['public']['Tables']['patients']['Row'];
type CreatePatientData = Database['public']['Tables']['patients']['Insert'];
type UpdatePatientData = Database['public']['Tables']['patients']['Update'];

// Export types
export { type Patient, type CreatePatientData, type UpdatePatientData };

export interface PatientWithRelations extends Patient {
  appointments?: any[];
  invoices?: any[];
  files?: any[];
  patientUDIs?: any[];
  notifications?: any[];
}


export interface PatientSearchFilters {
  search?: string;
  city?: string;
  isSmoker?: boolean;
  hasAllergies?: boolean;
  dateOfBirthFrom?: Date;
  dateOfBirthTo?: Date;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: keyof Patient;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Servizio per la gestione completa dei pazienti
 */
export class PatientService {
  /**
   * Recupera tutti i pazienti con paginazione e filtri
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getPatients(
    filters: PatientSearchFilters = {},
    pagination: PaginationOptions = {}
  ) {
    const {
      page = 1,
      limit = 20,
      sortBy = 'last_name',
      sortOrder = 'asc'
    } = pagination;

    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();

    let query = supabase.from('patients')
      .select('*')
      .eq('clinic_id', clinicId); // Filtro clinic_id per isolamento

    // Apply search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      query = query.or(`first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,fiscal_code.ilike.%${searchTerm}%`);
    }

    // Apply other filters
    if (filters.city) {
      query = query.ilike('city', `%${filters.city}%`);
    }

    if (filters.isSmoker !== undefined) {
      query = query.eq('is_smoker', filters.isSmoker);
    }

    if (filters.hasAllergies !== undefined) {
      query = filters.hasAllergies ? query.not('allergies', 'is', null) : query.is('allergies', null);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: patients, error, count } = await query;

    if (error) throw error;

    // Get total count for pagination (con filtro clinic_id)
    const { count: totalCount } = await supabase
      .from('patients')
      .select('*', { count: 'exact', head: true })
      .eq('clinic_id', clinicId);

    return {
      patients: patients || [],
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit),
        hasNext: page * limit < (totalCount || 0),
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Recupera un paziente per ID
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getPatientById(id: string, includeRelations = false): Promise<PatientWithRelations | null> {
    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();

    const { data: patient, error } = await supabase
      .from('patients')
      .select('*')
      .eq('id', id)
      .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    if (!includeRelations) {
      return patient;
    }

    // TODO: Load related data when needed
    return {
      ...patient,
      appointments: [],
      invoices: [],
      files: [],
      patientUDIs: [],
      notifications: [],
    };
  }

  /**
   * Crea un nuovo paziente
   */
  static async createPatient(data: CreatePatientData): Promise<Patient> {
    // Inject clinic_id automatically
    const dataWithClinic = await withClinicId(data);
    
    const { data: patient, error } = await supabase
      .from('patients')
      .insert(dataWithClinic)
      .select()
      .single();

    if (error) throw error;
    return patient;
  }

  /**
   * Aggiorna un paziente esistente
   */
  static async updatePatient(id: string, data: UpdatePatientData): Promise<Patient> {
    const { data: patient, error } = await supabase
      .from('patients')
      .update(data)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return patient;
  }

  /**
   * Elimina un paziente
   */
  static async deletePatient(id: string): Promise<void> {
    const { error } = await supabase
      .from('patients')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  /**
   * Cerca pazienti per nome, email o codice fiscale
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async searchPatients(query: string, limit = 10): Promise<Patient[]> {
    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();

    const { data: patients, error } = await supabase
      .from('patients')
      .select('*')
      .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
      .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%,fiscal_code.ilike.%${query}%`)
      .order('last_name')
      .limit(limit);

    if (error) throw error;
    return patients || [];
  }

  /**
   * Verifica se un email è già in uso nella clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async isEmailTaken(email: string, excludeId?: string): Promise<boolean> {
    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();

    let query = supabase.from('patients')
      .select('id')
      .eq('email', email)
      .eq('clinic_id', clinicId); // Filtro clinic_id per isolamento

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;
    if (error) throw error;

    return (data?.length || 0) > 0;
  }

  /**
   * Verifica se un codice fiscale è già in uso nella clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async isFiscalCodeTaken(fiscalCode: string, excludeId?: string): Promise<boolean> {
    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();

    let query = supabase.from('patients')
      .select('id')
      .eq('fiscal_code', fiscalCode)
      .eq('clinic_id', clinicId); // Filtro clinic_id per isolamento

    if (excludeId) {
      query = query.neq('id', excludeId);
    }

    const { data, error } = await query;
    if (error) throw error;

    return (data?.length || 0) > 0;
  }

  /**
   * Ottieni statistiche sui pazienti della clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getPatientStats() {
    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();

    const { count: totalPatients } = await supabase
      .from('patients')
      .select('*', { count: 'exact', head: true })
      .eq('clinic_id', clinicId); // Filtro clinic_id per isolamento

    const { count: smokersCount } = await supabase
      .from('patients')
      .select('*', { count: 'exact', head: true })
      .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
      .eq('is_smoker', true);

    const { count: patientsWithAllergies } = await supabase
      .from('patients')
      .select('*', { count: 'exact', head: true })
      .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
      .not('allergies', 'is', null);

    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    const { count: newPatientsThisMonth } = await supabase
      .from('patients')
      .select('*', { count: 'exact', head: true })
      .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
      .gte('created_at', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`)
      .lt('created_at', `${currentYear}-${(currentMonth + 1).toString().padStart(2, '0')}-01`);

    return {
      totalPatients: totalPatients || 0,
      newPatientsThisMonth: newPatientsThisMonth || 0,
      smokersCount: smokersCount || 0,
      patientsWithAllergies: patientsWithAllergies || 0,
      smokersPercentage: (totalPatients || 0) > 0 ? ((smokersCount || 0) / (totalPatients || 0)) * 100 : 0,
      allergiesPercentage: (totalPatients || 0) > 0 ? ((patientsWithAllergies || 0) / (totalPatients || 0)) * 100 : 0,
    };
  }
}

// Export convenient aliases for the requested API
export const getPatient = PatientService.getPatientById;
export const updatePatient = PatientService.updatePatient;

export default PatientService;
