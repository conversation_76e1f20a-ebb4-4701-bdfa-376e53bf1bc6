-- ===================================================================
-- DIAGNOSI COMPLETA RLS PATIENTS - BLOCCHI A-E
-- ===================================================================
-- Esegui questi comandi UNO ALLA VOLTA e riporta i risultati
-- ===================================================================

-- ===================================================================
-- BLOCCO A – Policy realmente attive
-- ===================================================================

SELECT '=== BLOCCO A.1: Policy correnti sulla tabella patients ===' as info;

SELECT polname,
       permissive,
       roles,
       cmd,
       qual,
       with_check
FROM pg_policies
WHERE schemaname = 'public'
  AND tablename  = 'patients';

SELECT '=== BLOCCO A.2: Stato RLS e owner della tabella ===' as info;

SELECT relrowsecurity,
       relforcerowsecurity,
       relowner
FROM pg_class
WHERE relname = 'patients';

-- ===================================================================
-- BLOCCO C – Test psql con claim simulati (service-role)
-- ===================================================================

SELECT '=== BLOCCO C.1: Test con clinic demo (dovrebbe passare) ===' as info;

-- A) Clinic demo (dovrebbe passare)
SET request.jwt.claims = '{"clinic_id":"00000000-0000-0000-0000-000000000000"}';

INSERT INTO patients(id, first_name, clinic_id)
VALUES (gen_random_uuid(), 'Test-A', '00000000-0000-0000-0000-000000000000');

SELECT '=== BLOCCO C.2: Test con altra clinic (dovrebbe fallire) ===' as info;

-- B) Altra clinic (dovrebbe fallire)
SET request.jwt.claims = '{"clinic_id":"11111111-1111-1111-1111-111111111111"}';

INSERT INTO patients(id, first_name, clinic_id)
VALUES (gen_random_uuid(), 'Test-B', '11111111-1111-1111-1111-111111111111');

-- ===================================================================
-- BLOCCO D – Dati reali in tabella
-- ===================================================================

SELECT '=== BLOCCO D: Ultimi record patients e loro clinic_id ===' as info;

SELECT id, clinic_id, created_at, first_name, last_name
FROM patients
ORDER BY created_at DESC
LIMIT 5;

-- ===================================================================
-- RESET per sicurezza
-- ===================================================================

-- Reset delle impostazioni JWT per evitare interferenze
RESET request.jwt.claims;

SELECT '=== DIAGNOSI COMPLETATA - RIPORTA TUTTI I RISULTATI ===' as info;
