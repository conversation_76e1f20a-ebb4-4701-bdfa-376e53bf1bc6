/**
 * Debug Panel per lo sviluppo - mostra informazioni utili
 */
import React from 'react';
import { useAuth } from '../../hooks/useAuth';

export const DevDebugPanel: React.FC = () => {
  const { user, loading } = useAuth();

  // Solo in modalità sviluppo
  if (!import.meta.env.DEV) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-3 rounded-lg shadow-lg text-xs max-w-xs z-50">
      <div className="font-bold text-yellow-400 mb-2">🚧 DEV MODE</div>
      <div className="space-y-1">
        <div>
          <span className="font-semibold">Auth:</span> {loading ? 'Loading...' : user ? '✅' : '❌'}
        </div>
        {user && (
          <>
            <div>
              <span className="font-semibold">User:</span> {user.email}
            </div>
            <div>
              <span className="font-semibold">Clinic:</span> {user.clinic_id?.slice(0, 8)}...
            </div>
            <div>
              <span className="font-semibold">Role:</span> {user.role}
            </div>
          </>
        )}
        <div className="text-yellow-400 text-xs mt-2">
          Mock user in dev mode
        </div>
      </div>
    </div>
  );
};
