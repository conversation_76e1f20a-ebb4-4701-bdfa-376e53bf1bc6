import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ge, <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { HiArrowLeft, HiPencil, HiTrash } from 'react-icons/hi';
import PageContainer from '../../components/container/PageContainer';
import { DoctorService, DoctorWithRelations } from '../../services/DoctorService';
import { useDoctorStore } from '../../store/useDoctorStore';

const ViewDoctor: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [doctor, setDoctor] = useState<DoctorWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { deleteDoctor } = useDoctorStore();

  useEffect(() => {
    const fetchDoctor = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        setError(null);
        const doctorData = await DoctorService.getDoctorById(parseInt(id));
        setDoctor(doctorData);
      } catch (err) {
        console.error('Errore nel recupero del dottore:', err);
        setError('Errore nel caricamento dei dati del dottore');
      } finally {
        setLoading(false);
      }
    };

    fetchDoctor();
  }, [id]);

  const handleDelete = async () => {
    if (!doctor || !doctor.id) return;
    
    if (window.confirm('Sei sicuro di voler eliminare questo dottore?')) {
      try {
        await deleteDoctor(doctor.id);
        navigate('/clinic/doctors');
      } catch (err) {
        console.error('Errore nell\'eliminazione:', err);
        setError('Errore nell\'eliminazione del dottore');
      }
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('it-IT');
  };

  const formatTime = (timeString: string | null) => {
    if (!timeString) return 'N/A';
    return timeString.slice(0, 5); // HH:MM format
  };

  const getDayName = (dayNumber: number) => {
    const days = ['Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato'];
    return days[dayNumber] || 'N/A';
  };

  if (loading) {
    return (
      <PageContainer title="Caricamento..." description="">
        <div className="flex justify-center items-center h-64">
          <Spinner size="xl" />
        </div>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer title="Errore" description="">
        <Alert color="failure">
          <span className="font-medium">Errore!</span> {error}
        </Alert>
      </PageContainer>
    );
  }

  if (!doctor) {
    return (
      <PageContainer title="Dottore non trovato" description="">
        <Alert color="warning">
          <span className="font-medium">Attenzione!</span> Il dottore richiesto non è stato trovato.
        </Alert>
      </PageContainer>
    );
  }

  return (
    <PageContainer 
      title={`Dott. ${doctor.first_name} ${doctor.last_name}`} 
      description="Dettagli del dottore"
    >
      <div className="flex flex-col gap-6">
        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button color="gray" onClick={() => navigate('/clinic/doctors')}>
            <HiArrowLeft className="mr-2 h-4 w-4" />
            Torna alla lista
          </Button>
          <Button 
            color="blue" 
            onClick={() => navigate(`/clinic/doctors/edit/${doctor.id}`)}
          >
            <HiPencil className="mr-2 h-4 w-4" />
            Modifica
          </Button>
          <Button color="failure" onClick={handleDelete}>
            <HiTrash className="mr-2 h-4 w-4" />
            Elimina
          </Button>
        </div>

        {/* Personal Information */}
        <Card className="shadow-md">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informazioni Personali</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Titolo
                </label>
                <p className="text-gray-900">{doctor.title || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nome Completo
                </label>
                <p className="text-gray-900">{`${doctor.first_name} ${doctor.last_name}`}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Codice Fiscale
                </label>
                <p className="text-gray-900">{doctor.fiscal_code || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data di Nascita
                </label>
                <p className="text-gray-900">{formatDate(doctor.birth_date)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sesso
                </label>
                <p className="text-gray-900">{doctor.sex || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stato
                </label>
                <Badge color={doctor.practice_status ? 'success' : 'failure'}>
                  {doctor.practice_status ? 'Attivo' : 'Inattivo'}
                </Badge>
              </div>
            </div>
          </div>
        </Card>

        {/* Professional Information */}
        <Card className="shadow-md">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informazioni Professionali</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Numero Ordine
                </label>
                <p className="text-gray-900">{doctor.order_number || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Provincia Ordine
                </label>
                <p className="text-gray-900">{doctor.order_province || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data Iscrizione Ordine
                </label>
                <p className="text-gray-900">{formatDate(doctor.order_date)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Codice Albo
                </label>
                <p className="text-gray-900">{doctor.albo_code || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Partita IVA
                </label>
                <p className="text-gray-900">{doctor.vat_id || 'N/A'}</p>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Qualifiche e Titoli
                </label>
                <p className="text-gray-900">{doctor.qualifications || 'Nessuna qualifica specificata'}</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Contact Information */}
        <Card className="shadow-md">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Informazioni di Contatto</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <p className="text-gray-900">{doctor.email || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  PEC
                </label>
                <p className="text-gray-900">{doctor.pec || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Telefono
                </label>
                <p className="text-gray-900">{doctor.phone || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mobile
                </label>
                <p className="text-gray-900">{doctor.mobile || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Indirizzo Residenza
                </label>
                <p className="text-gray-900">{doctor.residence_street || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  CAP Residenza
                </label>
                <p className="text-gray-900">{doctor.residence_cap || 'N/A'}</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Specialties */}
        {doctor.doctor_specialties && doctor.doctor_specialties.length > 0 && (
          <Card className="shadow-md">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Specializzazioni</h3>
              <div className="space-y-3">
                {doctor.doctor_specialties.map((specialty) => (
                  <div key={specialty.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Codice Specialità
                        </label>
                        <p className="text-gray-900">{specialty.specialty_code}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nome Specialità
                        </label>
                        <p className="text-gray-900">{specialty.specialty_name}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Data Specializzazione
                        </label>
                        <p className="text-gray-900">{formatDate(specialty.specialization_date)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}

        {/* Availability */}
        {doctor.doctor_availability && doctor.doctor_availability.length > 0 && (
          <Card className="shadow-md">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Disponibilità</h3>
              <div className="space-y-3">
                {doctor.doctor_availability.map((availability) => (
                  <div key={availability.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Giorno
                        </label>
                        <p className="text-gray-900">{getDayName(availability.day_of_week)}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Orario Inizio
                        </label>
                        <p className="text-gray-900">{formatTime(availability.start_time)}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Orario Fine
                        </label>
                        <p className="text-gray-900">{formatTime(availability.end_time)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}
      </div>
    </PageContainer>
  );
};

export default ViewDoctor;
