/**
 * <PERSON>rea forzatamente la clinica demo mancante
 * Usa l'utente esistente che funziona per creare la clinica
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🏥 === CREAZIONE FORZATA CLINICA DEMO ===\n');

async function createDemoClinicForce() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('1️⃣ Login con utente che funziona...');
    
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123456'
    });
    
    if (signInError) {
      console.log('❌ Errore login:', signInError.message);
      return;
    }
    
    console.log('✅ Login riuscito:', signInData.user.email);
    
    console.log('\n2️⃣ Verifica clinica demo esistente...');
    
    const demoClinicId = '00000000-0000-0000-0000-000000000000';
    
    const { data: existingClinic, error: checkError } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', demoClinicId)
      .maybeSingle();
    
    if (checkError) {
      console.log('❌ Errore verifica clinica:', checkError.message);
    } else if (existingClinic) {
      console.log('✅ Clinica demo già esiste:');
      console.log(`  - ID: ${existingClinic.id}`);
      console.log(`  - Nome: ${existingClinic.name}`);
      console.log('\n3️⃣ Procedo con test finale...');
    } else {
      console.log('⚠️ Clinica demo NON esiste, creazione necessaria...');
      
      console.log('\n3️⃣ Tentativo creazione clinica demo...');
      
      const demoClinicData = {
        id: demoClinicId,
        name: 'Demo Clinic',
        vat_number: '12345678901',
        address: 'Via Demo 123',
        city: 'Milano',
        postal_code: '20100',
        province: 'MI',
        phone: '+39 02 1234567',
        email: '<EMAIL>',
        website: 'https://democlinic.it',
        description: 'Clinica demo per testing e sviluppo'
      };
      
      const { data: newClinic, error: createError } = await supabase
        .from('clinics')
        .insert(demoClinicData)
        .select()
        .single();
      
      if (createError) {
        console.log('❌ Errore creazione clinica:', createError.message);
        console.log('📋 Codice errore:', createError.code);
        
        if (createError.code === '42501') {
          console.log('⚠️ Errore RLS - la tabella clinics ha RLS che blocca inserimento');
          console.log('🔧 Questo potrebbe essere il problema principale!');
        }
      } else {
        console.log('🎉 Clinica demo creata:');
        console.log(`  - ID: ${newClinic.id}`);
        console.log(`  - Nome: ${newClinic.name}`);
      }
    }
    
    console.log('\n4️⃣ Test finale nuovo utente...');
    
    // Logout dall'utente esistente
    await supabase.auth.signOut();
    
    // Registra nuovo utente
    const timestamp = Date.now();
    const testEmail = `final.test.${timestamp}@demo.com`;
    
    console.log(`📧 Registrazione: ${testEmail}`);
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: 'demo123456'
    });
    
    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }
    
    console.log('✅ Nuovo utente registrato');
    
    // Chiama Edge Function
    const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
      body: { user: signUpData.user }
    });
    
    if (functionError) {
      console.log('❌ Edge Function fallita:', functionError.message);
      return;
    }
    
    console.log('✅ Edge Function eseguita:', functionResult);
    
    // Verifica JWT claims
    const { data: { user: finalUser }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !finalUser) {
      console.log('❌ Errore recupero utente:', userError?.message);
      return;
    }
    
    console.log('📋 JWT claims finali:');
    console.log(`  - Clinic ID: ${finalUser.app_metadata?.clinic_id}`);
    console.log(`  - App Role: ${finalUser.app_metadata?.app_role}`);
    
    // Test creazione paziente
    const testPatient = {
      first_name: 'Test',
      last_name: 'FinaleCompleto',
      phone: '+39 ************',
      date_of_birth: '1985-01-01',
      address: 'Via Test Finale',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      anamnesis: 'Test finale completo',
      medical_history: 'None',
      clinic_id: finalUser.app_metadata?.clinic_id || demoClinicId
    };
    
    const { data: newPatient, error: patientError } = await supabase
      .from('patients')
      .insert(testPatient)
      .select()
      .single();
    
    if (patientError) {
      console.log('\n❌ CREAZIONE PAZIENTE ANCORA FALLISCE:');
      console.log(`  - Codice: ${patientError.code}`);
      console.log(`  - Messaggio: ${patientError.message}`);
      console.log(`  - Clinic ID: ${testPatient.clinic_id}`);
      
      console.log('\n🔧 DIAGNOSI FINALE:');
      console.log('1. Clinica demo: ' + (existingClinic ? 'ESISTE' : 'NON ESISTE'));
      console.log('2. JWT claims: ' + (finalUser.app_metadata?.clinic_id ? 'PRESENTI' : 'MANCANTI'));
      console.log('3. Edge Function: FUNZIONA');
      console.log('4. Policy RLS: PROBLEMA PERSISTENTE');
      
      if (patientError.code === '42501') {
        console.log('\n🚨 PROBLEMA CONFERMATO: POLICY RLS NON FUNZIONANO');
        console.log('Le policy potrebbero essere state create ma non attive');
        console.log('Oppure il path JWT è ancora sbagliato');
      }
    } else {
      console.log('\n🎉 SUCCESSO TOTALE!');
      console.log(`  - Paziente creato: ${newPatient.first_name} ${newPatient.last_name}`);
      console.log(`  - ID: ${newPatient.id}`);
      console.log(`  - Clinic ID: ${newPatient.clinic_id}`);
      console.log('\n✅ SISTEMA COMPLETAMENTE FUNZIONALE!');
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

createDemoClinicForce().then(() => {
  console.log('\n🏁 Test finale completato');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
