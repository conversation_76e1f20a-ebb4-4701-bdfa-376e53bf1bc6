/**
 * Test completo RLS con service-role per accesso amministrativo
 * Esegue tutte le verifiche richieste dall'utente
 */

const { createClient } = require('@supabase/supabase-js');

// Configurazione Supabase con service-role key per accesso admin
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://ctfufjfktpbaufwumacq.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

console.log('🔍 === TEST COMPLETO RLS CON SERVICE-ROLE ===\n');

async function main() {
  const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

  console.log('🔧 Usando service-role key per accesso amministrativo completo\n');

  // 1. STATO RLS CON SERVICE-ROLE
  console.log('1. === STATO RLS CON SERVICE-ROLE ===');
  console.log('1.1 Verifica relrowsecurity per patients, doctors, appointments:');
  
  try {
    const { data: rlsStatus, error } = await supabaseAdmin
      .from('pg_class')
      .select('relname, relrowsecurity')
      .in('relname', ['patients', 'doctors', 'appointments']);
    
    if (error) {
      console.log('❌ Errore nel controllo RLS:', error.message);
    } else {
      console.log('✅ Stato RLS per tabelle principali:');
      rlsStatus.forEach(table => {
        const status = table.relrowsecurity ? '✅ ABILITATO (t)' : '❌ DISABILITATO (f)';
        console.log(`   ${table.relname}: ${status}`);
      });
    }
  } catch (error) {
    console.log('❌ Errore nella query RLS:', error.message);
  }

  console.log('\n1.2 Se RLS è disabilitato, la migrazione dovrebbe contenere:');
  console.log('   ALTER TABLE <table> ENABLE ROW LEVEL SECURITY;');
  console.log('   Vedi: 20250728130000_enable_rls_clinic_isolation.sql');

  // 2. DEFINIZIONE COMPLETA DELLE POLICY
  console.log('\n2. === DEFINIZIONE COMPLETA DELLE POLICY ===');
  
  const tables = ['patients', 'doctors', 'appointments'];
  for (const table of tables) {
    console.log(`\n2.${tables.indexOf(table) + 1} Policy per tabella ${table}:`);
    
    try {
      const { data: policies, error } = await supabaseAdmin
        .from('pg_policies')
        .select('policyname, cmd as command, permissive, roles, qual')
        .eq('tablename', table);
      
      if (error) {
        console.log(`❌ Errore nel recupero policy per ${table}:`, error.message);
      } else if (policies && policies.length > 0) {
        policies.forEach(policy => {
          console.log(`   Policy: ${policy.policyname}`);
          console.log(`     Command: ${policy.command}`);
          console.log(`     Permissive: ${policy.permissive}`);
          console.log(`     Roles: ${JSON.stringify(policy.roles)}`);
          console.log(`     Condition: ${policy.qual || 'N/A'}`);
          console.log('');
        });
      } else {
        console.log(`   ❌ PROBLEMA: Nessuna policy trovata per ${table}!`);
        console.log('   La migrazione dovrebbe contenere:');
        console.log(`   CREATE POLICY "clinic_isolation_select" ON ${table}`);
        console.log(`     FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);`);
      }
    } catch (error) {
      console.log(`❌ Errore nell'accesso alle policy per ${table}:`, error.message);
    }
  }

  // 3. TEST "AUTENTICATO" VIA PSQL SIMULATION
  console.log('\n3. === TEST "AUTENTICATO" VIA PSQL SIMULATION ===');
  console.log('3.1 Simulazione con claim clinic_id demo:');
  
  try {
    // Simula l'impostazione del claim JWT
    console.log('   Simulando: SET request.jwt.claims = \'{"clinic_id":"00000000-0000-0000-0000-000000000000"}\';');
    
    // Test query con service role (bypassa RLS)
    const { data: patientsWithClaim, error } = await supabaseAdmin
      .from('patients')
      .select('id, clinic_id, first_name, last_name')
      .limit(3);
    
    if (error) {
      console.log('❌ Errore nella query con claim:', error.message);
    } else {
      console.log('   ✅ Risultati con service-role (bypassa RLS):');
      patientsWithClaim.forEach(p => {
        console.log(`     - ${p.first_name} ${p.last_name} (clinic: ${p.clinic_id || 'NULL'})`);
      });
    }
  } catch (error) {
    console.log('❌ Errore nel test con claim:', error.message);
  }

  console.log('\n3.2 Test con claim di altra clinica:');
  console.log('   Con RLS attivo, questa query dovrebbe restituire 0 risultati');
  console.log('   Simulando: SET request.jwt.claims = \'{"clinic_id":"11111111-1111-1111-1111-111111111111"}\';');

  // 4. BAD DATA - Record con clinic_id NULL
  console.log('\n4. === BAD DATA - RECORD CON CLINIC_ID NULL ===');
  console.log('4.1 Conteggio record con clinic_id IS NULL:');
  
  try {
    const tables = ['patients', 'doctors', 'appointments'];
    for (const table of tables) {
      const { count, error } = await supabaseAdmin
        .from(table)
        .select('*', { count: 'exact', head: true })
        .is('clinic_id', null);
      
      if (error) {
        console.log(`❌ Errore nel conteggio per ${table}:`, error.message);
      } else {
        console.log(`   ${table}: ${count} record con clinic_id NULL`);
      }
    }
  } catch (error) {
    console.log('❌ Errore nel conteggio bad data:', error.message);
  }

  console.log('\n4.2 La migrazione per popolare clinic_id dovrebbe contenere:');
  console.log('   UPDATE patients SET clinic_id = \'00000000-0000-0000-0000-000000000000\' WHERE clinic_id IS NULL;');
  console.log('   Vedi: 20250728120000_add_clinic_id_remaining_tables.sql');

  // 5. EDGE-FUNCTION LOGGING
  console.log('\n5. === EDGE-FUNCTION LOGGING ===');
  console.log('5.1 Per verificare i log della funzione issue-jwt:');
  console.log('   - Vai su Supabase Dashboard > Functions > issue-jwt > Logs');
  console.log('   - Cerca errori durante registrazione/login');
  console.log('   - Verifica che i custom claims vengano creati correttamente');
  console.log('   - Log attesi: "✅ [issue-jwt] Custom <NAME_EMAIL>: {clinic_id: ..., app_role: ...}"');

  // 6. INSERT PATH - Payload JSON effettivo
  console.log('\n6. === INSERT PATH - PAYLOAD JSON EFFETTIVO ===');
  console.log('6.1 Per verificare il payload di creazione paziente:');
  console.log('   - Apri DevTools (F12) > Network tab');
  console.log('   - Crea un nuovo paziente nell\'app');
  console.log('   - Cerca la richiesta POST a /rest/v1/patients');
  console.log('   - Verifica che il payload JSON contenga clinic_id');
  console.log('');
  console.log('   Payload atteso:');
  console.log('   {');
  console.log('     "first_name": "Mario",');
  console.log('     "last_name": "Rossi",');
  console.log('     "clinic_id": "00000000-0000-0000-0000-000000000000",');
  console.log('     ...altri campi');
  console.log('   }');

  // Test finale: Verifica dati esistenti
  console.log('\n🔍 === VERIFICA DATI ESISTENTI ===');
  
  try {
    const { data: allPatients, error } = await supabaseAdmin
      .from('patients')
      .select('id, clinic_id, first_name, last_name')
      .limit(5);
    
    if (error) {
      console.log('❌ Errore nel recupero pazienti:', error.message);
    } else {
      console.log('✅ Primi 5 pazienti nel database:');
      allPatients.forEach(p => {
        const clinicStatus = p.clinic_id ? '✅' : '❌ NULL';
        console.log(`   ${clinicStatus} ${p.first_name} ${p.last_name} (clinic: ${p.clinic_id || 'NULL'})`);
      });
    }
  } catch (error) {
    console.log('❌ Errore nella verifica dati:', error.message);
  }

  console.log('\n🎯 === CONCLUSIONI ===');
  console.log('Problemi identificati da risolvere:');
  console.log('1. Se RLS è disabilitato → Applicare migrazione ENABLE ROW LEVEL SECURITY');
  console.log('2. Se mancano policy → Applicare migrazione CREATE POLICY clinic_isolation');
  console.log('3. Se clinic_id è NULL → Applicare migrazione UPDATE con demo clinic');
  console.log('4. Testare con utente reale autenticato per confermare isolamento');
}

main();
