/**
 * MOKO SOSTANZA Dental CRM - Treatment Store
 * Zustand store per la gestione dei trattamenti
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Database } from '../types/database';

type Treatment = Database['public']['Tables']['treatments']['Row'];

interface TreatmentStore {
  treatments: Treatment[];
  loading: boolean;
  error: string | null;
  load: () => Promise<void>;
}

export const useTreatmentStore = create<TreatmentStore>()(
  devtools(
    (set) => ({
      treatments: [],
      loading: false,
      error: null,

      load: async () => {
        set({ loading: true, error: null });
        
        try {
          // Mock data temporaneo - TODO: sostituire con TreatmentService
          const mockTreatments: Treatment[] = [
            { id: 1, name: 'Visita di controllo', duration: 30, price: 50, category: 'Prevenzione', description: null, created_at: null, updated_at: null },
            { id: 2, name: '<PERSON>uli<PERSON> dentale', duration: 45, price: 80, category: 'Igiene', description: null, created_at: null, updated_at: null },
            { id: 3, name: 'Ottura<PERSON>', duration: 60, price: 120, category: 'Conservativa', description: null, created_at: null, updated_at: null },
            { id: 4, name: 'Estrazione', duration: 45, price: 100, category: 'Chirurgia', description: null, created_at: null, updated_at: null },
            { id: 5, name: 'Devitalizzazione', duration: 90, price: 300, category: 'Endodonzia', description: null, created_at: null, updated_at: null },
            { id: 6, name: 'Impianto dentale', duration: 120, price: 800, category: 'Implantologia', description: null, created_at: null, updated_at: null },
          ];
          
          set({ 
            treatments: mockTreatments, 
            loading: false 
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Errore nel caricamento trattamenti';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw error;
        }
      }
    }),
    {
      name: 'treatment-store'
    }
  )
);

export default useTreatmentStore;
