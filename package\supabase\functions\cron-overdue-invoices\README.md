# MOKO SOSTANZA Dental CRM - Cron Job Configuration

# Configurazione per il job automatico di aggiornamento fatture scadute

## Deployment della Edge Function

1. Deploy della funzione:

```bash
supabase functions deploy cron-overdue-invoices
```

2. Configurazione del cron job nel dashboard Supabase:
   - Vai a Database > Extensions
   - Abilita l'estensione "pg_cron"
   - Esegui questo SQL per configurare il job:

```sql
-- Configura il cron job per eseguire ogni giorno alle 08:00 UTC
SELECT cron.schedule(
  'update-overdue-invoices',
  '0 8 * * *', -- Ogni giorno alle 08:00
  $$
  SELECT net.http_post(
    url := 'https://YOUR_PROJECT_ID.supabase.co/functions/v1/cron-overdue-invoices',
    headers := jsonb_build_object(
      'Content-Type', 'application/json',
      'Authorization', 'Bearer ' || current_setting('app.service_role_key')
    )
  );
  $$
);
```

## Test manuale della funzione

```bash
# Test locale
supabase functions serve cron-overdue-invoices

# Test con curl
curl -X POST "http://localhost:54321/functions/v1/cron-overdue-invoices" \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json"
```

## Logs e Monitoring

- Logs disponibili nel dashboard Supabase > Edge Functions
- La funzione logga il numero di fatture aggiornate
- In caso di errori, vengono loggati dettagli specifici
