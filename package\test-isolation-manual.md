# 🧪 Test Manuale Isolamento Multi-Clinica

## Obiettivo
Verificare che l'isolamento delle cliniche funzioni correttamente con utenti diversi.

## Prerequisiti
- ✅ Build dell'applicazione completato
- ✅ ServiceUtils aggiornato con debug
- ✅ Database con migrazioni applicate
- ✅ Almeno 2 utenti registrati con email diverse

## 🔧 Setup Debug Temporaneo

Ho aggiunto debug avanzato in `ServiceUtils.ts` che:

1. **Logga informazioni dettagliate** su ogni utente
2. **Assegna clinic_id temporaneo** basato sull'email:
   - Email con `clinica-a`, `test1`, `user1` → Clinica A (`11111111-1111-1111-1111-111111111111`)
   - Email con `clinica-b`, `test2`, `user2` → Clinica B (`22222222-2222-2222-2222-222222222222`)
   - <PERSON><PERSON> gli altri → Clinica Demo (`00000000-0000-0000-0000-000000000000`)

## 📋 Procedura di Test

### Passo 1: Preparazione Utenti

**Opzione A: Crea nuovi utenti con email specifiche**
- Registra utente con email: `<EMAIL>` (andrà in Clinica A)
- Registra utente con email: `<EMAIL>` (andrà in Clinica B)

**Opzione B: Usa utenti esistenti**
- Nota le email degli utenti esistenti
- Verifica a quale clinica verranno assegnati secondo le regole sopra

### Passo 2: Test con Primo Utente

1. **Login** con il primo utente (es. `<EMAIL>`)
2. **Apri Console Browser** (F12 → Console)
3. **Naviga alla sezione Pazienti**
4. **Verifica i log** nella console:
   ```
   🔍 [DEBUG] getClinicId - Informazioni utente:
     - User ID: xxx
     - Email: <EMAIL>
     - App Metadata: {}
     - User Metadata: {}
     - Clinic ID estratto: null
   📍 [DEBUG] Assegnato temporaneamente alla Clinica A per test isolamento
   🎯 [DEBUG] Clinic ID finale utilizzato: 11111111-1111-1111-1111-111111111111
   ```
5. **Conta i pazienti** visibili nella lista
6. **Nota i nomi** dei pazienti per confronto

### Passo 3: Test con Secondo Utente

1. **Logout** dal primo utente
2. **Login** con il secondo utente (es. `<EMAIL>`)
3. **Apri Console Browser** (F12 → Console)
4. **Naviga alla sezione Pazienti**
5. **Verifica i log** nella console:
   ```
   🔍 [DEBUG] getClinicId - Informazioni utente:
     - User ID: yyy
     - Email: <EMAIL>
     - App Metadata: {}
     - User Metadata: {}
     - Clinic ID estratto: null
   📍 [DEBUG] Assegnato temporaneamente alla Clinica B per test isolamento
   🎯 [DEBUG] Clinic ID finale utilizzato: 22222222-2222-2222-2222-222222222222
   ```
6. **Conta i pazienti** visibili nella lista
7. **Confronta con il primo utente**

### Passo 4: Verifica Isolamento

**✅ Test PASSATO se:**
- I due utenti vedono **liste di pazienti diverse**
- I log mostrano **clinic_id diversi** per i due utenti
- Non ci sono **errori** nella console
- Le **statistiche dashboard** sono diverse per i due utenti

**❌ Test FALLITO se:**
- I due utenti vedono **gli stessi pazienti**
- I log mostrano **lo stesso clinic_id** per entrambi
- Ci sono **errori** nelle query o nei servizi

### Passo 5: Test Sezioni Aggiuntive

Ripeti il test per altre sezioni:
- **👨‍⚕️ Dottori**: Verifica che ogni utente veda dottori diversi
- **📅 Appuntamenti**: Verifica isolamento appuntamenti
- **💰 Fatture**: Verifica isolamento fatture
- **📊 Dashboard**: Verifica che le statistiche siano diverse

## 🔍 Cosa Cercare nei Log

### Log Normali (Funzionamento Corretto)
```
🔍 [DEBUG] getClinicId - Informazioni utente:
  - User ID: 12345678-1234-1234-1234-123456789012
  - Email: <EMAIL>
  - App Metadata: {}
  - User Metadata: {}
  - Clinic ID estratto: null
📍 [DEBUG] Assegnato temporaneamente alla Clinica A per test isolamento
🎯 [DEBUG] Clinic ID finale utilizzato: 11111111-1111-1111-1111-111111111111
```

### Log Problematici
```
❌ Errore in getClinicId: [errore]
⚠️ [ServiceUtils] No authenticated user, using hardcoded demo clinic for development
⚠️ [ServiceUtils] Could not fetch Demo Clinic due to RLS, using hardcoded ID
```

## 🚨 Problemi Comuni e Soluzioni

### Problema 1: Tutti gli utenti vedono gli stessi dati
**Causa**: RLS non funziona o policy non applicate
**Soluzione**: 
```bash
# Riapplica le migrazioni
npx supabase db reset
```

### Problema 2: Errori "Auth session missing"
**Causa**: Utente non autenticato correttamente
**Soluzione**: 
- Fai logout completo
- Cancella cache browser
- Fai login di nuovo

### Problema 3: Clinic ID sempre uguale
**Causa**: Logica di assegnazione non funziona
**Soluzione**: 
- Verifica che l'email contenga le parole chiave corrette
- Controlla i log per vedere quale branch viene eseguito

### Problema 4: Errori nelle query
**Causa**: Tabelle senza clinic_id o policy RLS mancanti
**Soluzione**: 
- Verifica che le migrazioni siano applicate
- Controlla che tutte le tabelle abbiano clinic_id

## 📊 Risultati Attesi

### Scenario Ideale
- **Utente <EMAIL>**: Vede dati Clinica A (potrebbero essere vuoti se nuova)
- **Utente <EMAIL>**: Vede dati Clinica B (potrebbero essere vuoti se nuova)
- **Altri utenti**: Vedono dati Clinica Demo (dati esistenti)

### Scenario Reale (Prima Implementazione)
- **Tutti gli utenti**: Vedono dati Clinica Demo (perché non hanno clinic_id nei metadati)
- **Con debug temporaneo**: Utenti con email specifiche vedono cliniche diverse

## 🎯 Prossimi Passi

### Se il Test Passa ✅
1. **Rimuovi il debug temporaneo** da ServiceUtils.ts
2. **Implementa assegnazione clinic_id permanente** via dashboard Supabase
3. **Testa con clinic_id reali** nei metadati utente
4. **Documenta la procedura** per assegnare utenti alle cliniche

### Se il Test Fallisce ❌
1. **Analizza i log** per identificare il problema specifico
2. **Verifica le migrazioni** database
3. **Controlla le policy RLS** 
4. **Testa le query dirette** al database
5. **Risolvi i problemi** identificati e riprova

## 📞 Support

Se incontri problemi:
1. **Copia i log completi** dalla console browser
2. **Descrivi il comportamento osservato** vs quello atteso
3. **Indica quale utente** stai usando per il test
4. **Specifica se vedi errori** nella console o nell'interfaccia

---

**🎯 Obiettivo**: Confermare che l'isolamento multi-clinica funziona correttamente prima di procedere con l'implementazione definitiva dei metadati utente.
