import { useState, useEffect } from 'react';
import { Modal, Button, Label, TextInput, Textarea, Select } from 'flowbite-react';
import { Reminder } from '../../services/ReminderService';
import { useReminderStore } from '../../store/useReminderStore';
import { useToast } from '../shared/Toast';

interface ReminderModalProps {
  isOpen: boolean;
  onClose: () => void;
  reminder?: Reminder;
  selectedDate?: string;
  selectedTime?: string;
  patientId?: string; // ID del paziente se è un promemoria specifico per paziente
  onSave?: () => void; // Callback per refreshare i dati
}

const ReminderModal = ({ 
  isOpen, 
  onClose, 
  reminder, 
  selectedDate, 
  selectedTime,
  patientId,
  onSave 
}: ReminderModalProps) => {
  const [formData, setFormData] = useState({
    date: '',
    time: '',
    title: '',
    text: '',
    channel: 'general'
  });
  
  const { createReminder, createPatientReminder, updateReminder, loading, error, clearError } = useReminderStore();
  const { showToast } = useToast();
  
  // Inizializza il form quando si apre il modale
  useEffect(() => {
    if (reminder) {
      // Modifica di un promemoria esistente
      setFormData({
        date: reminder.date,
        time: reminder.time,
        title: reminder.title,
        text: reminder.text,
        channel: reminder.channel || 'general'
      });
    } else {
      // Nuovo promemoria
      const today = new Date().toISOString().split('T')[0];
      setFormData({
        date: selectedDate || today,
        time: selectedTime || '09:00',
        title: '',
        text: '',
        channel: patientId ? 'appointment' : 'general'
      });
    }
    
    // Clear any previous errors when opening the modal
    clearError();
  }, [reminder, selectedDate, selectedTime, patientId, isOpen, clearError]);
  
  // Gestisce i cambiamenti nei campi del form
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Gestisce l'invio del form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    
    try {
      if (reminder) {
        // Aggiorna un promemoria esistente
        await updateReminder(reminder.id, formData);
        showToast('Promemoria aggiornato con successo', 'success');
      } else {
        // Crea un nuovo promemoria
        if (patientId) {
          await createPatientReminder(patientId, formData, formData.channel);
          showToast('Promemoria paziente creato con successo', 'success');
        } else {
          await createReminder(formData);
          showToast('Promemoria creato con successo', 'success');
        }
      }
      
      onSave?.(); // Ricarica i dati
      onClose();
    } catch (err) {
      console.error('Error saving reminder:', err);
      showToast(err instanceof Error ? err.message : 'Errore durante il salvataggio', 'error');
    }
  };
  
  return (
    <Modal show={isOpen} onClose={onClose} size="md">
      <Modal.Header>
        {reminder ? 'Modifica Promemoria' : 'Nuovo Promemoria'}
      </Modal.Header>
      <Modal.Body>
        {error && (
          <div className="mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date" value="Data" />
              <TextInput
                id="date"
                name="date"
                type="date"
                value={formData.date}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="time" value="Ora" />
              <TextInput
                id="time"
                name="time"
                type="time"
                value={formData.time}
                onChange={handleChange}
                required
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="title" value="Titolo" />
            <TextInput
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Inserisci un titolo per il promemoria"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="text" value="Testo" />
            <Textarea
              id="text"
              name="text"
              value={formData.text}
              onChange={handleChange}
              placeholder="Inserisci il testo del promemoria"
              rows={4}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="channel" value="Categoria" />
            <Select
              id="channel"
              name="channel"
              value={formData.channel}
              onChange={handleChange}
              required
            >
              <option value="general">Generale</option>
              <option value="appointment">Appuntamento</option>
              <option value="follow-up">Follow-up</option>
              <option value="treatment">Trattamento</option>
              <option value="email">Email</option>
              <option value="sms">SMS</option>
              <option value="phone">Telefono</option>
            </Select>
          </div>
          
          <div className="flex justify-end gap-4 mt-6">
            <Button color="light" onClick={onClose} disabled={loading}>
              Annulla
            </Button>
            <Button type="submit" color="primary" disabled={loading}>
              {loading ? 'Salvataggio...' : reminder ? 'Aggiorna' : 'Salva'}
            </Button>
          </div>
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default ReminderModal;
