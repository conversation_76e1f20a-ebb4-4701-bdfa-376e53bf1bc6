/**
 * Debug finale delle policy RLS
 * Verifica esattamente cosa non funziona nelle policy
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 === DEBUG FINALE POLICY RLS ===\n');

async function debugRlsPoliciesFinal() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('1️⃣ Test con utente esistente che funziona...');
    
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123456'
    });
    
    if (signInError) {
      console.log('❌ Errore login:', signInError.message);
      return;
    }
    
    console.log('✅ Login riuscito:', signInData.user.email);
    
    const { data: { user: existingUser } } = await supabase.auth.getUser();
    console.log('📋 JWT claims utente esistente:');
    console.log('  - Full app_metadata:', JSON.stringify(existingUser.app_metadata, null, 2));
    console.log('  - Clinic ID:', existingUser.app_metadata?.clinic_id);
    console.log('  - App Role:', existingUser.app_metadata?.app_role);
    
    // Test creazione paziente con utente esistente
    const testPatient1 = {
      first_name: 'Debug',
      last_name: 'UtenteEsistente',
      phone: '+39 ************',
      date_of_birth: '1980-01-01',
      address: 'Via Debug Esistente',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      anamnesis: 'Debug utente esistente',
      medical_history: 'None',
      clinic_id: existingUser.app_metadata?.clinic_id
    };
    
    console.log('\n📝 Dati paziente utente esistente:');
    console.log('  - Clinic ID:', testPatient1.clinic_id);
    
    const { data: patient1, error: error1 } = await supabase
      .from('patients')
      .insert(testPatient1)
      .select()
      .single();
    
    if (error1) {
      console.log('❌ UTENTE ESISTENTE FALLISCE (INASPETTATO):');
      console.log(`  - Codice: ${error1.code}`);
      console.log(`  - Messaggio: ${error1.message}`);
    } else {
      console.log('✅ UTENTE ESISTENTE FUNZIONA:');
      console.log(`  - Paziente: ${patient1.first_name} ${patient1.last_name}`);
      console.log(`  - ID: ${patient1.id}`);
      console.log(`  - Clinic ID: ${patient1.clinic_id}`);
    }
    
    console.log('\n2️⃣ Logout e test nuovo utente...');
    
    await supabase.auth.signOut();
    
    // Registra nuovo utente
    const timestamp = Date.now();
    const newUserEmail = `debug.final.${timestamp}@demo.com`;
    
    console.log(`📧 Registrazione nuovo utente: ${newUserEmail}`);
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: newUserEmail,
      password: 'demo123456'
    });
    
    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }
    
    console.log('✅ Nuovo utente registrato');
    
    // Chiama Edge Function
    const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
      body: { user: signUpData.user }
    });
    
    if (functionError) {
      console.log('❌ Edge Function fallita:', functionError.message);
      return;
    }
    
    console.log('✅ Edge Function eseguita:', functionResult);
    
    // Verifica JWT claims nuovo utente
    const { data: { user: newUser }, error: newUserError } = await supabase.auth.getUser();
    
    if (newUserError || !newUser) {
      console.log('❌ Errore recupero nuovo utente:', newUserError?.message);
      return;
    }
    
    console.log('\n📋 JWT claims nuovo utente:');
    console.log('  - Full app_metadata:', JSON.stringify(newUser.app_metadata, null, 2));
    console.log('  - Clinic ID:', newUser.app_metadata?.clinic_id);
    console.log('  - App Role:', newUser.app_metadata?.app_role);
    
    // Confronto JWT claims
    console.log('\n🔍 CONFRONTO JWT CLAIMS:');
    console.log('  - Utente esistente clinic_id:', existingUser.app_metadata?.clinic_id);
    console.log('  - Nuovo utente clinic_id:', newUser.app_metadata?.clinic_id);
    console.log('  - Sono identici:', existingUser.app_metadata?.clinic_id === newUser.app_metadata?.clinic_id);
    
    // Test creazione paziente con nuovo utente
    const testPatient2 = {
      first_name: 'Debug',
      last_name: 'NuovoUtente',
      phone: '+39 ************',
      date_of_birth: '1985-01-01',
      address: 'Via Debug Nuovo',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      anamnesis: 'Debug nuovo utente',
      medical_history: 'None',
      clinic_id: newUser.app_metadata?.clinic_id
    };
    
    console.log('\n📝 Dati paziente nuovo utente:');
    console.log('  - Clinic ID:', testPatient2.clinic_id);
    console.log('  - Stesso clinic_id dell\'utente esistente:', testPatient2.clinic_id === testPatient1.clinic_id);
    
    const { data: patient2, error: error2 } = await supabase
      .from('patients')
      .insert(testPatient2)
      .select()
      .single();
    
    if (error2) {
      console.log('\n❌ NUOVO UTENTE FALLISCE:');
      console.log(`  - Codice: ${error2.code}`);
      console.log(`  - Messaggio: ${error2.message}`);
      
      console.log('\n🔧 ANALISI DETTAGLIATA:');
      console.log('✅ Clinica demo: ESISTE (creata)');
      console.log('✅ JWT claims: IDENTICI tra utenti');
      console.log('✅ Edge Function: FUNZIONA');
      console.log('✅ Record utente: CREATO');
      console.log('❌ Policy RLS: NON RICONOSCONO NUOVO UTENTE');
      
      console.log('\n🚨 CONCLUSIONE:');
      console.log('Il problema è che le policy RLS per patients');
      console.log('riconoscono solo utenti "vecchi" ma non quelli "nuovi"');
      console.log('Anche se hanno JWT claims identici!');
      
      console.log('\n💡 POSSIBILI CAUSE:');
      console.log('1. Policy create ma non attive per nuovi utenti');
      console.log('2. Cache delle policy non aggiornata');
      console.log('3. Differenza nella struttura JWT tra utenti vecchi/nuovi');
      console.log('4. Policy RLS non applicate correttamente nella Dashboard');
      
    } else {
      console.log('\n🎉 NUOVO UTENTE FUNZIONA!');
      console.log(`  - Paziente: ${patient2.first_name} ${patient2.last_name}`);
      console.log(`  - ID: ${patient2.id}`);
      console.log(`  - Clinic ID: ${patient2.clinic_id}`);
      console.log('\n✅ PROBLEMA RISOLTO COMPLETAMENTE!');
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

debugRlsPoliciesFinal().then(() => {
  console.log('\n🏁 Debug finale completato');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
