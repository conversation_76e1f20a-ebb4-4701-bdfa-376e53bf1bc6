-- Migration: Add clinic_id and soft-delete support to appointments
-- Author: <PERSON><PERSON><PERSON><PERSON> Copilot Assistant
-- Date: 2025-07-20

-- Add clinic_id and deleted_at columns to appointments
ALTER TABLE public.appointments
ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000',
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ;

-- Create composite index for agenda performance
CREATE INDEX IF NOT EXISTS idx_appointments_clinic_date_time 
ON public.appointments (clinic_id, date, start_time) 
WHERE deleted_at IS NULL;

-- Create index for soft-delete queries
CREATE INDEX IF NOT EXISTS idx_appointments_deleted_at 
ON public.appointments (deleted_at);

-- Update existing rows with fallback clinic_id
UPDATE public.appointments 
SET clinic_id = '00000000-0000-0000-0000-000000000000' 
WHERE clinic_id IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.appointments.clinic_id IS 'FK to clinics table - defaults to system clinic for existing records';
COMMENT ON COLUMN public.appointments.deleted_at IS 'Soft delete timestamp - NULL means active record';
