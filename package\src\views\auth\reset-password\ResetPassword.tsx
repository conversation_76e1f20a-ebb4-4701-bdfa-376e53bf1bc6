import { Button, Label, TextInput, Alert } from "flowbite-react";
import { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { supabase } from "../../../lib/supabase";
import FullLogo from "src/layouts/full/shared/logo/FullLogo";

const gradientStyle = {
  background: "linear-gradient(45deg, rgb(238, 119, 82,0.2), rgb(231, 60, 126,0.2), rgb(35, 166, 213,0.2), rgb(35, 213, 171,0.2))",
  backgroundSize: "400% 400%",
  animation: "gradient 15s ease infinite",
  minHeight: "100vh",
};

/**
 * Pagina per reimpostare la password
 * Gestisce il link ricevuto via email
 */
const ResetPassword = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isValidSession, setIsValidSession] = useState(false);

  useEffect(() => {
    // Verifica se l'utente ha una sessione valida (dal link email)
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setIsValidSession(true);
      } else {
        setError('Link di reset non valido o scaduto. Richiedi un nuovo link.');
      }
    };

    checkSession();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validatePassword = () => {
    if (!formData.password.trim()) return "Password è obbligatoria";
    if (formData.password !== formData.confirmPassword) return "Le password non coincidono";
    if (formData.password.length < 8) return "Password deve essere di almeno 8 caratteri";
    if (!/\d/.test(formData.password)) return "Password deve contenere almeno un numero";
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(formData.password)) return "Password deve contenere almeno un simbolo";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log('🔄 [ResetPassword] Starting password update...');
    
    const validationError = validatePassword();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Aggiorna la password
      const { error: updateError } = await supabase.auth.updateUser({
        password: formData.password
      });

      if (updateError) {
        throw updateError;
      }

      console.log('✅ [ResetPassword] Password updated successfully');
      
      // Redirect al login con messaggio di successo
      navigate('/auth/login?message=password-updated');
      
    } catch (error: any) {
      console.error('❌ [ResetPassword] Update failed:', error);
      
      if (error.message?.includes('New password should be different')) {
        setError('La nuova password deve essere diversa da quella precedente.');
      } else if (error.message?.includes('Password')) {
        setError('Password non valida. Deve essere di almeno 8 caratteri con numero e simbolo.');
      } else {
        setError(error.message || 'Errore durante l\'aggiornamento. Riprova.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isValidSession && error) {
    return (
      <div style={gradientStyle} className="relative min-h-screen py-8">
        <div className="flex justify-center items-center px-4 min-h-screen">
          <div className="rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full max-w-md border-none">
            <div className="text-center">
              <div className="mx-auto mb-4">
                <FullLogo />
              </div>
              
              <Alert color="failure" className="mb-4">
                <span className="font-medium">Link non valido!</span>
                <br />
                {error}
              </Alert>

              <div className="space-y-4">
                <Link
                  to="/auth/forgot-password"
                  className="inline-block bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90"
                >
                  Richiedi nuovo link
                </Link>
                
                <div>
                  <Link
                    to="/auth/login"
                    className="text-primary hover:underline"
                  >
                    ← Torna al login
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={gradientStyle} className="relative min-h-screen py-8">
      <div className="flex justify-center items-center px-4 min-h-screen">
        <div className="rounded-xl shadow-md bg-white dark:bg-darkgray p-6 w-full max-w-md border-none">
          <div className="flex flex-col gap-4">
            <div className="mx-auto">
              <FullLogo />
            </div>
            
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Nuova Password
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Inserisci la tua nuova password sicura
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert color="failure">
                  <span className="font-medium">Errore!</span> {error}
                </Alert>
              )}

              <div>
                <Label htmlFor="password" value="Nuova Password" />
                <TextInput
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Min. 8 caratteri, 1 numero, 1 simbolo"
                  disabled={isLoading}
                  className="form-control form-rounded-xl"
                />
              </div>

              <div>
                <Label htmlFor="confirmPassword" value="Conferma Password" />
                <TextInput
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="Ripeti la nuova password"
                  disabled={isLoading}
                  className="form-control form-rounded-xl"
                />
              </div>

              <Button 
                type="submit" 
                color="primary" 
                className="w-full"
                disabled={isLoading || !isValidSession}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Aggiornamento...
                  </>
                ) : (
                  'Aggiorna Password'
                )}
              </Button>

              <div className="text-center">
                <Link
                  to="/auth/login"
                  className="text-sm text-primary hover:underline"
                >
                  ← Torna al login
                </Link>
              </div>

              <div className="border-t pt-4">
                <p className="text-xs text-gray-500 text-center">
                  🔒 La password deve essere sicura per proteggere il tuo account.
                  <br />
                  Dopo l'aggiornamento potrai accedere con la nuova password.
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
