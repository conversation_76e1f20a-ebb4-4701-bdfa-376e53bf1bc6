-- Create patient_files table for OTP/TAC Cone Beam uploads
-- Migration: 20250719210325_create_patient_files.sql

create table if not exists patient_files (
  id uuid primary key default gen_random_uuid(),
  patient_id uuid not null references patients(id) on delete cascade,
  filename text not null,
  path text not null,
  mime_type text not null,
  size integer not null,
  category text default 'document' check (category in ('document', 'image', 'otp', 'tac', 'cone_beam')),
  deleted_at timestamptz,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Create indexes for better performance
create index if not exists idx_patient_files_patient_id on patient_files(patient_id);
create index if not exists idx_patient_files_category on patient_files(category);
create index if not exists idx_patient_files_deleted_at on patient_files(deleted_at);

-- Create updated_at trigger
create or replace function update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

create trigger update_patient_files_updated_at
  before update on patient_files
  for each row
  execute function update_updated_at_column();

-- Enable Row Level Security
alter table patient_files enable row level security;

-- Create RLS policies - Permissive per sviluppo locale
create policy "Allow all operations on patient_files for development" on patient_files
  for all using (true) with check (true);

-- Add comment for documentation
comment on table patient_files is 'Stores file metadata for patient uploads including OTP, TAC, and Cone Beam scans';
comment on column patient_files.category is 'File category: document, image, otp, tac, cone_beam';
comment on column patient_files.deleted_at is 'Soft delete timestamp - NULL means active file';