-- Migration: Add clinic_id to inventory and product tables for multi-tenant isolation
-- Created: 2025-07-28 12:02:00
-- Author: AI Assistant

-- Add clinic_id column to products table (inventory)
ALTER TABLE public.products
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for products clinic isolation
CREATE INDEX IF NOT EXISTS idx_products_clinic
  ON public.products(clinic_id);

-- Update existing products with demo clinic
UPDATE public.products
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add clinic_id column to medical_devices table (if exists)
ALTER TABLE public.medical_devices
  ADD COLUMN IF NOT EXISTS clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Create index for medical_devices clinic isolation
CREATE INDEX IF NOT EXISTS idx_medical_devices_clinic
  ON public.medical_devices(clinic_id);

-- Update existing medical_devices with demo clinic
UPDATE public.medical_devices
  SET clinic_id = '00000000-0000-0000-0000-000000000000'
  WHERE clinic_id IS NULL;

-- Add comments for documentation
COMMENT ON COLUMN public.products.clinic_id IS 'FK to clinics table - isolates inventory products per clinic';
COMMENT ON COLUMN public.medical_devices.clinic_id IS 'FK to clinics table - isolates medical devices per clinic';

-- Create composite indexes for better performance on filtered queries
CREATE INDEX IF NOT EXISTS idx_products_clinic_category
  ON public.products(clinic_id, category);

CREATE INDEX IF NOT EXISTS idx_products_clinic_quantity
  ON public.products(clinic_id, quantity)
  WHERE quantity <= min_quantity; -- For low stock alerts per clinic
