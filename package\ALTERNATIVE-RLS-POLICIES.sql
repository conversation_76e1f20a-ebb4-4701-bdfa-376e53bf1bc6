-- ===================================================================
-- POLICY RLS ALTERNATIVE - DIVERSI PATH JWT
-- ===================================================================
-- 
-- Questo script crea policy con diversi path JWT per trovare quello giusto
-- 
-- ===================================================================

-- STEP 1: Elimina policy esistenti
SELECT 'STEP 1: ELIMINA POLICY ESISTENTI' as step;

DROP POLICY IF EXISTS "patients_rls_select_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_insert_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_update_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_delete_v2" ON public.patients;

-- STEP 2: Crea policy con path JWT alternativo 1 (root level)
SELECT 'STEP 2: POLICY CON PATH ROOT LEVEL' as step;

CREATE POLICY "patients_rls_select_v3" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
  );

CREATE POLICY "patients_rls_insert_v3" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
  );

CREATE POLICY "patients_rls_update_v3" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
  );

CREATE POLICY "patients_rls_delete_v3" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
  );

-- STEP 3: Verifica policy create
SELECT 'STEP 3: VERIFICA POLICY V3 CREATE' as step;

SELECT 
  policyname,
  cmd,
  qual as using_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
  AND policyname LIKE '%_v3'
ORDER BY cmd;

-- STEP 4: Test path JWT (se autenticato)
SELECT 'STEP 4: TEST PATH JWT' as step;

SELECT 
  'Root Path Test' as test,
  auth.jwt() ->> 'clinic_id' as clinic_id_root,
  (auth.jwt() ->> 'clinic_id')::uuid as clinic_id_root_uuid,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_app_metadata,
  (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid as clinic_id_app_metadata_uuid;

SELECT 'STEP 5: POLICY V3 PRONTE PER TEST' as step;

-- ===================================================================
-- ISTRUZIONI DOPO L'ESECUZIONE:
-- 
-- 1. Esegui questo script nel dashboard Supabase
-- 2. Verifica che le 4 policy v3 siano create
-- 3. Torna in VS Code per testare
-- 
-- DIFFERENZA:
-- - Policy v2: usavano auth.jwt() -> 'app_metadata' ->> 'clinic_id'
-- - Policy v3: usano auth.jwt() ->> 'clinic_id' (path diretto)
-- 
-- ===================================================================
