-- Script per creare cliniche di test per l'isolamento multi-tenant
-- Esegui questo script nel dashboard Supabase o tramite SQL Editor

-- Inserisci cliniche di test se non esistono già
INSERT INTO public.clinics (id, name, address, phone, email, created_at, updated_at)
VALUES 
  -- Clinica Demo (dovrebbe già esistere)
  (
    '00000000-0000-0000-0000-000000000000',
    'Clinica Demo',
    'Via Demo 123, Demo City',
    '+39 ************',
    '<EMAIL>',
    NOW(),
    NOW()
  ),
  -- Clinica Test A
  (
    '11111111-1111-1111-1111-111111111111',
    'Clinica Test A',
    'Via Test A 456, Test City A',
    '+39 ************',
    '<EMAIL>',
    NOW(),
    NOW()
  ),
  -- Clinica Test B
  (
    '*************-2222-2222-************',
    'Clinica Test B',
    'Via Test B 789, Test City B',
    '+39 ************',
    '<EMAIL>',
    NOW(),
    NOW()
  )
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  address = EXCLUDED.address,
  phone = EXCLUDED.phone,
  email = EXCLUDED.email,
  updated_at = NOW();

-- Verifica che le cliniche siano state create
SELECT 
  id,
  name,
  address,
  phone,
  email,
  created_at
FROM public.clinics
ORDER BY name;

-- Crea alcuni dati di test per le cliniche A e B (opzionale)

-- Dottori per Clinica A
INSERT INTO public.doctors (id, clinic_id, first_name, last_name, email, phone, specialization, created_at, updated_at)
VALUES 
  (
    gen_random_uuid(),
    '11111111-1111-1111-1111-111111111111',
    'Mario',
    'Rossi',
    '<EMAIL>',
    '+39 ************',
    'Ortodonzia',
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    '11111111-1111-1111-1111-111111111111',
    'Laura',
    'Bianchi',
    '<EMAIL>',
    '+39 ************',
    'Implantologia',
    NOW(),
    NOW()
  )
ON CONFLICT (email) DO NOTHING;

-- Dottori per Clinica B
INSERT INTO public.doctors (id, clinic_id, first_name, last_name, email, phone, specialization, created_at, updated_at)
VALUES 
  (
    gen_random_uuid(),
    '*************-2222-2222-************',
    'Giuseppe',
    'Verdi',
    '<EMAIL>',
    '+39 ************',
    'Endodonzia',
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    '*************-2222-2222-************',
    'Anna',
    'Neri',
    '<EMAIL>',
    '+39 ************',
    'Parodontologia',
    NOW(),
    NOW()
  )
ON CONFLICT (email) DO NOTHING;

-- Pazienti per Clinica A
INSERT INTO public.patients (id, clinic_id, first_name, last_name, email, phone, date_of_birth, created_at, updated_at)
VALUES 
  (
    gen_random_uuid(),
    '11111111-1111-1111-1111-111111111111',
    'Paolo',
    'Rossi',
    '<EMAIL>',
    '+39 ************',
    '1985-03-15',
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    '11111111-1111-1111-1111-111111111111',
    'Maria',
    'Bianchi',
    '<EMAIL>',
    '+39 ************',
    '1990-07-22',
    NOW(),
    NOW()
  )
ON CONFLICT (email) DO NOTHING;

-- Pazienti per Clinica B
INSERT INTO public.patients (id, clinic_id, first_name, last_name, email, phone, date_of_birth, created_at, updated_at)
VALUES 
  (
    gen_random_uuid(),
    '*************-2222-2222-************',
    'Luca',
    'Verdi',
    '<EMAIL>',
    '+39 ************',
    '1988-11-10',
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    '*************-2222-2222-************',
    'Giulia',
    'Neri',
    '<EMAIL>',
    '+39 ************',
    '1992-05-18',
    NOW(),
    NOW()
  )
ON CONFLICT (email) DO NOTHING;

-- Verifica i dati creati
SELECT 
  'Cliniche' as tipo,
  COUNT(*) as totale
FROM public.clinics
UNION ALL
SELECT 
  'Dottori Clinica A' as tipo,
  COUNT(*) as totale
FROM public.doctors 
WHERE clinic_id = '11111111-1111-1111-1111-111111111111'
UNION ALL
SELECT 
  'Dottori Clinica B' as tipo,
  COUNT(*) as totale
FROM public.doctors 
WHERE clinic_id = '*************-2222-2222-************'
UNION ALL
SELECT 
  'Pazienti Clinica A' as tipo,
  COUNT(*) as totale
FROM public.patients 
WHERE clinic_id = '11111111-1111-1111-1111-111111111111'
UNION ALL
SELECT 
  'Pazienti Clinica B' as tipo,
  COUNT(*) as totale
FROM public.patients 
WHERE clinic_id = '*************-2222-2222-************'
UNION ALL
SELECT 
  'Pazienti Clinica Demo' as tipo,
  COUNT(*) as totale
FROM public.patients 
WHERE clinic_id = '00000000-0000-0000-0000-000000000000';

-- Script per assegnare clinic_id agli utenti (da eseguire dopo aver identificato gli utenti)
-- Sostituisci '<EMAIL>' con l'email dell'utente reale

-- Esempio: Assegna utente alla Clinica A
-- UPDATE auth.users 
-- SET user_metadata = jsonb_set(
--   COALESCE(user_metadata, '{}'), 
--   '{clinic_id}', 
--   '"11111111-1111-1111-1111-111111111111"'
-- )
-- WHERE email = '<EMAIL>';

-- Esempio: Assegna utente alla Clinica B
-- UPDATE auth.users 
-- SET user_metadata = jsonb_set(
--   COALESCE(user_metadata, '{}'), 
--   '{clinic_id}', 
--   '"*************-2222-2222-************"'
-- )
-- WHERE email = '<EMAIL>';

-- Verifica assegnazioni utenti
-- SELECT 
--   id,
--   email,
--   user_metadata->>'clinic_id' as clinic_id,
--   created_at
-- FROM auth.users
-- ORDER BY email;
