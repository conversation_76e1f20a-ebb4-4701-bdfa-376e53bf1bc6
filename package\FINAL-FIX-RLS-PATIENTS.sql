-- ===================================================================
-- FIX DEFINITIVO E FINALE - POLICY RLS PATIENTS
-- ===================================================================
-- 
-- PROBLEMA CONFERMATO:
-- - Utenti esistenti funzionano (<EMAIL>)
-- - Nuovi utenti falliscono con stesso JWT claims
-- - Le policy RLS per patients NON sono state aggiornate correttamente
-- 
-- CAUSA:
-- Le policy nella Dashboard usano ancora il path JWT sbagliato
-- 
-- SOLUZIONE DEFINITIVA:
-- R<PERSON><PERSON>re completamente tutte le policy con path corretto
-- ===================================================================

-- 1. VERIFICA STATO ATTUALE
SELECT 'VERIFICA POLICY ATTUALI' as step;

SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
ORDER BY cmd;

-- ===================================================================
-- 2. DISABILITA TEMPORANEAMENTE RLS (SICUREZZA)
-- ===================================================================

SELECT 'DISABILITA RLS TEMPORANEAMENTE' as step;

ALTER TABLE public.patients DISABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 3. RIMUOVI TUTTE LE POLICY ESISTENTI
-- ===================================================================

SELECT 'RIMUOVI TUTTE LE POLICY ESISTENTI' as step;

-- Rimuovi policy con nome standard
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

-- Rimuovi policy con nomi alternativi (se esistono)
DROP POLICY IF EXISTS "patients_clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_delete" ON public.patients;

-- Rimuovi eventuali altre policy
DROP POLICY IF EXISTS "patients_select" ON public.patients;
DROP POLICY IF EXISTS "patients_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_update" ON public.patients;
DROP POLICY IF EXISTS "patients_delete" ON public.patients;

-- ===================================================================
-- 4. RIABILITA RLS
-- ===================================================================

SELECT 'RIABILITA RLS' as step;

ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 5. CREA POLICY CORRETTE CON PATH JWT GIUSTO
-- ===================================================================

SELECT 'CREA POLICY CORRETTE' as step;

-- Policy SELECT: Utenti vedono solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy INSERT: Utenti possono inserire solo pazienti nella propria clinica
CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy UPDATE: Utenti possono aggiornare solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy DELETE: Utenti possono eliminare solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- ===================================================================
-- 6. VERIFICA FINALE
-- ===================================================================

SELECT 'VERIFICA FINALE' as step;

-- Verifica RLS abilitato
SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- Verifica policy create
SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
ORDER BY cmd;

-- ===================================================================
-- 7. TEST JWT CLAIMS (OPZIONALE)
-- ===================================================================

SELECT 'TEST JWT CLAIMS' as step;

-- Mostra JWT claims attuali
SELECT 
  'JWT Test' as test,
  auth.jwt() -> 'app_metadata' as app_metadata,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_from_jwt,
  auth.uid() as user_id;

-- ===================================================================
-- 8. LOG FINALE
-- ===================================================================

DO $$
BEGIN
  RAISE NOTICE '=== FIX RLS PATIENTS COMPLETATO ===';
  RAISE NOTICE 'Tutte le policy RLS per patients ricreate';
  RAISE NOTICE 'Path JWT corretto: auth.jwt() -> ''app_metadata'' ->> ''clinic_id''';
  RAISE NOTICE 'Sia utenti esistenti che nuovi dovrebbero ora funzionare';
  RAISE NOTICE '=== TESTA CREAZIONE PAZIENTE ===';
END $$;

-- ===================================================================
-- ISTRUZIONI FINALI:
-- ===================================================================
-- 
-- 1. Esegui questo script COMPLETO nella Dashboard
-- 2. Verifica che non ci siano errori
-- 3. Testa la creazione di un paziente con un nuovo utente
-- 4. Se funziona, il problema è RISOLTO DEFINITIVAMENTE
-- 
-- QUESTO SCRIPT:
-- - Rimuove TUTTE le policy esistenti per patients
-- - Ricrea le policy con il path JWT corretto
-- - Garantisce che tutti gli utenti funzionino
-- 
-- ===================================================================
