/**
 * <PERSON><PERSON><PERSON> SOSTANZA Dental CRM - New Doctor Form
 * Enhanced form with complete professional fields for FNOMCeO compliance
 */

import React, { useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { Button, Label, TextInput, Select, Textarea, Card } from 'flowbite-react';
import { Icon } from '@iconify/react';
import { useDoctorStore } from '../../store/useDoctorStore';

const NewDoctor = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { createDoctor } = useDoctorStore();

  // Determina se siamo nella sezione clinica o dentista
  const isClinic = location.pathname.startsWith('/clinic');
  const basePath = isClinic ? '/clinic/doctors' : '/doctors';

  const [formData, setFormData] = useState({
    // Anagrafe (obbligatori FNOMCeO)
    title: 'DOTT.',
    first_name: '',
    last_name: '',
    fiscal_code: '',
    birth_date: '',
    sex: '',
    birth_city_code: '',
    citizenship: 'ITALIANA',
    
    // Residenza (obbligatorio)
    residence_street: '',
    residence_cap: '',
    residence_city_code: '',
    
    // Contatti (PEC obbligatoria per legge DL 76/2020)
    pec: '',
    email: '',
    mobile: '',
    phone: '',
    
    // Iscrizione Ordine (obbligatorio)
    order_province: '',
    order_number: '',
    order_date: '',
    albo_code: 'O',
    practice_status: true,
    
    // Dati CRM
    calendar_color: '#1E88E5',
    revenue_share_percentage: 0,
    
    // Dati fiscali (facoltativi)
    vat_id: '',
    iban: '',
    avatar_url: '',
    notes: '',
    
    // Specializzazioni (per ora gestiamo una semplice)
    specialties: [{
      specialty_code: '',
      specialty_name: '',
      specialization_date: ''
    }]
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Filtra le specializzazioni vuote
      const filteredData = {
        ...formData,
        specialties: formData.specialties.filter(s => s.specialty_name.trim() !== '')
      };
      
      await createDoctor(filteredData);
      console.log('✅ Dottore creato con successo!');
      navigate(basePath);
    } catch (error) {
      console.error('❌ Errore nella creazione del dottore:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <h5 className="card-title">Nuovo Medico/Odontoiatra</h5>
          <Link to={basePath} className="text-gray-500 hover:text-primary">
            <Icon icon="solar:arrow-left-linear" height={20} />
            <span className="sr-only">Torna all'elenco dottori</span>
          </Link>
        </div>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {/* Sezione Anagrafe */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Dati Anagrafici</h6>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="title" value="Titolo *" />
              <Select
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
              >
                <option value="DOTT.">DOTT.</option>
                <option value="DOTT.SSA">DOTT.SSA</option>
                <option value="PROF.">PROF.</option>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="first_name" value="Nome *" />
              <TextInput
                id="first_name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                required
                placeholder="Mario"
              />
            </div>
            
            <div>
              <Label htmlFor="last_name" value="Cognome *" />
              <TextInput
                id="last_name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                required
                placeholder="Rossi"
              />
            </div>
            
            <div>
              <Label htmlFor="fiscal_code" value="Codice Fiscale *" />
              <TextInput
                id="fiscal_code"
                name="fiscal_code"
                value={formData.fiscal_code}
                onChange={handleChange}
                required
                maxLength={16}
                placeholder="****************"
                pattern="^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$"
              />
            </div>
            
            <div>
              <Label htmlFor="birth_date" value="Data di Nascita *" />
              <TextInput
                id="birth_date"
                name="birth_date"
                type="date"
                value={formData.birth_date}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="sex" value="Sesso *" />
              <Select
                id="sex"
                name="sex"
                value={formData.sex}
                onChange={handleChange}
                required
              >
                <option value="">Seleziona</option>
                <option value="M">Maschio</option>
                <option value="F">Femmina</option>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="birth_city_code" value="Cod. Comune Nascita *" />
              <TextInput
                id="birth_city_code"
                name="birth_city_code"
                value={formData.birth_city_code}
                onChange={handleChange}
                required
                maxLength={4}
                placeholder="F205"
                pattern="^[A-Z][0-9]{3}$"
              />
            </div>
            
            <div>
              <Label htmlFor="citizenship" value="Cittadinanza" />
              <TextInput
                id="citizenship"
                name="citizenship"
                value={formData.citizenship}
                onChange={handleChange}
                placeholder="ITALIANA"
              />
            </div>
          </div>
        </Card>

        {/* Sezione Residenza */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Residenza</h6>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="residence_street" value="Indirizzo *" />
              <TextInput
                id="residence_street"
                name="residence_street"
                value={formData.residence_street}
                onChange={handleChange}
                required
                placeholder="Via Roma, 123"
              />
            </div>
            
            <div>
              <Label htmlFor="residence_cap" value="CAP *" />
              <TextInput
                id="residence_cap"
                name="residence_cap"
                value={formData.residence_cap}
                onChange={handleChange}
                required
                maxLength={5}
                placeholder="20100"
                pattern="^[0-9]{5}$"
              />
            </div>
            
            <div>
              <Label htmlFor="residence_city_code" value="Cod. Comune Residenza *" />
              <TextInput
                id="residence_city_code"
                name="residence_city_code"
                value={formData.residence_city_code}
                onChange={handleChange}
                required
                maxLength={4}
                placeholder="F205"
                pattern="^[A-Z][0-9]{3}$"
              />
            </div>
          </div>
        </Card>

        {/* Sezione Contatti */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Contatti</h6>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pec" value="PEC (obbligatoria) *" />
              <TextInput
                id="pec"
                name="pec"
                type="email"
                value={formData.pec}
                onChange={handleChange}
                required
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <Label htmlFor="email" value="Email" />
              <TextInput
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <Label htmlFor="mobile" value="Cellulare" />
              <TextInput
                id="mobile"
                name="mobile"
                value={formData.mobile}
                onChange={handleChange}
                placeholder="+39 335 1234567"
              />
            </div>
            
            <div>
              <Label htmlFor="phone" value="Telefono" />
              <TextInput
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="+39 02 1234567"
              />
            </div>
          </div>
        </Card>

        {/* Sezione Ordine Professionale */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Iscrizione Ordine</h6>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="order_province" value="Provincia Ordine *" />
              <TextInput
                id="order_province"
                name="order_province"
                value={formData.order_province}
                onChange={handleChange}
                required
                maxLength={2}
                placeholder="MI"
              />
            </div>
            
            <div>
              <Label htmlFor="order_number" value="Numero Iscrizione *" />
              <TextInput
                id="order_number"
                name="order_number"
                value={formData.order_number}
                onChange={handleChange}
                required
                placeholder="12345"
              />
            </div>
            
            <div>
              <Label htmlFor="order_date" value="Data Iscrizione *" />
              <TextInput
                id="order_date"
                name="order_date"
                type="date"
                value={formData.order_date}
                onChange={handleChange}
                required
              />
            </div>
          </div>
        </Card>

        {/* Sezione CRM */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Dati Operativi</h6>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="calendar_color" value="Colore Calendario" />
              <div className="flex gap-2">
                <TextInput
                  id="calendar_color"
                  name="calendar_color"
                  value={formData.calendar_color}
                  onChange={handleChange}
                  placeholder="#1E88E5"
                />
                <input
                  type="color"
                  value={formData.calendar_color}
                  onChange={handleChange}
                  name="calendar_color"
                  className="w-12 h-10 border rounded"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="revenue_share_percentage" value="% Fatturato" />
              <TextInput
                id="revenue_share_percentage"
                name="revenue_share_percentage"
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={formData.revenue_share_percentage}
                onChange={handleChange}
                placeholder="25.00"
              />
            </div>
          </div>
        </Card>

        {/* Sezione Dati Fiscali */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Dati Fiscali (Opzionali)</h6>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="vat_id" value="Partita IVA" />
              <TextInput
                id="vat_id"
                name="vat_id"
                value={formData.vat_id}
                onChange={handleChange}
                maxLength={11}
                placeholder="12345678901"
                pattern="^[0-9]{11}$"
              />
            </div>
            
            <div>
              <Label htmlFor="iban" value="IBAN" />
              <TextInput
                id="iban"
                name="iban"
                value={formData.iban}
                onChange={handleChange}
                maxLength={34}
                placeholder="***************************"
                pattern="^IT[0-9]{2}[A-Z0-9]{23}$"
              />
            </div>
            
            <div className="md:col-span-2">
              <Label htmlFor="notes" value="Note" />
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                placeholder="Note aggiuntive sul medico..."
              />
            </div>
          </div>
        </Card>

        {/* Pulsanti */}
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            color="gray"
            onClick={() => navigate(basePath)}
            disabled={isSubmitting}
          >
            <Icon icon="solar:arrow-left-linear" className="mr-2" />
            Annulla
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-primary hover:bg-primary/90"
          >
            {isSubmitting ? (
              <>
                <Icon icon="solar:refresh-linear" className="mr-2 animate-spin" />
                Salvataggio...
              </>
            ) : (
              <>
                <Icon icon="solar:diskette-linear" className="mr-2" />
                Salva Medico
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default NewDoctor;
