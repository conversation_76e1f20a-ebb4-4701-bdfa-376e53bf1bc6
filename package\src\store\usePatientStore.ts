import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { getPatient, updatePatient } from '../services/PatientService';
import type { Patient } from '../services/PatientService';

// Debounce utility
const debounce = (func: (...args: any[]) => void, wait: number) => {
  let timeout: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
};

interface PatientState {
  // State
  currentPatientId: string | null;
  udi: string;
  anamnesiSigned: boolean;
  loading: boolean;
  error: string | null;
  
  // Actions
  loadPatient: (id: string) => Promise<void>;
  save: () => Promise<{ success: boolean; message: string }>;
  setUdi: (udi: string) => void;
  setAnamnesiSigned: (signed: boolean) => void;
  toggleAnamnesi: () => void;
  
  // Internal
  _debouncedSave: () => void;
}

export const usePatientStore = create<PatientState>()(
  devtools(
    (set, get) => {
      // Create debounced save function
      const debouncedSave = debounce(async () => {
        const state = get();
        if (state.currentPatientId) {
          try {
            await state.save();
          } catch (error) {
            console.error('Auto-save failed:', error);
          }
        }
      }, 300);

      return {
        // Initial state
        currentPatientId: null,
        udi: '',
        anamnesiSigned: false,
        loading: false,
        error: null,

        // Load patient data
        loadPatient: async (id: string) => {
          set({ loading: true, error: null, currentPatientId: id });
          try {
            const patient = await getPatient(id);
            if (patient) {
              set({ 
                udi: patient.udi ?? '', 
                anamnesiSigned: patient.anamnesi_signed ?? false,
                loading: false
              });
            } else {
              set({ error: 'Patient not found', loading: false });
            }
          } catch (error) {
            console.error('Error loading patient:', error);
            set({ 
              error: error instanceof Error ? error.message : 'Failed to load patient',
              loading: false 
            });
          }
        },

        // Save current state to database
        save: async (): Promise<{ success: boolean; message: string }> => {
          const { currentPatientId, udi, anamnesiSigned } = get();
          console.log('💾 Saving patient:', { currentPatientId, udi, anamnesiSigned });
          
          if (!currentPatientId) {
            console.error('❌ No patient ID provided');
            return { success: false, message: 'No patient selected' };
          }

          try {
            console.log('🔄 Calling updatePatient service...');
            const result = await updatePatient(currentPatientId, { 
              udi, 
              anamnesi_signed: anamnesiSigned 
            });
            console.log('✅ Update successful:', result);
            set({ error: null });
            return { success: true, message: 'Patient data saved successfully' };
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to save patient';
            console.error('❌ Error saving patient:', error);
            set({ error: message });
            return { success: false, message };
          }
        },

        // Set UDI and auto-save
        setUdi: (udi: string) => {
          set({ udi });
          get()._debouncedSave();
        },

        // Set anamnesi signed and auto-save
        setAnamnesiSigned: (anamnesiSigned: boolean) => {
          set({ anamnesiSigned });
          get()._debouncedSave();
        },

        // Toggle anamnesis signed and auto-save
        toggleAnamnesi: () => {
          set((state) => ({ anamnesiSigned: !state.anamnesiSigned }));
          get()._debouncedSave();
        },

        // Internal debounced save
        _debouncedSave: debouncedSave,
      };
    },
    {
      name: 'patient-store', // for devtools
    }
  )
);

export default usePatientStore;
