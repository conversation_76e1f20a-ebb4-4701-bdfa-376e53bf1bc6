-- Migration: Enable Row Level Security with clinic isolation policies
-- Created: 2025-07-28 13:00:00
-- Author: AI Assistant
-- This migration re-enables R<PERSON> and creates comprehensive clinic isolation policies

-- Drop the temporary dev policy
DROP POLICY IF EXISTS "dev_allow_all" ON storage.objects;

-- Enable RLS on all core tables
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patient_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patient_events ENABLE ROW LEVEL SECURITY;

-- Enable RLS on secondary tables
ALTER TABLE public.dental_procedures ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctor_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctor_specialties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;

-- Enable RLS on inventory tables
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medical_devices ENABLE ROW LEVEL SECURITY;

-- Enable RLS on system tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clinics ENABLE ROW LEVEL SECURITY;

-- Create clinic isolation policies for patients table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for doctors table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.doctors;

CREATE POLICY "clinic_isolation_select" ON public.doctors
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.doctors
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.doctors
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.doctors
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for appointments table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.appointments;

CREATE POLICY "clinic_isolation_select" ON public.appointments
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.appointments
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.appointments
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.appointments
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Create clinic isolation policies for treatments table
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.treatments;

CREATE POLICY "clinic_isolation_select" ON public.treatments
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_insert" ON public.treatments
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_update" ON public.treatments
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

CREATE POLICY "clinic_isolation_delete" ON public.treatments
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
