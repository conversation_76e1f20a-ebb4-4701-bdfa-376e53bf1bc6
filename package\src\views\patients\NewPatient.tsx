import { Icon } from "@iconify/react";
import { <PERSON> } from "react-router-dom";
import PatientForm from "../../components/patients/PatientForm";

const NewPatient = () => {
  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <h5 className="card-title">Nuovo Paziente</h5>
            <Link to="/patients" className="text-gray-500 hover:text-primary">
              <Icon icon="solar:arrow-left-linear" height={20} />
              <span className="sr-only">Torna alla lista pazienti</span>
            </Link>
          </div>
        </div>

        <PatientForm />
      </div>
    </>
  );
};

export default NewPatient;
