/**
 * MOKO SOSTANZA Dental CRM - Invoice Store
 * Zustand store per la gestione delle fatture con optimistic updates
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import InvoiceService, { type InvoiceWithDetails, type InvoiceItem } from '../services/InvoiceService';
import type { Database } from '../types/database';

type InvoiceInsert = Database['public']['Tables']['invoices']['Insert'];
type InvoiceUpdate = Database['public']['Tables']['invoices']['Update'];

interface InvoiceStore {
  // State
  invoices: InvoiceWithDetails[];
  loading: boolean;
  error: string | null;

  // Actions
  load: (filters?: any) => Promise<void>;
  create: (invoice: Omit<InvoiceInsert, 'clinic_id' | 'status'>, items?: Omit<InvoiceItem, 'id' | 'invoice_id'>[]) => Promise<InvoiceWithDetails>;
  update: (id: string, patch: InvoiceUpdate) => Promise<InvoiceWithDetails>;
  softDelete: (id: string) => Promise<void>;
  restore: (id: string) => Promise<InvoiceWithDetails>;
  markAsPaid: (id: string, paymentDate: string, paymentMethod: string) => Promise<InvoiceWithDetails>;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useInvoiceStore = create<InvoiceStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      invoices: [],
      loading: false,
      error: null,

      // Load invoices
      load: async (filters = {}) => {
        set({ loading: true, error: null });
        
        try {
          const result = await InvoiceService.getInvoices(filters);
          set({ 
            invoices: result.invoices, 
            loading: false 
          });
        } catch (error) {
          console.error('Store: error loading invoices:', error);
          const errorMessage = error instanceof Error ? error.message : 'Errore nel caricamento fatture';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw error;
        }
      },

      // Create invoice with optimistic update
      create: async (invoiceData, items = []) => {
        const optimisticId = `temp-${Date.now()}`;
        
        // Create base object with all required fields
        const baseInvoice = {
          id: optimisticId,
          invoice_number: `TEMP-${Date.now()}`,
          issue_date: new Date().toISOString().split('T')[0],
          due_date: new Date().toISOString().split('T')[0],
          subtotal: 0,
          tax_rate: 22,
          tax_amount: 0,
          total: 0,
          status: 'draft' as const,
          payment_method: null,
          payment_date: null,
          patient_id: '',
          description: '',
          notes: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          clinic_id: null,
          deleted_at: null,
          sal: null,
          rate_number: null,
        };

        // Merge with provided data, but keep optimistic ID
        const optimisticInvoice: InvoiceWithDetails = {
          ...baseInvoice,
          ...invoiceData,
          id: optimisticId // Always keep the optimistic ID
        };

        // Optimistic update
        const previousInvoices = get().invoices;
        set(state => ({
          invoices: [optimisticInvoice, ...state.invoices],
          loading: false,
          error: null
        }));

        try {
          const newInvoice = items.length > 0 
            ? await InvoiceService.createInvoiceWithItems(invoiceData, items)
            : await InvoiceService.createInvoice(invoiceData);

          // Replace optimistic invoice with real one
          set(state => ({
            invoices: state.invoices.map(inv => 
              inv.id === optimisticId ? newInvoice : inv
            )
          }));

          return newInvoice;
        } catch (error) {
          // Rollback on error
          set({ invoices: previousInvoices });
          
          const errorMessage = error instanceof Error ? error.message : 'Errore nella creazione fattura';
          set({ error: errorMessage });
          throw error;
        }
      },

      // Update invoice with optimistic update
      update: async (id, patch) => {
        const previousInvoices = get().invoices;
        
        // Optimistic update
        set(state => ({
          invoices: state.invoices.map(invoice => 
            invoice.id === id 
              ? { ...invoice, ...patch, updated_at: new Date().toISOString() }
              : invoice
          ),
          error: null
        }));

        try {
          const updatedInvoice = await InvoiceService.updateInvoice(id, patch);
          
          // Replace with server response
          set(state => ({
            invoices: state.invoices.map(inv => 
              inv.id === id ? updatedInvoice : inv
            )
          }));

          return updatedInvoice;
        } catch (error) {
          // Rollback on error
          set({ invoices: previousInvoices });
          
          const errorMessage = error instanceof Error ? error.message : 'Errore aggiornamento fattura';
          set({ error: errorMessage });
          throw error;
        }
      },

      // Soft delete with optimistic update
      softDelete: async (id) => {
        const previousInvoices = get().invoices;
        
        // Optimistic update - remove from list
        set(state => ({
          invoices: state.invoices.filter(invoice => invoice.id !== id),
          error: null
        }));

        try {
          await InvoiceService.softDeleteInvoice(id);
        } catch (error) {
          // Rollback on error
          set({ invoices: previousInvoices });
          
          const errorMessage = error instanceof Error ? error.message : 'Errore eliminazione fattura';
          set({ error: errorMessage });
          throw error;
        }
      },

      // Restore invoice with optimistic update
      restore: async (id) => {
        try {
          const restoredInvoice = await InvoiceService.restoreInvoice(id);
          
          // Add back to list
          set(state => ({
            invoices: [restoredInvoice, ...state.invoices],
            error: null
          }));

          return restoredInvoice;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Errore ripristino fattura';
          set({ error: errorMessage });
          throw error;
        }
      },

      // Mark as paid with optimistic update
      markAsPaid: async (id, paymentDate, paymentMethod) => {
        const previousInvoices = get().invoices;
        
        // Optimistic update
        set(state => ({
          invoices: state.invoices.map(invoice => 
            invoice.id === id 
              ? { 
                  ...invoice, 
                  status: 'paid' as const,
                  payment_date: paymentDate,
                  payment_method: paymentMethod,
                  updated_at: new Date().toISOString()
                }
              : invoice
          ),
          error: null
        }));

        try {
          const updatedInvoice = await InvoiceService.markAsPaid(id, paymentDate, paymentMethod);
          
          // Replace with server response
          set(state => ({
            invoices: state.invoices.map(inv => 
              inv.id === id ? updatedInvoice : inv
            )
          }));

          return updatedInvoice;
        } catch (error) {
          // Rollback on error
          set({ invoices: previousInvoices });
          
          const errorMessage = error instanceof Error ? error.message : 'Errore nel pagamento fattura';
          set({ error: errorMessage });
          throw error;
        }
      },

      // Utility actions
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null })
    }),
    {
      name: 'invoice-store',
      partialize: (state: any) => ({ 
        invoices: state.invoices.slice(0, 50) // Persist only first 50 invoices
      })
    }
  )
);

export default useInvoiceStore;
