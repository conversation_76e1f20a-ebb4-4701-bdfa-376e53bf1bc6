import { useState, useEffect } from 'react';
import { <PERSON>, Badge, Button, TextInput, Alert, Spinner } from 'flowbite-react';
import { Icon } from '@iconify/react';
import { Link, useLocation } from 'react-router-dom';
import { HiSearch, <PERSON>Plus, HiPencil, HiTrash, Hi<PERSON>ye } from 'react-icons/hi';
import { useDoctorStore } from '../../store/useDoctorStore';
import type { DoctorWithRelations } from '../../services/DoctorService';

const Doctors = () => {
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  
  const { 
    doctors, 
    isLoading, 
    error, 
    fetchDoctors, 
    searchDoctors, 
    deleteDoctor,
    clearError 
  } = useDoctorStore();

  // Carica i dottori all'avvio
  useEffect(() => {
    fetchDoctors();
  }, [fetchDoctors]);

  // Gestisce la ricerca
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    if (value.trim()) {
      searchDoctors(value);
    } else {
      fetchDoctors();
    }
  };

  // Gestisce l'eliminazione
  const handleDelete = async (id: number, doctorName: string) => {
    if (window.confirm(`Sei sicuro di voler eliminare il Dr. ${doctorName}?`)) {
      try {
        await deleteDoctor(id);
      } catch (error) {
        // L'errore è già gestito nello store
      }
    }
  };

  // Formatta il nome completo
  const getFullName = (doctor: DoctorWithRelations) => {
    return `${doctor.title} ${doctor.first_name} ${doctor.last_name}`;
  };

  // Formatta le specializzazioni
  const getSpecialties = (doctor: DoctorWithRelations) => {
    if (!doctor.doctor_specialties || doctor.doctor_specialties.length === 0) {
      return 'Nessuna specializzazione';
    }
    return doctor.doctor_specialties.map(s => s.specialty_name).join(', ');
  };

  // Determina il badge di stato
  const getStatusBadge = (doctor: DoctorWithRelations) => {
    if (doctor.deleted_at) {
      return <Badge color="failure">Eliminato</Badge>;
    }
    if (!doctor.practice_status) {
      return <Badge color="warning">Non attivo</Badge>;
    }
    return <Badge color="success">Attivo</Badge>;
  };

  return (
    <div>
      {/* Header con ricerca e azioni */}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestione Dottori
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Gestisci medici e odontoiatri del tuo studio
          </p>
        </div>
        
        <div className="flex flex-col gap-2 sm:flex-row">
          <TextInput
            id="search"
            placeholder="Cerca dottori..."
            value={searchTerm}
            onChange={handleSearch}
            icon={HiSearch}
            className="min-w-[300px]"
          />
          <Link to="/clinic/doctors/new">
            <Button className="whitespace-nowrap">
              <HiPlus className="mr-2 h-4 w-4" />
              Nuovo Dottore
            </Button>
          </Link>
        </div>
      </div>

      {/* Messaggio di errore */}
      {error && (
        <Alert color="failure" className="mb-6">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <button
              onClick={clearError}
              className="text-red-600 hover:text-red-800"
            >
              ×
            </button>
          </div>
        </Alert>
      )}

      {/* Loading spinner */}
      {isLoading && (
        <div className="flex justify-center py-12">
          <Spinner size="xl" />
        </div>
      )}

      {/* Tabella dottori */}
      {!isLoading && (
        <div className="overflow-x-auto">
          <Table hoverable>
            <Table.Head>
              <Table.HeadCell>Nome</Table.HeadCell>
              <Table.HeadCell>Specializzazioni</Table.HeadCell>
              <Table.HeadCell>Contatti</Table.HeadCell>
              <Table.HeadCell>Ordine</Table.HeadCell>
              <Table.HeadCell>Stato</Table.HeadCell>
              <Table.HeadCell>Colore</Table.HeadCell>
              <Table.HeadCell>Azioni</Table.HeadCell>
            </Table.Head>
            <Table.Body className="divide-y">
              {doctors.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center py-12">
                    <div className="text-gray-500">
                      {searchTerm ? (
                        <>
                          <Icon 
                            icon="heroicons:magnifying-glass" 
                            className="mx-auto mb-4 h-12 w-12" 
                          />
                          <p>Nessun dottore trovato per "{searchTerm}"</p>
                        </>
                      ) : (
                        <>
                          <Icon 
                            icon="heroicons:user-plus" 
                            className="mx-auto mb-4 h-12 w-12" 
                          />
                          <p>Nessun dottore registrato</p>
                          <p className="text-sm mt-1">
                            Inizia aggiungendo il primo dottore al tuo studio
                          </p>
                        </>
                      )}
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                doctors.map((doctor) => (
                  <Table.Row key={doctor.id}>
                    <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                      <div>
                        <div className="font-semibold">{getFullName(doctor)}</div>
                        <div className="text-sm text-gray-500">
                          CF: {doctor.fiscal_code}
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="text-sm">
                        {getSpecialties(doctor)}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="text-sm space-y-1">
                        <div className="flex items-center gap-1">
                          <Icon icon="heroicons:envelope" className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">PEC:</span> {doctor.pec}
                        </div>
                        {doctor.email && (
                          <div className="flex items-center gap-1">
                            <Icon icon="heroicons:at-symbol" className="h-4 w-4 text-gray-400" />
                            <span>Email:</span> {doctor.email}
                          </div>
                        )}
                        {doctor.mobile && (
                          <div className="flex items-center gap-1">
                            <Icon icon="heroicons:phone" className="h-4 w-4 text-gray-400" />
                            <span>Mobile:</span> {doctor.mobile}
                          </div>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="text-sm">
                        <div>{doctor.order_province} - {doctor.order_number}</div>
                        <div className="text-gray-500">
                          Iscritto dal {new Date(doctor.order_date).toLocaleDateString()}
                        </div>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      {getStatusBadge(doctor)}
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <div
                          className="h-6 w-6 rounded border-2 border-gray-300"
                          style={{ backgroundColor: doctor.calendar_color }}
                        />
                        <span className="text-xs text-gray-500 font-mono">
                          {doctor.calendar_color}
                        </span>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <Link to={`/clinic/doctors/view/${doctor.id}`}>
                          <Button size="xs" color="light">
                            <HiEye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link to={`/clinic/doctors/edit/${doctor.id}`}>
                          <Button size="xs" color="light">
                            <HiPencil className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          size="xs"
                          color="failure"
                          onClick={() => handleDelete(doctor.id, getFullName(doctor))}
                        >
                          <HiTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      )}

      {/* Stats summary */}
      {doctors.length > 0 && (
        <div className="mt-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
          <div className="flex flex-wrap gap-6 text-sm text-gray-600 dark:text-gray-400">
            <div>
              <span className="font-medium">Totale:</span> {doctors.length} dottori
            </div>
            <div>
              <span className="font-medium">Attivi:</span>{' '}
              {doctors.filter(d => d.practice_status && !d.deleted_at).length}
            </div>
            <div>
              <span className="font-medium">Province:</span>{' '}
              {[...new Set(doctors.map(d => d.order_province))].length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Doctors;
