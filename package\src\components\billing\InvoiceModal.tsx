/**
 * MOKO SOSTANZA Dental CRM - Invoice Modal
 * Modal per creazione/modifica fatture con workflow step-by-step
 */

import { Modal, Button, TextInput, Label, Select, Textarea, Table, Badge, Spinner } from "flowbite-react";
import { Icon } from "@iconify/react";
import { useState, useEffect } from "react";
import { PatientService, type Patient } from "../../services/PatientService";
import { TreatmentService, type Treatment } from "../../services/TreatmentService";
import InvoiceService, { type InvoiceItem } from "../../services/InvoiceService";
import type { Database } from "../../types/database";
import { formatInvoiceStatus, InvoiceStatusConfig } from "../../utils/invoiceStatusUtils";

type InvoiceInsert = Database['public']['Tables']['invoices']['Insert'];

interface InvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoiceToEdit?: any;
  onSave?: () => void; // Callback per ricaricare i dati
}

interface InvoiceFormData {
  patient_id: string;
  issue_date: string;
  due_date: string;
  description: string;
  notes: string;
  status?: string;
  sal?: number;
  rate_number?: number;
}

const InvoiceModal = ({ isOpen, onClose, invoiceToEdit, onSave }: InvoiceModalProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<InvoiceFormData>({
    patient_id: '',
    issue_date: new Date().toISOString().split('T')[0],
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    description: '',
    notes: '',
  });
  const [items, setItems] = useState<Omit<InvoiceItem, 'id' | 'invoice_id'>[]>([]);
  const [selectedTreatment, setSelectedTreatment] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [customPrice, setCustomPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [treatments, setTreatments] = useState<Treatment[]>([]);

  // Stati per l'autocompletamento pazienti (come in AppointmentModal)
  const [patientSearch, setPatientSearch] = useState('');
  const [showPatientSuggestions, setShowPatientSuggestions] = useState(false);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);

  // Carica dati iniziali
  useEffect(() => {
    if (isOpen) {
      const loadData = async () => {
        try {
          const [patientsResult, treatmentsResult] = await Promise.all([
            PatientService.getPatients({}, { limit: 100 }),
            TreatmentService.getTreatments()
          ]);
          setPatients(patientsResult.patients);
          setTreatments(treatmentsResult.treatments);
        } catch (error) {
          console.error('Error loading data:', error);
        }
      };
      
      loadData();
    }
  }, [isOpen]);

  // Reset quando si apre/chiude il modal
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      if (invoiceToEdit) {
        // Populate form with existing invoice data
        setFormData({
          patient_id: invoiceToEdit.patient_id,
          issue_date: invoiceToEdit.issue_date,
          due_date: invoiceToEdit.due_date,
          description: invoiceToEdit.description || '',
          notes: invoiceToEdit.notes || '',
          status: invoiceToEdit.status,
          sal: invoiceToEdit.sal,
          rate_number: invoiceToEdit.rate_number,
        });
        
        // Find patient name for autocomplete
        const patient = patients.find(p => p.id === invoiceToEdit.patient_id);
        if (patient) {
          setPatientSearch(`${patient.first_name} ${patient.last_name}`);
        }
        
        // TODO: Load invoice items if editing
      } else {
        resetForm();
      }
    }
  }, [isOpen, invoiceToEdit, patients]);

  const resetForm = () => {
    setFormData({
      patient_id: '',
      issue_date: new Date().toISOString().split('T')[0],
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      description: '',
      notes: '',
      status: 'draft', // Default status for new invoices
    });
    setItems([]);
    setSelectedTreatment('');
    setQuantity(1);
    setCustomPrice('');
    setPatientSearch('');
    setShowPatientSuggestions(false);
    setFilteredPatients([]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Gestisce la ricerca dei pazienti (come in AppointmentModal)
  const handlePatientSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPatientSearch(value);
    
    if (value.length > 0) {
      const filtered = patients.filter(patient => 
        `${patient.first_name} ${patient.last_name}`.toLowerCase().includes(value.toLowerCase()) ||
        patient.fiscal_code?.toLowerCase().includes(value.toLowerCase()) ||
        patient.phone?.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredPatients(filtered);
      setShowPatientSuggestions(true);
    } else {
      setShowPatientSuggestions(false);
      setFormData(prev => ({ ...prev, patient_id: '' }));
    }
  };

  // Seleziona un paziente dall'autocompletamento
  const handleSelectPatient = (patient: Patient) => {
    setPatientSearch(`${patient.first_name} ${patient.last_name}`);
    setFormData(prev => ({ ...prev, patient_id: patient.id }));
    setShowPatientSuggestions(false);
  };

  // Nasconde i suggerimenti quando si clicca fuori
  const handlePatientInputBlur = () => {
    setTimeout(() => setShowPatientSuggestions(false), 200);
  };

  const addItem = () => {
    if (!selectedTreatment) return;

    const treatment = treatments.find(t => t.id === parseInt(selectedTreatment));
    if (!treatment) return;

    const unitPrice = customPrice ? parseFloat(customPrice) : treatment.price;
    const subtotal = quantity * unitPrice;
    const iva = 22; // Default IVA
    const ivaAmount = (subtotal * iva) / 100;
    const total = subtotal + ivaAmount;

    const newItem: Omit<InvoiceItem, 'id' | 'invoice_id'> = {
      treatment_id: treatment.id,
      quantity,
      unit_price: unitPrice,
      subtotal,
      iva,
      total,
      treatment
    };

    setItems(prev => [...prev, newItem]);
    setSelectedTreatment('');
    setQuantity(1);
    setCustomPrice('');
  };

  const removeItem = (index: number) => {
    setItems(prev => prev.filter((_, i) => i !== index));
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0);
    const taxAmount = items.reduce((sum, item) => sum + (item.subtotal * item.iva / 100), 0);
    const total = subtotal + taxAmount;
    return { subtotal, taxAmount, total };
  };

  const handleSubmit = async () => {
    if (!formData.patient_id) {
      alert('Seleziona un paziente');
      return;
    }

    if (items.length === 0) {
      alert('Aggiungi almeno un trattamento');
      return;
    }

    setLoading(true);
    try {
      const { subtotal, taxAmount, total } = calculateTotals();
      
      if (invoiceToEdit) {
        // Update existing invoice
        const updateData = {
          ...formData,
          subtotal,
          tax_rate: 22,
          tax_amount: taxAmount,
          total
        };

        await InvoiceService.updateInvoice(invoiceToEdit.id, updateData);
        console.log('Invoice updated successfully');
      } else {
        // Create new invoice
        const invoiceData: Omit<InvoiceInsert, 'clinic_id' | 'status'> = {
          ...formData,
          invoice_number: `FAT-${Date.now()}`, // Genera numero fattura temporaneo
          subtotal,
          tax_rate: 22,
          tax_amount: taxAmount,
          total
        };

        await InvoiceService.createInvoiceWithItems(invoiceData, items);
        console.log('Invoice created successfully');
      }
      
      // Callback per ricaricare i dati nel componente padre
      onSave?.();
      // Reset form and close modal
      resetForm();
      onClose();
    } catch (error) {
      console.error('Error saving invoice:', error);
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < 3) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
        1. Seleziona Paziente
      </h3>
      
      <div className="relative">
        <Label htmlFor="patientSearch" value="Paziente *" />
        <TextInput
          id="patientSearch"
          name="patientSearch"
          value={patientSearch}
          onChange={handlePatientSearch}
          onBlur={handlePatientInputBlur}
          onFocus={() => patientSearch.length > 0 && setShowPatientSuggestions(true)}
          placeholder="Digita il nome, cognome, codice fiscale o telefono..."
          autoComplete="off"
          required
        />
        {showPatientSuggestions && filteredPatients.length > 0 && (
          <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
            {filteredPatients.map(patient => (
              <div
                key={patient.id}
                className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                onClick={() => handleSelectPatient(patient)}
              >
                <div className="font-medium">{patient.first_name} {patient.last_name}</div>
                <div className="text-sm text-gray-500">
                  {patient.fiscal_code && `CF: ${patient.fiscal_code} • `}
                  Tel: {patient.phone}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="issue_date" value="Data Emissione *" />
          <TextInput
            type="date"
            id="issue_date"
            name="issue_date"
            value={formData.issue_date}
            onChange={handleInputChange}
            required
          />
        </div>
        <div>
          <Label htmlFor="due_date" value="Data Scadenza *" />
          <TextInput
            type="date"
            id="due_date"
            name="due_date"
            value={formData.due_date}
            onChange={handleInputChange}
            required
          />
        </div>
      </div>

      <div>
        <Label htmlFor="description" value="Descrizione *" />
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          rows={3}
          placeholder="Descrizione della fattura..."
          required
        />
      </div>

      {/* Status field only in edit mode */}
      {invoiceToEdit && (
        <div>
          <Label htmlFor="status" value="Stato *" />
          <Select
            id="status"
            name="status"
            value={formData.status || 'draft'}
            onChange={handleInputChange}
            required
          >
            {Object.entries(InvoiceStatusConfig).map(([key, config]) => (
              <option key={key} value={key}>
                {config.label}
              </option>
            ))}
          </Select>
        </div>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="sal" value="SAL %" />
          <TextInput
            type="number"
            id="sal"
            name="sal"
            value={formData.sal || ''}
            onChange={handleInputChange}
            placeholder="0.00"
            min="0"
            max="100"
            step="0.01"
          />
        </div>
        <div>
          <Label htmlFor="rate_number" value="Rata N." />
          <TextInput
            type="number"
            id="rate_number"
            name="rate_number"
            value={formData.rate_number || ''}
            onChange={handleInputChange}
            placeholder="1"
            min="1"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="notes" value="Note" />
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleInputChange}
          rows={2}
          placeholder="Note aggiuntive..."
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
        2. Aggiungi Trattamenti
      </h3>

      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div className="grid grid-cols-4 gap-4 mb-4">
          <div>
            <Label value="Trattamento" />
            <Select
              value={selectedTreatment}
              onChange={(e) => setSelectedTreatment(e.target.value)}
            >
              <option value="">Seleziona trattamento...</option>
              {treatments.map(treatment => (
                <option key={treatment.id} value={treatment.id}>
                  {treatment.name} - €{treatment.price.toFixed(2)}
                </option>
              ))}
            </Select>
          </div>
          <div>
            <Label value="Quantità" />
            <TextInput
              type="number"
              value={quantity}
              onChange={(e) => setQuantity(parseInt(e.target.value))}
              min="1"
            />
          </div>
          <div>
            <Label value="Prezzo Custom (opzionale)" />
            <TextInput
              type="number"
              value={customPrice}
              onChange={(e) => setCustomPrice(e.target.value)}
              placeholder="Prezzo personalizzato"
              step="0.01"
            />
          </div>
          <div className="flex items-end">
            <Button onClick={addItem} disabled={!selectedTreatment}>
              <Icon icon="heroicons:plus" className="w-4 h-4 mr-1" />
              Aggiungi
            </Button>
          </div>
        </div>
      </div>

      {items.length > 0 && (
        <div className="overflow-x-auto">
          <Table>
            <Table.Head>
              <Table.HeadCell>Trattamento</Table.HeadCell>
              <Table.HeadCell>Qtà</Table.HeadCell>
              <Table.HeadCell>Prezzo Unitario</Table.HeadCell>
              <Table.HeadCell>Subtotale</Table.HeadCell>
              <Table.HeadCell>IVA</Table.HeadCell>
              <Table.HeadCell>Totale</Table.HeadCell>
              <Table.HeadCell>Azioni</Table.HeadCell>
            </Table.Head>
            <Table.Body>
              {items.map((item, index) => (
                <Table.Row key={index}>
                  <Table.Cell>{item.treatment?.name}</Table.Cell>
                  <Table.Cell>{item.quantity}</Table.Cell>
                  <Table.Cell>€{item.unit_price.toFixed(2)}</Table.Cell>
                  <Table.Cell>€{item.subtotal.toFixed(2)}</Table.Cell>
                  <Table.Cell>{item.iva}%</Table.Cell>
                  <Table.Cell>€{item.total.toFixed(2)}</Table.Cell>
                  <Table.Cell>
                    <Button
                      size="sm"
                      color="failure"
                      onClick={() => removeItem(index)}
                    >
                      <Icon icon="heroicons:trash" className="w-4 h-4" />
                    </Button>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      )}
    </div>
  );

  const renderStep3 = () => {
    const { subtotal, taxAmount, total } = calculateTotals();
    
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          3. Riepilogo Fattura
        </h3>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="font-medium mb-2">Paziente</h4>
          <p>{patients.find(p => p.id === formData.patient_id)?.first_name} {patients.find(p => p.id === formData.patient_id)?.last_name}</p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="font-medium mb-2">Date</h4>
          <p>Emissione: {formData.issue_date}</p>
          <p>Scadenza: {formData.due_date}</p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="font-medium mb-2">Totali</h4>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Subtotale:</span>
              <span>€{subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>IVA (22%):</span>
              <span>€{taxAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-1">
              <span>Totale:</span>
              <span>€{total.toFixed(2)}</span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="font-medium mb-2">Trattamenti ({items.length})</h4>
          {items.map((item, index) => (
            <div key={index} className="flex justify-between py-1">
              <span>{item.treatment?.name} x{item.quantity}</span>
              <span>€{item.total.toFixed(2)}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="4xl">
      <Modal.Header>
        <div className="flex items-center space-x-4">
          <span>{invoiceToEdit ? 'Modifica Fattura' : 'Nuova Fattura'}</span>
          <div className="flex space-x-2">
            {[1, 2, 3].map(step => (
              <Badge
                key={step}
                color={currentStep === step ? 'info' : currentStep > step ? 'success' : 'gray'}
              >
                {step}
              </Badge>
            ))}
          </div>
        </div>
      </Modal.Header>

      <Modal.Body>
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
      </Modal.Body>

      <Modal.Footer>
        <div className="flex justify-between w-full">
          <Button
            color="gray"
            onClick={prevStep}
            disabled={currentStep === 1}
          >
            <Icon icon="heroicons:chevron-left" className="w-4 h-4 mr-1" />
            Indietro
          </Button>
          
          <div className="flex space-x-2">
            <Button color="gray" onClick={onClose}>
              Annulla
            </Button>
            
            {currentStep < 3 ? (
              <Button
                onClick={nextStep}
                disabled={currentStep === 1 && !formData.patient_id}
              >
                Avanti
                <Icon icon="heroicons:chevron-right" className="w-4 h-4 ml-1" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={loading || items.length === 0}
              >
                {loading && <Spinner size="sm" className="mr-2" />}
                Crea Fattura
              </Button>
            )}
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
};

export default InvoiceModal;
