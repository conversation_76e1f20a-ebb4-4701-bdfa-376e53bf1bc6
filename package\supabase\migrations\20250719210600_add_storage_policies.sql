-- Add storage policies for patient-files bucket
-- Migration: 20250719210600_add_storage_policies.sql

-- Create a function to manage storage policies (run as supabase_admin)
DO $$
BEGIN
  -- Check if we're running as supabase_admin or similar privileged role
  IF current_user = 'postgres' OR current_user = 'supabase_admin' THEN
    -- Create policies for storage access
    
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view patient files" ON storage.objects;
    DROP POLICY IF EXISTS "Users can upload patient files" ON storage.objects;
    DROP POLICY IF EXISTS "Users can update patient files" ON storage.objects;
    DROP POLICY IF EXISTS "Users can delete patient files" ON storage.objects;

    -- Policy for selecting (viewing) files
    CREATE POLICY "Users can view patient files" ON storage.objects
      FOR SELECT USING (
        bucket_id = 'patient-files'
      );

    -- Policy for inserting (uploading) files
    CREATE POLICY "Users can upload patient files" ON storage.objects
      FOR INSERT WITH CHECK (
        bucket_id = 'patient-files'
      );

    -- Policy for updating files
    CREATE POLICY "Users can update patient files" ON storage.objects
      FOR UPDATE USING (
        bucket_id = 'patient-files'
      );

    -- Policy for deleting files
    CREATE POLICY "Users can delete patient files" ON storage.objects
      FOR DELETE USING (
        bucket_id = 'patient-files'
      );
  END IF;
END $$;
