/**
 * Script per debug in tempo reale del problema isolamento
 * Questo script simula esattamente quello che fa l'applicazione
 */

const { createClient } = require('@supabase/supabase-js');

// Configurazione Supabase (usa le stesse variabili dell'app)
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

console.log('🔍 === DEBUG REAL-TIME ISOLAMENTO ===\n');
console.log('📡 Configurazione Supabase:');
console.log('  - URL:', supabaseUrl);
console.log('  - Key:', supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'NON IMPOSTATA');

async function testCurrentUser() {
  console.log('\n👤 === TEST UTENTE CORRENTE ===');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('❌ Errore nell\'ottenere l\'utente:', error.message);
      console.log('💡 PROBLEMA: L\'utente non è autenticato in questo contesto');
      console.log('💡 SOLUZIONE: Questo script deve essere eseguito dall\'interno dell\'app');
      return null;
    }
    
    if (!user) {
      console.log('❌ Nessun utente autenticato');
      console.log('💡 PROBLEMA: Nessuna sessione attiva');
      return null;
    }
    
    console.log('✅ Utente trovato:');
    console.log('  - ID:', user.id);
    console.log('  - Email:', user.email);
    console.log('  - App Metadata:', JSON.stringify(user.app_metadata, null, 2));
    console.log('  - User Metadata:', JSON.stringify(user.user_metadata, null, 2));
    
    // Simula la logica di getClinicId
    let clinicId = user.app_metadata?.clinic_id || user.user_metadata?.clinic_id;
    console.log('  - Clinic ID dai metadati:', clinicId);
    
    if (!clinicId) {
      console.log('  - ⚠️ Clinic ID non trovato nei metadati');
      
      // Simula la logica temporanea
      if (user.email?.includes('test1') || user.email?.includes('clinica-a')) {
        clinicId = '11111111-1111-1111-1111-111111111111';
        console.log('  - 📍 Assegnazione temporanea: Clinica A');
      } else if (user.email?.includes('test2') || user.email?.includes('clinica-b')) {
        clinicId = '22222222-2222-2222-2222-222222222222';
        console.log('  - 📍 Assegnazione temporanea: Clinica B');
      } else {
        clinicId = '00000000-0000-0000-0000-000000000000';
        console.log('  - 📍 Fallback: Clinica Demo');
      }
    }
    
    console.log('  - 🎯 Clinic ID finale:', clinicId);
    return { user, clinicId };
    
  } catch (error) {
    console.log('❌ Errore nel test utente:', error.message);
    return null;
  }
}

async function testPatientsQuery(clinicId) {
  console.log('\n📊 === TEST QUERY PAZIENTI ===');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Test 1: Query senza filtro clinic_id (per vedere tutti i dati)
    console.log('🔍 Test 1: Query senza filtro clinic_id');
    const { data: allPatients, error: allError } = await supabase
      .from('patients')
      .select('id, first_name, last_name, clinic_id')
      .limit(10);
    
    if (allError) {
      console.log('❌ Errore nella query senza filtro:', allError.message);
    } else {
      console.log(`✅ Pazienti totali visibili: ${allPatients.length}`);
      
      // Raggruppa per clinic_id
      const byClinic = allPatients.reduce((acc, patient) => {
        const cId = patient.clinic_id || 'NULL';
        if (!acc[cId]) acc[cId] = [];
        acc[cId].push(`${patient.first_name} ${patient.last_name}`);
        return acc;
      }, {});
      
      console.log('📋 Distribuzione pazienti per clinic_id:');
      Object.entries(byClinic).forEach(([cId, patients]) => {
        console.log(`  - ${cId}: ${patients.length} pazienti`);
        patients.forEach(name => console.log(`    • ${name}`));
      });
    }
    
    // Test 2: Query con filtro clinic_id
    if (clinicId) {
      console.log(`\n🔍 Test 2: Query con filtro clinic_id = ${clinicId}`);
      const { data: filteredPatients, error: filteredError } = await supabase
        .from('patients')
        .select('id, first_name, last_name, clinic_id')
        .eq('clinic_id', clinicId)
        .limit(10);
      
      if (filteredError) {
        console.log('❌ Errore nella query filtrata:', filteredError.message);
      } else {
        console.log(`✅ Pazienti filtrati per clinic_id: ${filteredPatients.length}`);
        
        if (filteredPatients.length > 0) {
          console.log('📋 Pazienti della clinica corrente:');
          filteredPatients.forEach(patient => {
            console.log(`  • ${patient.first_name} ${patient.last_name} (${patient.clinic_id})`);
          });
        } else {
          console.log('📋 Nessun paziente trovato per questa clinica');
        }
      }
    }
    
  } catch (error) {
    console.log('❌ Errore nel test query pazienti:', error.message);
  }
}

async function testRLSPolicies() {
  console.log('\n🛡️ === TEST POLICY RLS ===');
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Verifica se RLS è abilitato
    const { data: rlsStatus, error: rlsError } = await supabase
      .rpc('check_rls_status');
    
    if (rlsError) {
      console.log('⚠️ Non riesco a verificare lo stato RLS direttamente');
      console.log('💡 Questo è normale, proviamo un approccio diverso');
    }
    
    // Test indiretto: prova a fare una query che dovrebbe essere bloccata da RLS
    console.log('🔍 Test indiretto RLS: Query diretta alla tabella patients');
    
    const { data: directQuery, error: directError } = await supabase
      .from('patients')
      .select('id, clinic_id')
      .limit(1);
    
    if (directError) {
      if (directError.message.includes('RLS') || directError.message.includes('policy')) {
        console.log('✅ RLS sembra essere attivo (query bloccata)');
      } else {
        console.log('❌ Errore nella query diretta:', directError.message);
      }
    } else {
      console.log('⚠️ Query diretta riuscita - RLS potrebbe non essere attivo');
      console.log(`   Risultati ottenuti: ${directQuery.length}`);
    }
    
  } catch (error) {
    console.log('❌ Errore nel test RLS:', error.message);
  }
}

async function suggestSolutions() {
  console.log('\n🔧 === SOLUZIONI SUGGERITE ===');
  
  console.log('Basandomi sui risultati sopra, ecco le possibili cause e soluzioni:\n');
  
  console.log('1. 🚫 PROBLEMA: RLS NON ATTIVO');
  console.log('   Se vedi tutti i pazienti senza filtro:');
  console.log('   - Le policy RLS non sono attive');
  console.log('   - Soluzione: Riapplica le migrazioni RLS');
  console.log('   - Comando: npx supabase db reset\n');
  
  console.log('2. 👤 PROBLEMA: UTENTE NON AUTENTICATO');
  console.log('   Se non vedi informazioni utente:');
  console.log('   - Lo script non può accedere alla sessione');
  console.log('   - Soluzione: Testa direttamente nell\'app con console browser\n');
  
  console.log('3. 🔧 PROBLEMA: SERVIZI NON USANO FILTRO');
  console.log('   Se i servizi non applicano il filtro clinic_id:');
  console.log('   - Verifica che PatientService usi getClinicId()');
  console.log('   - Verifica che aggiunga .eq("clinic_id", clinicId)\n');
  
  console.log('4. 📊 PROBLEMA: DATI TUTTI NELLA STESSA CLINICA');
  console.log('   Se tutti i pazienti hanno lo stesso clinic_id:');
  console.log('   - Crea dati di test per cliniche diverse');
  console.log('   - Usa lo script setup-test-clinics.sql\n');
  
  console.log('💡 PROSSIMO PASSO RACCOMANDATO:');
  console.log('1. Apri l\'app nel browser');
  console.log('2. Fai login con un utente');
  console.log('3. Apri Console Browser (F12)');
  console.log('4. Vai alla sezione Pazienti');
  console.log('5. Cerca i log di debug di ServiceUtils');
  console.log('6. Copia e incolla qui i log che vedi');
}

async function runDebug() {
  console.log('🚀 Iniziando debug real-time...\n');
  
  const userInfo = await testCurrentUser();
  
  if (userInfo) {
    await testPatientsQuery(userInfo.clinicId);
  } else {
    console.log('\n⚠️ Non posso testare le query senza utente autenticato');
    console.log('💡 Questo script deve essere eseguito dall\'interno dell\'app o con sessione attiva');
  }
  
  await testRLSPolicies();
  await suggestSolutions();
  
  console.log('\n🏁 === DEBUG COMPLETATO ===');
  console.log('Analizza i risultati e segui le soluzioni suggerite.');
}

// Esegui il debug
runDebug().catch(error => {
  console.error('❌ Errore durante il debug:', error);
  process.exit(1);
});
