# 🏥 Documentazione Tecnica - Isolamento Multi-Clinica

## Panoramica

Il sistema **Gestionale Odontoiatrico** implementa un'architettura multi-tenant completa che garantisce l'isolamento totale dei dati tra diverse cliniche odontoiatriche. Questa documentazione fornisce dettagli tecnici sull'implementazione, le best practice e le procedure di testing.

## Indice

- [Architettura Multi-Tenant](#architettura-multi-tenant)
- [Schema Database](#schema-database)
- [Row Level Security (RLS)](#row-level-security-rls)
- [Implementazione Servizi](#implementazione-servizi)
- [Testing e Verifica](#testing-e-verifica)
- [Troubleshooting](#troubleshooting)
- [Best Practice](#best-practice)

## Architettura Multi-Tenant

### Approccio Utilizzato

Il sistema utilizza un approccio **multi-tenant con database condiviso e isolamento a livello di riga**:

- **Database Condiviso**: Un singolo database PostgreSQL per tutte le cliniche
- **Isolamento per Riga**: Ogni record include un `clinic_id` per identificare la clinica proprietaria
- **Row Level Security**: Policy PostgreSQL automatiche per filtrare i dati
- **Contesto Utente**: Il `clinic_id` viene estratto dal JWT dell'utente autenticato

### Vantaggi

✅ **Sicurezza**: Isolamento garantito a livello database  
✅ **Performance**: Condivisione risorse e ottimizzazioni  
✅ **Manutenzione**: Un'unica istanza dell'applicazione  
✅ **Scalabilità**: Facile aggiunta di nuove cliniche  
✅ **Costi**: Riduzione dei costi infrastrutturali

## Schema Database

### Tabelle con Isolamento Clinic_ID

Tutte le tabelle principali del sistema includono una colonna `clinic_id` di tipo `UUID` che referenzia la tabella `clinics`:

#### Tabelle Core
```sql
-- Pazienti
ALTER TABLE public.patients 
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Dottori  
ALTER TABLE public.doctors
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Appuntamenti
ALTER TABLE public.appointments
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Trattamenti
ALTER TABLE public.treatments
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Fatture
ALTER TABLE public.invoices
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);
```

#### Tabelle Secondarie
```sql
-- Procedure odontoiatriche
ALTER TABLE public.dental_procedures
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Disponibilità dottori
ALTER TABLE public.doctor_availability
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Specializzazioni dottori
ALTER TABLE public.doctor_specialties
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);
```

#### Tabelle Inventario
```sql
-- Prodotti
ALTER TABLE public.products
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);

-- Dispositivi medici
ALTER TABLE public.medical_devices
  ADD COLUMN clinic_id UUID NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000'
  REFERENCES public.clinics(id);
```

### Indici per Performance

Per ottimizzare le performance delle query filtrate per clinica:

```sql
-- Indici per clinic_id su tutte le tabelle principali
CREATE INDEX IF NOT EXISTS idx_patients_clinic_id ON public.patients(clinic_id);
CREATE INDEX IF NOT EXISTS idx_doctors_clinic_id ON public.doctors(clinic_id);
CREATE INDEX IF NOT EXISTS idx_appointments_clinic_id ON public.appointments(clinic_id);
CREATE INDEX IF NOT EXISTS idx_treatments_clinic_id ON public.treatments(clinic_id);
CREATE INDEX IF NOT EXISTS idx_invoices_clinic_id ON public.invoices(clinic_id);
CREATE INDEX IF NOT EXISTS idx_reminders_clinic_id ON public.reminders(clinic_id);
CREATE INDEX IF NOT EXISTS idx_patient_events_clinic_id ON public.patient_events(clinic_id);
CREATE INDEX IF NOT EXISTS idx_patient_files_clinic_id ON public.patient_files(clinic_id);
```

## Row Level Security (RLS)

### Policy Template

Ogni tabella con isolamento clinic_id ha quattro policy RLS standard:

```sql
-- Template per policy RLS (esempio: tabella patients)

-- Policy per SELECT
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Policy per INSERT  
CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Policy per UPDATE
CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Policy per DELETE
CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
```

### Abilitazione RLS

```sql
-- Abilita RLS su tutte le tabelle con isolamento
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
-- ... e così via per tutte le tabelle
```

### Funzionamento delle Policy

Le policy RLS utilizzano il claim `clinic_id` presente nel JWT dell'utente autenticato:

1. **Autenticazione**: L'utente si autentica con Supabase Auth
2. **JWT Claims**: Il JWT include il claim `clinic_id` dell'utente
3. **Policy Evaluation**: PostgreSQL valuta automaticamente le policy per ogni query
4. **Filtro Automatico**: Solo i record con `clinic_id` corrispondente sono accessibili

## Implementazione Servizi

### ServiceUtils.ts

Il file `ServiceUtils.ts` fornisce funzioni utility per gestire l'isolamento:

```typescript
/**
 * Ottiene il clinic_id dal contesto dell'utente autenticato
 */
export async function getClinicId(): Promise<string> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('Utente non autenticato');
  }
  
  const clinicId = user.user_metadata?.clinic_id;
  
  if (!clinicId) {
    // Fallback alla clinica demo per sviluppo
    return '00000000-0000-0000-0000-000000000000';
  }
  
  return clinicId;
}

/**
 * Aggiunge automaticamente clinic_id ai dati prima dell'inserimento
 */
export async function withClinicId<T extends Record<string, any>>(
  data: T
): Promise<T & { clinic_id: string }> {
  const clinicId = await getClinicId();
  return { ...data, clinic_id: clinicId };
}
```

### Pattern di Implementazione nei Servizi

#### Operazioni di Lettura

```typescript
// Esempio: PatientService.getPatients()
static async getPatients(): Promise<PaginatedResponse<Patient>> {
  try {
    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();
    
    let query = supabase
      .from('patients')
      .select('*')
      .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
      .eq('deleted_at', null); // Soft delete
    
    const { data, error } = await query;
    
    if (error) throw error;
    
    return {
      data: data || [],
      total: data?.length || 0,
      page: 1,
      limit: data?.length || 0
    };
  } catch (error) {
    console.error('Errore nel recupero pazienti:', error);
    throw error;
  }
}
```

#### Operazioni di Scrittura

```typescript
// Esempio: PatientService.createPatient()
static async createPatient(patientData: CreatePatientData): Promise<Patient> {
  try {
    // Aggiunge automaticamente clinic_id
    const dataWithClinic = await withClinicId(patientData);
    
    const { data, error } = await supabase
      .from('patients')
      .insert(dataWithClinic)
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Errore nella creazione paziente:', error);
    throw error;
  }
}
```

#### Operazioni di Aggiornamento

```typescript
// Esempio: PatientService.updatePatient()
static async updatePatient(id: string, updates: Partial<Patient>): Promise<Patient> {
  try {
    // Ottieni clinic_id per l'isolamento
    const clinicId = await getClinicId();
    
    const { data, error } = await supabase
      .from('patients')
      .update(updates)
      .eq('id', id)
      .eq('clinic_id', clinicId) // Assicura che l'aggiornamento sia solo per la clinica corrente
      .select()
      .single();
    
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Errore nell\'aggiornamento paziente:', error);
    throw error;
  }
}
```

### Servizi Aggiornati

Tutti i servizi principali sono stati aggiornati per supportare l'isolamento:

| Servizio | Metodi Aggiornati | Filtri Clinic_ID |
|----------|-------------------|------------------|
| **PatientService** | getPatients, getPatientById, getPatientStats, searchPatients, getPatientsByDoctor | 10 |
| **DoctorService** | getDoctors, getDoctorById, getDoctorStats | 6 |
| **AppointmentService** | getAppointments, getAppointmentById, checkTimeConflict, getAppointmentStats | 7 |
| **TreatmentService** | getTreatments, getTreatmentById | 2 |
| **InvoiceService** | getInvoices, getInvoiceById, generateInvoiceNumber, getInvoiceStats, getUpcomingDueInvoices | 5 |
| **FileService** | getPatientFiles, getPatientFileStats | 2 |
| **ReminderService** | getReminders, getReminderById, getReminderStats, getUpcomingRemindersByPatient | 7 |
| **PatientEventService** | getEventsByPatient, getEventById | 2 |

## Testing e Verifica

### Test Automatici

#### Test di Configurazione

```bash
# Verifica che tutti i servizi abbiano i filtri clinic_id
cd package
node test-isolation.cjs
```

Questo test verifica:
- ✅ Presenza import `getClinicId` in tutti i servizi
- ✅ Utilizzo filtri `.eq('clinic_id', clinicId)` nelle query
- ✅ Numero corretto di filtri per servizio
- ✅ Presenza migrazioni per isolamento
- ✅ Configurazione corretta di ServiceUtils

#### Test Funzionale

```bash
# Test con dati reali (richiede database attivo)
cd package  
node test-functional-isolation.cjs
```

Questo test verifica:
- 🔌 Connessione al database
- 🏥 Esistenza cliniche di test
- 📊 Isolamento dati per clinica
- 🔒 Assenza di leak di dati tra cliniche

### Test Manuali

#### 1. Test Isolamento Dati

1. Accedi con utente della Clinica A
2. Verifica che vedi solo pazienti/dottori/appuntamenti della Clinica A
3. Accedi con utente della Clinica B  
4. Verifica che vedi solo dati della Clinica B
5. Conferma che i dati delle due cliniche sono completamente separati

#### 2. Test Cross-Clinic Access

1. Prova ad accedere direttamente a un record di un'altra clinica (tramite URL o API)
2. Verifica che l'accesso sia negato
3. Controlla che non ci siano errori di sicurezza nei log

#### 3. Test Statistiche

1. Verifica che le statistiche (dashboard, report) siano isolate per clinica
2. Controlla che i conteggi siano corretti per ogni clinica
3. Assicurati che le metriche non includano dati di altre cliniche

### Risultati Test Attesi

```
📊 === RISULTATI FINALI ===
Servizi verificati: 8
Servizi con isolamento: 8  
Percentuale successo: 100%

🎉 TUTTI I SERVIZI SONO CONFIGURATI CORRETTAMENTE!
L'isolamento delle cliniche dovrebbe funzionare come previsto.
```

## Troubleshooting

### Problemi Comuni

#### 1. Errore "clinic_id non trovato"

**Sintomo**: Errore durante l'accesso ai dati  
**Causa**: JWT dell'utente non contiene il claim clinic_id  
**Soluzione**: 
```typescript
// Verifica il JWT dell'utente
const { data: { user } } = await supabase.auth.getUser();
console.log('User metadata:', user?.user_metadata);

// Assicurati che clinic_id sia presente
if (!user?.user_metadata?.clinic_id) {
  // Aggiorna i metadati utente o usa clinica demo
}
```

#### 2. Policy RLS non funzionanti

**Sintomo**: Utenti vedono dati di altre cliniche  
**Causa**: Policy RLS non abilitate o configurate male  
**Soluzione**:
```sql
-- Verifica che RLS sia abilitato
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = false;

-- Abilita RLS se necessario
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;

-- Verifica policy esistenti
SELECT * FROM pg_policies WHERE schemaname = 'public';
```

#### 3. Performance lente

**Sintomo**: Query lente su tabelle grandi  
**Causa**: Mancanza di indici su clinic_id  
**Soluzione**:
```sql
-- Crea indici per clinic_id
CREATE INDEX IF NOT EXISTS idx_patients_clinic_id ON public.patients(clinic_id);
CREATE INDEX IF NOT EXISTS idx_appointments_clinic_id ON public.appointments(clinic_id);
-- ... per tutte le tabelle principali
```

#### 4. Dati senza clinic_id

**Sintomo**: Record esistenti senza clinic_id  
**Causa**: Dati creati prima dell'implementazione isolamento  
**Soluzione**:
```sql
-- Aggiorna record esistenti con clinic_id demo
UPDATE public.patients 
SET clinic_id = '00000000-0000-0000-0000-000000000000' 
WHERE clinic_id IS NULL;
```

### Debug e Monitoring

#### Log delle Query

```typescript
// Abilita logging delle query per debug
const supabase = createClient(url, key, {
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Debug': 'true'
    }
  }
});
```

#### Verifica Policy RLS

```sql
-- Query per verificare che le policy funzionino
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM patients WHERE clinic_id = '00000000-0000-0000-0000-000000000000';
```

## Best Practice

### Sviluppo

1. **Sempre usare getClinicId()**: Non hardcodare mai clinic_id nei servizi
2. **Test con cliniche multiple**: Testa sempre con almeno 2 cliniche diverse
3. **Verifica policy RLS**: Assicurati che le policy siano abilitate su nuove tabelle
4. **Indici per performance**: Crea sempre indici su clinic_id per tabelle grandi

### Sicurezza

1. **Validazione JWT**: Verifica sempre che il JWT contenga clinic_id valido
2. **Policy difensive**: Usa policy RLS anche se i servizi filtrano già
3. **Audit trail**: Logga accessi e modifiche per audit di sicurezza
4. **Test penetration**: Testa regolarmente tentativi di accesso cross-clinic

### Performance

1. **Indici composti**: Usa indici composti (clinic_id, other_column) per query complesse
2. **Partitioning**: Considera il partitioning per tabelle molto grandi
3. **Cache per clinica**: Implementa cache separata per ogni clinica
4. **Monitoring**: Monitora performance delle query filtrate per clinic_id

### Manutenzione

1. **Migrazioni sicure**: Testa sempre le migrazioni su dati di test multi-clinica
2. **Backup per clinica**: Considera backup separati per ogni clinica
3. **Documentazione**: Mantieni aggiornata la documentazione dell'isolamento
4. **Training team**: Assicurati che tutto il team comprenda l'architettura multi-tenant
