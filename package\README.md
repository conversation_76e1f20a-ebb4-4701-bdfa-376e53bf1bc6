# Gestionale Odontoiatrico - Frontend Application

![Gestionale Odontoiatrico](./src/assets/images/logos/dental-crm-logo-basic.svg)

Frontend React dell'applicazione **Gestionale Odontoiatrico** - Sistema CRM completo per studi dentistici e cliniche odontoiatriche, sviluppato con React, TypeScript, Tailwind CSS e integrazione Supabase.

## 🆕 Ultime Implementazioni

### ✅ Menu Laterale Completo & Fix Errore 406 (24 Luglio 2025)

**Risoluzione problemi menu e autenticazione**:

- **🏥 Menu Completo per Tutti gli Utenti**:

  - ✅ **Menu unificato clinica** con tutte le sezioni (Appuntamenti, Pazienti, Dottori, Personale, Reparti, Stanze, Trattamenti, Magazzino, Contabilità, Eventi, Galleria)
  - ✅ **Hook useSidebarMenu** semplificato per restituire sempre il menu completo
  - ✅ **Route aggiornate** per utilizzare i percorsi esistenti nel router

- **🔧 Fix Errore 406 "Not Acceptable"**:

  - ✅ **Hook useAuth migliorato** con `maybeSingle()` invece di `single()` per evitare errori quando nessun record è trovato
  - ✅ **Fallback robusto** ai metadata utente se il record in `public.users` non esiste
  - ✅ **Auto-recovery** per creare automaticamente record utente mancanti
  - ✅ **Logging dettagliato** per debug e monitoraggio

- **🧹 Pulizia Codice**:
  - ✅ **File obsoleti eliminati** (cache Supabase, migrazioni duplicate, documentazione obsoleta)
  - ✅ **Componenti essenziali mantenuti** (DevDebugPanel, test unitari, configurazioni)
  - ✅ **Struttura pulita** con solo file necessari per sviluppo e produzione

### ✅ Sistema Autenticazione Multi-Clinic (23 Gennaio 2025)

**Migrazione completa a architettura multi-clinic con autenticazione Supabase e RLS**:

- **🔐 Sistema Autenticazione Completo**:

  - ✅ **Magic-Link Authentication** con Supabase Auth
  - ✅ **Edge Function JWT Claims** per custom roles e clinic assignment
  - ✅ **User Management System** con inviti e gestione collaboratori
  - ✅ **Dev Mode Fallback** con mock user per sviluppo locale

- **🗄️ Database Schema Multi-Clinic**:

  - ✅ **Row Level Security (RLS)** configurato con policies per isolamento cliniche
  - ⚠️ **RLS abilitata in produzione** (richiede applicazione manuale via Dashboard)
  - ✅ **Storage Policies** per isolamento file per clinica
  - ✅ **Cloud-Compatible Migrations** con deployment sicuro

- **🔧 Service Layer Architecture**:
  - ✅ **ServiceUtils** con fallback clinic_id robusto
  - ✅ **Tutti i servizi CRUD** supportano clinic isolation
  - ✅ **PatientEventService** completamente riscritto per integrazione Supabase

## 📋 Indice

- [🚀 Caratteristiche Principali](#-caratteristiche-principali)
- [📊 Stato Integrazione Database](#-stato-integrazione-database)
  - [✅ Servizi Completati](#-servizi-completati-production-ready)
  - [✅ UI Integrate](#-ui-components-integrate)
  - [🔄 In Sviluppo](#-in-sviluppo)
- [🌐 Demo e Test](#-demo-e-test)
- [🔧 Setup e Installazione](#-setup-e-installazione)
- [🛠️ Stack Tecnologico](#️-stack-tecnologico)
- [🏗️ Struttura Frontend](#️-struttura-frontend)
- [📱 Design Responsive](#-design-responsive)
- [🔐 Gestione Utenti](#-gestione-utenti)
- [🧪 Testing](#-testing)
- [📞 Support & Contributing](#-support--contributing)
- [📄 Licenza](#-licenza)

## 🚀 Caratteristiche Principali

- **🎯 Dashboard Real-time**: Metriche live da database Supabase con statistiche pazienti, appuntamenti e fatturato
- **👥 Gestione Pazienti Completa**: ✅ CRUD funzionale con database integration
- **📅 Sistema Appuntamenti Avanzato**: ✅ Calendar sincronizzato con database reale
- **⏰ Sistema Promemoria Avanzato**: ✅ Promemoria collegati ai pazienti con quick actions e categorizzazione
- **� Allegati Diagnostici**: ✅ Sistema upload/download per file OTP, TAC, Cone Beam con preview inline
- **�💰 Fatturazione Professionale**: ✅ Sistema completo con numerazione automatica e tracking pagamenti
- **🏥 Gestione Trattamenti**: ✅ Catalogo database-driven con pricing dinamico
- **👨‍⚕️ Gestione Dottori**: ✅ Sistema specializzazioni e disponibilità
- **📦 Inventario Intelligente**: Sistema gestione scorte con alert automatici
- **🔄 Real-time Sync**: Aggiornamenti automatici cross-client via Supabase
- **📱 Mobile-First Design**: UI responsive ottimizzata per tutti i dispositivi
- **🌙 Dark/Light Theme**: Supporto temi dinamici con persistenza

## 📊 Stato Integrazione Database

### ✅ Servizi Completati (Production Ready)

- **✅ PatientService**: CRUD completo, search, pagination, statistics
  - **✅ Nuovi campi UDI**: Codice identificativo paziente integrato
  - **✅ Anamnesi Firmata**: Tracking digitale firma anamnesi con badge UI
  - **✅ Auto-save**: Salvataggio incrementale con debounce per UX fluida
- **✅ InvoiceService**: Fatturazione avanzata con auto-numbering (INV-2025-001)
- **✅ DoctorService**: Gestione dottori con specializzazioni e colori
- **✅ TreatmentService**: Catalogo trattamenti con categories e pricing
- **✅ AppointmentService**: Sistema prenotazioni con relazioni complete
- **✅ ReminderService**: Sistema promemoria con patient linking e categorizzazione
- **✅ PatientEventService**: ✅ **NUOVO** - Gestione eventi pazienti con integrazione Supabase completa
  - **✅ Database Integration**: Sostituito mock service con chiamate Supabase reali
  - **✅ Clinic Isolation**: Supporto clinic_id per multi-tenant
  - **✅ CRUD Completo**: Create, read, update, delete eventi con error handling
  - **✅ Component Integration**: PatientEventModal e ViewPatient aggiornati
- **✅ FileService**: Sistema allegati diagnostici (OTP, TAC, Cone Beam)
  - **✅ Upload Sicuro**: Drag & drop con validazione MIME e dimensioni (10MB)
  - **✅ Categorizzazione**: OTP, CBCT, TAC, documenti generici
  - **✅ Storage Supabase**: Bucket `patient-files` con clinic isolation
  - **✅ Soft Delete**: Eliminazione logica con `deleted_at` per recupero dati
  - **✅ Preview Thumbnails**: Anteprima immagini e icone documenti

### ✅ UI Components Integrate

- **✅ PatientForm**: Workflow create/update funzionale con database saves
  - **✅ Campi UDI e Anamnesi**: Integrazione completa nuovi campi Supabase
  - **✅ Toast Notifications**: Feedback immediato successo/errore operazioni
  - **✅ Real-time Validation**: Type safety end-to-end con auto-generated types
- **✅ Billing Dashboard**: Dati reali da InvoiceService con patient relationships
- **✅ Calendar**: Appuntamenti sincronizzati da database con loading states
- **✅ RightSidebar**: Recent appointments da dati reali
- **✅ Reminder System**: Quick actions, modal avanzato, lista con toggle completion
- **✅ File Management**: Sistema drag & drop per allegati diagnostici
  - **✅ FileUpload**: Componente dropzone con progress e validazione
  - **✅ FileList**: Griglia preview con thumbnail e badge categorizzazione
  - **✅ ViewPatient Tab**: Sezione "📎 Allegati" integrata con auto-reload

### 🔄 In Sviluppo

- **🔄 Dashboard Analytics**: Sostituzione hardcoded stats con dati reali
- **🔄 ProductService**: Sistema inventario database-driven

## � Ultime Implementazioni

### ✅ Sistema Allegati Diagnostici (19 Luglio 2025)

**Implementazione completa gestione file medici** con integration form pazienti:

- **📎 Funzionalità Core**:

  - Upload drag & drop di file OTP, TAC, Cone Beam
  - Categorizzazione automatica con badge colorate
  - Preview inline per PDF e immagini
  - Soft delete con possibilità di recupero

- **🔧 Integrazione Form**:

  - Sezione allegati automatica nei form di modifica paziente
  - Upload limitato ai pazienti esistenti (logica condizionale)
  - Auto-reload dopo operazioni con toast feedback

- **⚡ Bug-fix Risolti**:

  - **Pulsante VISUALIZZA**: Ora apre modal preview inline (no nuove tab)
  - **Pulsante SCARICA**: Download reale con Blob fetch (correzione CORS)
  - **Eliminazione File**: Soft delete funzionante con rollback corretto

- **🗄️ Database & Storage**:

  - Bucket Supabase `patient-files` (10MB limit)
  - Tabella `patient_files` con soft delete (`deleted_at`)
  - Migrazione RLS temporaneamente disabilitato per sviluppo

### ✅ Patient Data Migration (Luglio 2025)

**Migrazione incrementale sicura** dei dati pazienti con nuove funzionalità:

- **📋 Nuovi Campi Database**:

  - `udi`: Codice identificativo univoco paziente
  - `anamnesi_signed`: Tracking digitale firma anamnesi

- **🎨 UI Enhancements**:

  - Campo UDI integrato in PatientForm con auto-save
  - Checkbox "Anamnesi Firmata" con visual feedback
  - Badge colorato in ViewPatient (verde=firmata, rosso=non firmata)

- **⚡ UX Improvements**:

  - Toast notifications per feedback immediato
  - Zustand store per state management ottimizzato
  - Debounced auto-save per prestazioni fluide
  - Real-time sync tra Edit/View patient

- **🔧 Technical Stack**:
  - Database migration non-distruttiva
  - Type safety end-to-end con auto-generated types
  - Error handling robusto con logging dettagliato

## �🌐 Demo e Test

**🚀 Production URL**: [https://gestionale-odontoiatrico.vercel.app](https://gestionale-odontoiatrico.vercel.app)

Esperienza completa con database Supabase integrato:

- ✅ **Database Reale**: Pazienti, dottori, trattamenti e appuntamenti da Supabase
- ✅ **CRUD Funzionale**: Creazione pazienti salva effettivamente nel database
- ✅ **Real-time Updates**: Modifiche sincronizzate istantaneamente
- ✅ **Mobile Optimized**: Menu completo con sottomenu espandibili
- ✅ **Multi-role Support**: Dashboard dentista/clinica con permessi

### Test Accounts

```javascript
// Simula utente dentista
localStorage.setItem(
  'user-session',
  JSON.stringify({
    user: { email: '<EMAIL>', role: 'dentist' },
  }),
);

// Simula utente clinica
localStorage.setItem(
  'user-session',
  JSON.stringify({
    user: { email: '<EMAIL>', role: 'clinic' },
  }),
);
```

## 🔧 Setup e Installazione

### Prerequisiti

Prima di iniziare, assicurati di avere installato:

- Node.js (versione 18.x o superiore)
- npm (versione 9.x o superiore)
- Docker Desktop (per database locale)
- Supabase CLI: `npm install -g @supabase/cli`

### 1. Clone Repository

```bash
git clone https://github.com/daviducciope/GestionaleOdontoiatrico.git
cd GestionaleOdontoiatrico/package
```

### 2. Installa Dipendenze

```bash
npm install --legacy-peer-deps
```

> **IMPORTANTE**: Usa sempre `--legacy-peer-deps` per evitare conflitti di dipendenze.

### 3. Setup Database Locale

#### Avvia Supabase (Docker deve essere attivo)

```bash
# Avvia l'istanza locale Supabase
npx supabase start

# Output mostrerà le credenziali locali:
# API URL: http://127.0.0.1:54321
# anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Avvia Edge Functions (per Auth Hook)

```bash
# Avvia la Edge Function per JWT custom claims
npx supabase functions serve issue-jwt

# Output mostrerà:
# Functions API URL: http://127.0.0.1:54321/functions/v1/issue-jwt
```

#### Migrazione File Paths (solo se necessario)

```bash
# Migra file esistenti ai nuovi path con clinic isolation
npm run migrate:file-paths

# Questo sposta i file da:
# patientId/category/filename
# a:
# clinicId/patientId/category/filename
```

#### Configura Environment

Il file `.env` dovrebbe già esistere con le credenziali locali:

```env
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

### 4. Avvia Development Server

```bash
npm run dev
```

Apri il browser su: `http://localhost:5174` (o altra porta disponibile)

### 🔗 URLs Locali Importanti

- **Frontend**: http://localhost:5174
- **Supabase Studio**: http://localhost:54323 (GUI per database)
- **API**: http://127.0.0.1:54321
- **Database**: postgresql://postgres:postgres@127.0.0.1:54322/postgres

### 🔄 Reset Database (se necessario)

Se hai problemi con container precedenti o nomi progetti cambiati:

```bash
# Ferma e rimuovi tutti i container Supabase
docker stop $(docker ps -q --filter "name=supabase")
docker rm $(docker ps -aq --filter "name=supabase")

# Riavvia fresh
npx supabase start
```

## 🛠️ Stack Tecnologico

### Frontend Core

- **⚛️ React 18**: Libreria UI con Concurrent Features
- **📘 TypeScript**: Type safety e developer experience
- **⚡ Vite**: Build tool ultra-veloce con HMR
- **🎨 Tailwind CSS**: Utility-first CSS framework
- **🌊 Flowbite**: Componenti UI React pre-costruiti

### Data & State Management

- **🗄️ Supabase**: Backend-as-a-Service PostgreSQL
- **🏪 Zustand**: State management leggero e scalabile
- **🔄 Real-time subscriptions**: Live data sync
- **📋 React Hook Form**: Form management performante

### UI & Visualization

- **📊 ApexCharts**: Grafici interattivi e dashboard
- **🗂️ React Router v7**: Client-side routing avanzato
- **🎨 Responsive Design**: Mobile-first approach
- **🌙 Theme System**: Dark/Light mode support

## 📜 Script NPM Disponibili

### Development & Build

```bash
npm run dev           # Avvia server di sviluppo (Vite)
npm run build         # Build produzione
npm run build:check   # Build con controllo TypeScript
npm run preview       # Preview build produzione
npm run lint          # Controllo ESLint
```

### Testing

```bash
npm run test          # Esegui test suite (Jest)
npm run test:watch    # Test in modalità watch
npm run test:coverage # Test con coverage report
```

### Database & Maintenance

```bash
npm run db:push                # Push migrazioni a Supabase (alias per supabase db push)
npm run dev:purge             # Pulizia environment dev (cleanup_dev.ts)
npm run migrate:file-paths    # Migrazione path file per clinic isolation
```

> **Note**: Lo script `dev:purge` rimuove test data, file orfani storage, e invoice di test per ambiente pulito.

## 🏗️ Struttura Frontend

```
package/
├── public/              # File statici
├── src/
│   ├── assets/          # Immagini, font e altri asset
│   ├── components/      # Componenti riutilizzabili
│   │   ├── appointments/    # Modali e form appuntamenti
│   │   ├── patients/        # Form e card pazienti
│   │   ├── billing/         # Componenti fatturazione
│   │   ├── dashboard/       # Widget dashboard
│   │   └── shared/          # Componenti condivisi
│   ├── services/        # Layer di accesso ai dati
│   │   ├── PatientService.ts
│   │   ├── InvoiceService.ts
│   │   ├── AppointmentService.ts
│   │   └── ...
│   ├── views/           # Pagine complete
│   │   ├── patients/        # Lista, form, dettaglio pazienti
│   │   ├── appointments/    # Gestione appuntamenti
│   │   ├── billing/         # Dashboard fatturazione
│   │   └── dashboards/      # Dashboard principale
│   ├── types/           # Definizioni TypeScript
│   ├── hooks/           # Custom React hooks
│   ├── store/           # Zustand global store
│   ├── utils/           # Funzioni di utilità
│   ├── App.tsx          # Componente principale
│   └── main.tsx         # Entry point
├── package.json         # Dipendenze e script
└── vite.config.ts       # Configurazione di Vite
```

## 📱 Design Responsive

L'applicazione garantisce **parità completa di funzionalità** tra tutte le piattaforme:

### Desktop Experience

- **Layout Full-Featured**: Sidebar multiple con menu espandibili
- **Dashboard Avanzata**: Grafici interattivi e metriche real-time
- **Shortcuts Keyboard**: Navigazione rapida e hotkeys

### Mobile Experience

- **Menu Drawer Completo**: Tutte le funzionalità desktop disponibili
- **Touch Optimized**: Target touch 48px+ per migliore usabilità
- **Gesture Support**: Swipe, pinch-to-zoom, pull-to-refresh

### Cross-Platform Features

- **Real-time Sync**: Modifiche sincronizzate istantaneamente
- **Theme Persistence**: Dark/Light mode salvato tra dispositivi
- **Session Management**: Login persistente cross-device

## 🔐 Gestione Utenti

### Simulazione Ruoli per Development

Per testare diversi livelli di accesso durante lo sviluppo:

```javascript
// Simula utente dentista
localStorage.setItem(
  'user-session',
  JSON.stringify({
    user: {
      email: '<EMAIL>',
      role: 'dentist',
      id: 'dentist-001',
    },
  }),
);

// Simula amministratore clinica
localStorage.setItem(
  'user-session',
  JSON.stringify({
    user: {
      email: '<EMAIL>',
      role: 'clinic',
      id: 'admin-001',
    },
  }),
);
```

### Sistema Permessi

- **Dentist Role**: Dashboard personale, pazienti assegnati, propri appuntamenti
- **Clinic Admin**: Accesso completo, gestione dottori, statistiche globali

## 🧪 Testing

Per eseguire i test:

```bash
npm run test
```

## � Troubleshooting

### 🐳 Database Locale

**Supabase non si avvia**

```bash
# Verifica Docker sia attivo
docker --version
docker ps

# Reset completo container
npx supabase stop
docker system prune -f
npx supabase start
```

**Container con nome vecchio progetto**

```bash
# Lista container Supabase
docker ps -a --filter "name=supabase"

# Reset con nuovo nome progetto
docker stop $(docker ps -q --filter "name=supabase")
docker rm $(docker ps -aq --filter "name=supabase")
npx supabase start
```

**Credenziali non funzionano**

```bash
# Verifica file .env
cat .env

# Rigenera credenziali
npx supabase stop
npx supabase start
# Copia nuovo anon_key dal output
```

### ⚛️ Frontend Issues

**Porta già occupata**

```bash
# Se 5173 è occupata, Vite usa automaticamente 5174
# Verifica nel terminale l'URL corretto
npm run dev
```

**Errori connessione API**

```bash
# Verifica Supabase status
npx supabase status

# Controlla Studio database
# http://localhost:54323
```

**Servizi non caricano dati**

```bash
# Verifica schema database in Studio
# Applica seed data
npx supabase db reset

# Controlla console browser per errori JS
```

### 🔄 Reset Completo

Se tutto va storto:

```bash
# 1. Stop tutto
npx supabase stop
docker system prune -f

# 2. Reset dipendenze
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps

# 3. Restart database
npx supabase start

# 4. Verifica .env con nuove credenziali
# 5. Start frontend
npm run dev
```

## �📞 Support & Contributing

### Contributing Workflow

1. **Fork Repository**: [https://github.com/daviducciope/GestionaleOdontoiatrico](https://github.com/daviducciope/GestionaleOdontoiatrico)
2. **Clone Locally**: `git clone https://github.com/YOUR_USERNAME/GestionaleOdontoiatrico.git`
3. **Create Feature Branch**: `git checkout -b feature/amazing-feature`
4. **Development**: Test locale + Supabase setup
5. **Commit**: Conventional Commits format (`feat:`, `fix:`, `docs:`)
6. **Push**: `git push origin feature/amazing-feature`
7. **Pull Request**: Descrizione dettagliata + screenshots se UI changes

### Contact & Support

- **Developer**: David Ucciope
- **Email**: <EMAIL>
- **Project URL**: [GestionaleOdontoiatrico](https://github.com/daviducciope/GestionaleOdontoiatrico)
- **Live Demo**: [https://gestionale-odontoiatrico.vercel.app](https://gestionale-odontoiatrico.vercel.app)

## 📄 Licenza

Distribuito sotto **MIT License**. Vedi [LICENSE](../LICENSE) per dettagli.

---

<div align="center">
  
**🦷 Gestionale Odontoiatrico Frontend**  
*React + TypeScript + Supabase Integration*

[🚀 Live Demo](https://gestionale-odontoiatrico.vercel.app) | [📚 Documentation](../README.md) | [🐛 Issues](https://github.com/daviducciope/GestionaleOdontoiatrico/issues)

</div>
