-- Fix definitivo policy RLS per isolamento cliniche
-- Problema: Le policy usano il path JWT sbagliato
-- Data: 2025-07-28 14:00:00

-- DIAGNOSI:
-- Le policy RLS per patients usano: auth.jwt() ->> 'clinic_id'
-- Ma i JWT claims sono in: auth.jwt() -> 'app_metadata' ->> 'clinic_id'

-- SOLUZIONE:
-- <PERSON><PERSON><PERSON><PERSON> tutte le policy con il path corretto

-- 1. Rimuovi policy esistenti per patients
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

-- 2. Crea policy corrette con path JWT giusto per patients
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- 3. Fix policy per altre tabelle se esistono

-- DOCTORS
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.doctors;

CREATE POLICY "clinic_isolation_select" ON public.doctors
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.doctors
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.doctors
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.doctors
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- APPOINTMENTS
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.appointments;

CREATE POLICY "clinic_isolation_select" ON public.appointments
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.appointments
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.appointments
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.appointments
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- TREATMENTS
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.treatments;

CREATE POLICY "clinic_isolation_select" ON public.treatments
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.treatments
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.treatments
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.treatments
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Log dell'operazione
DO $$
BEGIN
  RAISE NOTICE 'Policy RLS corrette con path JWT: auth.jwt() -> ''app_metadata'' ->> ''clinic_id''';
  RAISE NOTICE 'Tutte le tabelle (patients, doctors, appointments, treatments) aggiornate';
END $$;
