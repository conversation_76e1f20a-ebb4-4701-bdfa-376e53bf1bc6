import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Icon } from '@iconify/react';
import PatientForm from '../../components/patients/PatientForm';
import { PatientService } from '../../services/PatientService';

// Interfaccia per i dati del paziente
interface Patient {
  id: string; // Cambiato da number a string per supportare UUID
  name: string;
  phone: string;
  email: string;
  birthdate: string;
  gender: string;
  address: string;
  notes: string;
  udiCode: string;
  fiscalCode?: string;
  medicalHistory?: string;
  isSmoker?: boolean;
  medications?: string;
  anamnesis?: string;
  // Nuovi campi Supabase
  udi?: string;
  anamnesi_signed?: boolean;
}

const EditPatient = () => {
  const { id } = useParams();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(true);

  // Carica i dati del paziente da Supabase
  useEffect(() => {
    const loadPatient = async () => {
      if (!id) return;

      try {
        setLoading(true);
        console.log('🔍 Loading patient with ID:', id);
        const patientData = await PatientService.getPatientById(id);
        console.log('📦 Patient data received:', patientData);
        
        if (patientData) {
          // Mappa i dati da Supabase al formato del form
          const mappedPatient: Patient = {
            id: id, // Mantieni l'ID come stringa UUID
            name: `${patientData.first_name} ${patientData.last_name}`.trim(),
            phone: patientData.phone || '',
            email: patientData.email || '',
            birthdate: patientData.date_of_birth || '',
            gender: 'M', // Da mappare se hai il campo gender in Supabase
            address: patientData.address || '',
            notes: '', // Da mappare se hai un campo notes
            udiCode: patientData.udi || '', // Mappa udi -> udiCode
            fiscalCode: patientData.fiscal_code || '',
            medicalHistory: patientData.medical_history || '',
            isSmoker: patientData.is_smoker || false,
            medications: patientData.medications || '',
            anamnesis: patientData.anamnesis || '',
            // Nuovi campi Supabase
            udi: patientData.udi || '',
            anamnesi_signed: patientData.anamnesi_signed || false
          };

          setPatient(mappedPatient);
        }
      } catch (error) {
        console.error('Errore nel caricamento del paziente:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPatient();
  }, [id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2">Caricamento...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <h5 className="card-title">Modifica Paziente</h5>
            <Link to={`/patients/view/${id}`} className="text-gray-500 hover:text-primary">
              <Icon icon="solar:arrow-left-linear" height={20} />
              <span className="sr-only">Torna al profilo paziente</span>
            </Link>
          </div>
        </div>

        {patient && <PatientForm isEdit={true} patientData={patient} />}
      </div>
    </>
  );
};

export default EditPatient;
