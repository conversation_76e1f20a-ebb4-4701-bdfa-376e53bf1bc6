-- 🏥 <PERSON><PERSON><PERSON> SOSTANZA Dental CRM - Database Seed File
-- This file contains sample data for local development
-- Run with: supabase db reset

-- Clear existing data (in correct order due to foreign key constraints)
TRUNCATE TABLE public.invoices CASCADE;
TRUNCATE TABLE public.appointments CASCADE;
TRUNCATE TABLE public.treatments CASCADE;
TRUNCATE TABLE public.doctor_availability CASCADE;
TRUNCATE TABLE public.doctor_specialties CASCADE;
TRUNCATE TABLE public.doctors CASCADE;
TRUNCATE TABLE public.patients CASCADE;
TRUNCATE TABLE public.reminders CASCADE;

-- Reset sequences
ALTER SEQUENCE IF EXISTS doctors_id_seq RESTART WITH 1;
ALTER SEQUENCE IF EXISTS treatments_id_seq RESTART WITH 1;
ALTER SEQUENCE IF EXISTS appointments_id_seq RESTART WITH 1;
ALTER SEQUENCE IF EXISTS reminders_id_seq RESTART WITH 1;

-- Insert Enhanced Doctors with complete professional data
INSERT INTO public.doctors (
    title, first_name, last_name, fiscal_code, birth_date, sex, citizenship,
    residence_street, residence_cap,
    pec, email, mobile, phone,
    order_province, order_number, order_date, albo_code, practice_status,
    calendar_color, qualifications,
    vat_id, iban, notes,
    created_at, updated_at
) VALUES 
(
    'DOTT.', 'Mario', 'Rossi', '****************', '1980-01-15', 'M', 'ITALIANA',
    'Via Roma 123', '20100',
    '<EMAIL>', '<EMAIL>', '+39 ***********', '+39 02 1234567',
    'MI', '12345', '2005-07-15', 'O', true,
    '#FF5733', 'Specialista in Ortodonzia, Master in Invisalign',
    '12345678901', '***************************', 'Specialista in ortodonzia con 20 anni di esperienza',
    now(), now()
),
(
    'DOTT.SSA', 'Anna', 'Verdi', '****************', '1985-08-22', 'F', 'ITALIANA',
    'Via Garibaldi 456', '00100',
    '<EMAIL>', '<EMAIL>', '+39 347 9876543', '+39 06 9876543',
    'RM', '67890', '2010-09-20', 'O', true,
    '#33FF57', 'Specialista in Endodonzia, Perfezionamento in Microscopia Operatoria',
    '09876543210', '***************************', 'Endodontista specializzata in casi complessi',
    now(), now()
),
(
    'PROF.', 'Luca', 'Bianchi', 'BNCLCU75D10L219Z', '1975-04-10', 'M', 'ITALIANA',
    'Corso Italia 789', '10100',
    '<EMAIL>', '<EMAIL>', '+39 333 5555666', '+39 011 5555666',
    'TO', '11111', '2000-03-12', 'O', true,
    '#3357FF', 'Professore Ordinario, Specialista in Chirurgia Maxillo-Facciale, Master in Implantologia',
    '11111111111', 'IT90X0542811101000000111111', 'Professore ordinario, chirurgo maxillo-facciale',
    now(), now()
),
(
    'DOTT.SSA', 'Sara', 'Neri', 'NRISRA90L15F839W', '1990-07-15', 'F', 'ITALIANA',
    'Via Dante 321', '80100',
    '<EMAIL>', '<EMAIL>', '+39 328 7777888', '+39 081 7777888',
    'NA', '22222', '2015-11-30', 'O', true,
    '#FF33F5', 'Igienista Dentale, Master in Parodontologia',
    NULL, NULL, 'Igienista dentale e parodontologa junior',
    now(), now()
);

-- Insert Doctor Specialties
INSERT INTO public.doctor_specialties (doctor_id, specialty_code, specialty_name, specialization_date) VALUES
(1, 'ORD001', 'Ortodonzia', '2008-07-20'),
(2, 'END001', 'Endodonzia', '2013-12-15'),
(2, 'CON001', 'Odontoiatria Conservativa', '2014-06-10'),
(3, 'CHI001', 'Chirurgia Orale', '2003-09-25'),
(3, 'IMP001', 'Implantologia', '2005-03-18'),
(4, 'IGI001', 'Igiene Dentale', '2018-05-12'),
(4, 'PAR001', 'Parodontologia', '2020-10-08');

-- Insert Doctor Availability (example schedules)
INSERT INTO public.doctor_availability (doctor_id, day_of_week, start_time, end_time, is_active) VALUES
-- Dr. Mario Rossi (Mon-Fri 9:00-18:00)
(1, 1, '09:00:00', '18:00:00', true), -- Monday
(1, 2, '09:00:00', '18:00:00', true), -- Tuesday  
(1, 3, '09:00:00', '18:00:00', true), -- Wednesday
(1, 4, '09:00:00', '18:00:00', true), -- Thursday
(1, 5, '09:00:00', '18:00:00', true), -- Friday
-- Dr. Anna Verdi (Mon-Wed-Fri 8:30-17:30)
(2, 1, '08:30:00', '17:30:00', true), -- Monday
(2, 3, '08:30:00', '17:30:00', true), -- Wednesday
(2, 5, '08:30:00', '17:30:00', true), -- Friday
-- Prof. Luca Bianchi (Tue-Thu 10:00-19:00, Sat 9:00-13:00)
(3, 2, '10:00:00', '19:00:00', true), -- Tuesday
(3, 4, '10:00:00', '19:00:00', true), -- Thursday
(3, 6, '09:00:00', '13:00:00', true), -- Saturday
-- Dr. Sara Neri (Mon-Fri 8:00-16:00)
(4, 1, '08:00:00', '16:00:00', true), -- Monday
(4, 2, '08:00:00', '16:00:00', true), -- Tuesday
(4, 3, '08:00:00', '16:00:00', true), -- Wednesday
(4, 4, '08:00:00', '16:00:00', true), -- Thursday
(4, 5, '08:00:00', '16:00:00', true); -- Friday

-- Insert Patients (using UUID for id as per schema)
-- First, insert the test patient with a fixed ID for testing
INSERT INTO public.patients (id, first_name, last_name, phone, email, date_of_birth, address, city, postal_code, province, medical_history, anamnesis, udi, anamnesi_signed, created_at, updated_at) VALUES 
('123e4567-e89b-12d3-a456-************', 'Test', 'Patient', '+39 ************', '<EMAIL>', '1990-01-01', 'Via Test 123', 'Test City', '00000', 'TC', 'Patient for testing purposes', 'Test patient for development', 'TEST001', false, now(), now());

-- Then insert other sample patients
INSERT INTO public.patients (id, first_name, last_name, phone, email, date_of_birth, address, city, postal_code, province, medical_history, anamnesis, created_at, updated_at) VALUES 
(gen_random_uuid(), 'Mario', 'Rossi', '+39 ************', '<EMAIL>', '1980-05-15', 'Via Roma 123', 'Milano', '20100', 'MI', 'Nessuna patologia significativa', 'Paziente in buona salute generale', now(), now()),
(gen_random_uuid(), 'Giulia', 'Bianchi', '+39 ************', '<EMAIL>', '1985-09-22', 'Via Garibaldi 456', 'Roma', '00100', 'RM', 'Allergia a penicillina', 'Paziente con allergie farmacologiche', now(), now()),
(gen_random_uuid(), 'Francesca', 'Neri', '+39 ************', '<EMAIL>', '1990-03-10', 'Via Dante 789', 'Napoli', '80100', 'NA', 'Diabete tipo 2', 'Paziente diabetico sotto controllo', now(), now()),
(gen_random_uuid(), 'Giuseppe', 'Verdi', '+39 ************', '<EMAIL>', '1975-11-30', 'Corso Italia 321', 'Torino', '10100', 'TO', 'Ipertensione', 'Paziente iperteso in terapia', now(), now());

-- Insert Treatments
INSERT INTO public.treatments (name, duration, price, category, description, created_at, updated_at) VALUES 
('Visita di Controllo', 30, 50.00, 'Prevenzione', 'Controllo generale dello stato di salute dentale', now(), now()),
('Pulizia Dentale', 45, 80.00, 'Igiene', 'Rimozione del tartaro e pulizia professionale', now(), now()),
('Otturazione', 60, 120.00, 'Conservativa', 'Otturazione di carie con materiale composito', now(), now()),
('Estrazione', 45, 100.00, 'Chirurgia', 'Estrazione di dente non recuperabile', now(), now()),
('Devitalizzazione', 90, 300.00, 'Endodonzia', 'Trattamento endodontico per salvare il dente', now(), now()),
('Impianto Dentale', 120, 800.00, 'Implantologia', 'Inserimento di impianto dentale in titanio', now(), now()),
('Ortodonzia Mobile', 30, 200.00, 'Ortodonzia', 'Applicazione di apparecchio ortodontico mobile', now(), now()),
('Sbiancamento', 60, 250.00, 'Estetica', 'Sbiancamento professionale dei denti', now(), now());

-- Insert Sample Appointments (using correct column names with clinic_id and deleted_at)
INSERT INTO public.appointments (patient_id, doctor_id, treatment_id, date, start_time, end_time, status, notes, clinic_id, deleted_at, created_at, updated_at) VALUES 
((SELECT id FROM patients WHERE first_name = 'Mario' AND last_name = 'Rossi'), 1, 1, '2025-07-21', '09:00', '09:30', 'confermato', 'Controllo di routine', '00000000-0000-0000-0000-000000000000', NULL, now(), now()),
((SELECT id FROM patients WHERE first_name = 'Giulia' AND last_name = 'Bianchi'), 2, 2, '2025-07-21', '10:30', '11:15', 'confermato', 'Pulizia semestrale', '00000000-0000-0000-0000-000000000000', NULL, now(), now()),
((SELECT id FROM patients WHERE first_name = 'Mario' AND last_name = 'Rossi'), 3, 3, '2025-07-22', '14:00', '15:00', 'confermato', 'Otturazione molare superiore', '00000000-0000-0000-0000-000000000000', NULL, now(), now()),
((SELECT id FROM patients WHERE first_name = 'Giulia' AND last_name = 'Bianchi'), 1, 8, '2025-07-22', '16:00', '17:00', 'confermato', 'Sbiancamento estetico', '00000000-0000-0000-0000-000000000000', NULL, now(), now()),
((SELECT id FROM patients WHERE first_name = 'Francesca' AND last_name = 'Neri'), 1, 5, '2025-07-23', '09:00', '10:30', 'in attesa', 'Devitalizzazione premolare', '00000000-0000-0000-0000-000000000000', NULL, now(), now()),
((SELECT id FROM patients WHERE first_name = 'Giuseppe' AND last_name = 'Verdi'), 2, 4, '2025-07-24', '11:00', '11:45', 'confermato', 'Estrazione dente del giudizio', '00000000-0000-0000-0000-000000000000', NULL, now(), now());

-- Insert Sample Invoices
INSERT INTO public.invoices (id, patient_id, invoice_number, issue_date, due_date, subtotal, tax_rate, tax_amount, total, status, payment_method, payment_date, description, created_at, updated_at) VALUES 
(gen_random_uuid(), (SELECT id FROM patients WHERE first_name = 'Mario' AND last_name = 'Rossi'), 'INV-2025-002', '2025-07-04', '2025-07-19', 100.00, 22.00, 22.00, 122.00, 'draft', NULL, NULL, 'Visita di controllo e pulizia dentale', now(), now()),
(gen_random_uuid(), (SELECT id FROM patients WHERE first_name = 'Giulia' AND last_name = 'Bianchi'), 'INV-2025-003', '2025-07-04', '2025-07-19', 250.00, 22.00, 55.00, 305.00, 'paid', 'Contanti', '2025-07-04', 'Sbiancamento dentale professionale', now(), now());

-- Insert Sample Reminders
INSERT INTO public.reminders (date, time, title, text, completed, created_at, updated_at) VALUES 
('2025-07-08', '08:30', 'Preparazione sala operatoria', 'Preparare la sala per l''intervento di ortodonzia delle 09:00', false, now(), now()),
('2025-07-09', '10:00', 'Controllo scorte', 'Verificare le scorte di materiali per pulizia dentale', false, now(), now()),
('2025-07-10', '13:30', 'Chiamata paziente', 'Richiamare il paziente per conferma appuntamento', false, now(), now());