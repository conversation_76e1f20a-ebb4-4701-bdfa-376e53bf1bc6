/**
 * Debug per nuovo utente che non riesce a creare pazienti
 * Verifica Edge Function, JWT claims e RLS policies
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 === DEBUG NUOVO UTENTE - CREAZIONE PAZIENTI ===\n');

async function debugNewUserIssue() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('1️⃣ Verifica utente corrente...');
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('❌ Nessun utente autenticato');
      console.log('🔄 Prova a fare login dal browser e poi riesegui questo script');
      return;
    }
    
    console.log('✅ Utente autenticato:');
    console.log(`  - ID: ${user.id}`);
    console.log(`  - Email: ${user.email}`);
    console.log(`  - Created: ${user.created_at}`);
    
    console.log('\n2️⃣ Verifica JWT claims attuali...');
    console.log('📋 App Metadata:', JSON.stringify(user.app_metadata, null, 2));
    console.log('📋 User Metadata:', JSON.stringify(user.user_metadata, null, 2));
    
    const clinicId = user.app_metadata?.clinic_id;
    const appRole = user.app_metadata?.app_role;
    
    if (!clinicId) {
      console.log('❌ clinic_id mancante nei JWT claims');
      console.log('🔧 Chiamata Edge Function per iniettare i claims...');
      
      const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
        body: { user: user }
      });
      
      if (functionError) {
        console.log('❌ Errore Edge Function:', functionError.message);
        console.log('📋 Dettagli errore:', functionError);
      } else {
        console.log('✅ Edge Function eseguita:', functionResult);
        
        // Ricarica l'utente per vedere i nuovi claims
        const { data: { user: updatedUser }, error: updateError } = await supabase.auth.getUser();
        if (!updateError && updatedUser) {
          console.log('📋 JWT claims aggiornati:');
          console.log('  - App Metadata:', JSON.stringify(updatedUser.app_metadata, null, 2));
          console.log(`  - Clinic ID: ${updatedUser.app_metadata?.clinic_id}`);
          console.log(`  - App Role: ${updatedUser.app_metadata?.app_role}`);
        }
      }
    } else {
      console.log(`✅ JWT claims presenti - Clinic ID: ${clinicId}, Role: ${appRole}`);
    }
    
    console.log('\n3️⃣ Verifica record in public.users...');
    
    const { data: userRecord, error: userRecordError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (userRecordError) {
      console.log('❌ Record utente non trovato in public.users:', userRecordError.message);
    } else {
      console.log('✅ Record utente trovato:');
      console.log(`  - ID: ${userRecord.id}`);
      console.log(`  - Email: ${userRecord.email}`);
      console.log(`  - Clinic ID: ${userRecord.clinic_id}`);
      console.log(`  - Role: ${userRecord.role}`);
    }
    
    console.log('\n4️⃣ Verifica clinica demo...');
    
    const { data: clinics, error: clinicsError } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', '00000000-0000-0000-0000-000000000000');
    
    if (clinicsError) {
      console.log('❌ Errore verifica clinica:', clinicsError.message);
    } else if (clinics.length === 0) {
      console.log('❌ Clinica demo non trovata');
    } else {
      console.log('✅ Clinica demo trovata:', clinics[0].name);
    }
    
    console.log('\n5️⃣ Test creazione paziente semplificato...');
    
    // Usa i claims più recenti
    const { data: { user: currentUser } } = await supabase.auth.getUser();
    const currentClinicId = currentUser?.app_metadata?.clinic_id || '00000000-0000-0000-0000-000000000000';
    
    const testPatient = {
      first_name: 'Test',
      last_name: 'Debug',
      phone: '+39 ************',
      date_of_birth: '1990-01-01',
      address: 'Via Test 123',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      anamnesis: 'Test debug',
      medical_history: 'Nessuna',
      clinic_id: currentClinicId
    };
    
    console.log('📝 Tentativo inserimento paziente...');
    console.log(`  - Clinic ID utilizzato: ${testPatient.clinic_id}`);
    
    const { data: newPatient, error: patientError } = await supabase
      .from('patients')
      .insert(testPatient)
      .select()
      .single();
    
    if (patientError) {
      console.log('\n❌ ERRORE CREAZIONE PAZIENTE:');
      console.log('  - Codice:', patientError.code);
      console.log('  - Messaggio:', patientError.message);
      console.log('  - Dettagli:', patientError.details);
      console.log('  - Hint:', patientError.hint);
      
      if (patientError.code === '42501') {
        console.log('\n🔧 DIAGNOSI RLS:');
        console.log('  - RLS sta bloccando l\'inserimento');
        console.log('  - Verifica che i JWT claims siano corretti');
        console.log('  - Verifica che le policy RLS usino il path corretto');
      } else if (patientError.code === 'PGRST204') {
        console.log('\n🔧 DIAGNOSI SCHEMA:');
        console.log('  - Problema con lo schema della tabella');
        console.log('  - Colonna mancante o nome errato');
      }
    } else {
      console.log('\n🎉 SUCCESSO! Paziente creato:');
      console.log(`  - ID: ${newPatient.id}`);
      console.log(`  - Nome: ${newPatient.first_name} ${newPatient.last_name}`);
      console.log(`  - Clinic ID: ${newPatient.clinic_id}`);
    }
    
    console.log('\n6️⃣ Verifica policy RLS...');
    
    // Test query per verificare le policy
    const { data: policies, error: policiesError } = await supabase
      .rpc('exec_sql', { 
        sql_query: `
          SELECT policyname, cmd, qual 
          FROM pg_policies 
          WHERE schemaname = 'public' 
          AND tablename = 'patients' 
          AND policyname LIKE 'clinic_isolation_%'
          ORDER BY cmd;
        `
      });
    
    if (policiesError) {
      console.log('⚠️ Non posso verificare le policy RLS direttamente');
    } else {
      console.log('📋 Policy RLS per patients:');
      policies.forEach(policy => {
        console.log(`  - ${policy.policyname} (${policy.cmd}): ${policy.qual || 'null'}`);
      });
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

debugNewUserIssue().then(() => {
  console.log('\n🏁 Debug completato');
  console.log('\n📋 PROSSIMI PASSI:');
  console.log('1. Se JWT claims mancano: Edge Function non funziona automaticamente');
  console.log('2. Se RLS blocca: Policy potrebbero essere ancora sbagliate');
  console.log('3. Se schema error: Problema con struttura tabella');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
