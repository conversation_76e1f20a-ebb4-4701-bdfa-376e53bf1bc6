/**
 * MOKO SOSTANZA Dental CRM - File Store
 * 
 * Zustand store per gestione stato file pazienti
 * Supporta upload OTP, CBCT, documenti
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { FileService, PatientFile, FILE_CATEGORIES } from '../services/FileService';

// Type alias per compatibilità
export type PatientFileDB = PatientFile;

export interface FileUploadProgress {
  fileId: string;
  filename: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
}

interface FileState {
  // State - seguendo pattern useGlobalStore
  files: PatientFileDB[];
  loading: boolean;
  uploading: boolean;
  uploadProgress: FileUploadProgress[];
  error: string | null;
  
  // Actions - API semplificata come richiesto
  load: (patientId: string) => Promise<void>;
  upload: (patientId: string, file: File, category: 'otp' | 'cbct' | 'document') => Promise<boolean>;
  softDelete: (id: string) => Promise<boolean>;
  
  // Utility actions
  getFileUrl: (filePath: string) => Promise<string | null>;
  clearFiles: () => void;
  clearError: () => void;
  
  // Computed
  getFilesByCategory: (category: string) => PatientFileDB[];
  getTotalFileSize: () => number;
  getFileStats: () => Record<string, number>;
}

export const useFileStore = create<FileState>()(
  devtools(
    (set, get) => ({
      // Initial state
      files: [],
      uploading: false,
      uploadProgress: [],
      loading: false,
      error: null,

      // Load patient files - API semplificata
      load: async (patientId: string) => {
        set({ loading: true, error: null });
        
        try {
          const files = await FileService.getPatientFiles(patientId);
          set({ files, loading: false });
        } catch (error) {
          console.error('Error loading patient files:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Errore nel caricamento file',
            loading: false 
          });
        }
      },

      // Upload file - API semplificata con optimistic update
      upload: async (patientId: string, file: File, category: 'otp' | 'cbct' | 'document'): Promise<boolean> => {
        console.log('🏪 FileStore: Starting upload', { patientId, fileName: file.name, category });
        
        const fileId = `${Date.now()}_${file.name}`;
        
        // Add to upload progress
        set(state => ({
          uploading: true,
          uploadProgress: [
            ...state.uploadProgress,
            {
              fileId,
              filename: file.name,
              progress: 0,
              status: 'uploading'
            }
          ]
        }));

        try {
          // Update progress
          set(state => ({
            uploadProgress: state.uploadProgress.map(p => 
              p.fileId === fileId ? { ...p, progress: 50 } : p
            )
          }));

          console.log('🏪 FileStore: Calling FileService.uploadFile');
          const result = await FileService.uploadFile(file, patientId, category);
          console.log('🏪 FileStore: Upload result', result);

          if (result.success && result.file) {
            // Update progress to success
            set(state => ({
              uploadProgress: state.uploadProgress.map(p => 
                p.fileId === fileId ? { ...p, progress: 100, status: 'success' } : p
              ),
              files: [result.file!, ...state.files]
            }));

            // Remove from progress after delay
            setTimeout(() => {
              set(state => ({
                uploadProgress: state.uploadProgress.filter(p => p.fileId !== fileId)
              }));
            }, 2000);

            console.log('✅ FileStore: Upload successful');
            return true;
          } else {
            // Update progress to error
            set(state => ({
              uploadProgress: state.uploadProgress.map(p => 
                p.fileId === fileId ? { 
                  ...p, 
                  status: 'error', 
                  error: result.error || 'Upload fallito' 
                } : p
              ),
              error: result.error || 'Upload fallito'
            }));

            console.error('❌ FileStore: Upload failed', result.error);
            return false;
          }
        } catch (error) {
          console.error('❌ FileStore: Upload error', error);
          const errorMessage = error instanceof Error ? error.message : 'Errore durante upload';
          
          set(state => ({
            uploadProgress: state.uploadProgress.map(p => 
              p.fileId === fileId ? { 
                ...p, 
                status: 'error', 
                error: errorMessage 
              } : p
            ),
            error: errorMessage
          }));

          return false;
        } finally {
          // Check if any uploads are still in progress
          const state = get();
          const stillUploading = state.uploadProgress.some(p => p.status === 'uploading');
          if (!stillUploading) {
            set({ uploading: false });
          }
        }
      },



      // Soft delete file - API semplificata con optimistic update
      softDelete: async (id: string): Promise<boolean> => {
        console.log('🗂️ Store: Starting soft delete for file ID:', id);
        
        // Trova il file per ottenere il patient_id prima di rimuoverlo
        const fileToDelete = get().files.find(f => f.id === id);
        const patientId = fileToDelete?.patient_id;

        if (!patientId) {
          console.error('❌ Store: Patient ID not found for file:', id);
          return false;
        }

        console.log('📋 Store: File found, patient ID:', patientId);

        // Optimistic update - rimuovi subito dalla UI
        set(state => ({
          files: state.files.filter(f => f.id !== id)
        }));

        console.log('🔄 Store: Optimistic update applied, calling FileService...');

        try {
          const success = await FileService.deleteFile(id);
          
          if (!success) {
            // Rollback optimistic update se fallisce
            console.warn('⚠️ Store: Soft delete failed, rolling back...');
            const allFiles = await FileService.getPatientFiles(patientId);
            set({ files: allFiles });
          } else {
            console.log('✅ Store: Soft delete successful');
          }

          return success;
        } catch (error) {
          console.error('❌ Store: Soft delete error:', error);
          set({ error: error instanceof Error ? error.message : 'Errore durante eliminazione' });
          
          // Rollback optimistic update
          console.log('🔄 Store: Rolling back due to error...');
          const allFiles = await FileService.getPatientFiles(patientId);
          set({ files: allFiles });
          
          return false;
        }
      },

      // Get file URL
      getFileUrl: async (filePath: string): Promise<string | null> => {
        try {
          return await FileService.getFileUrl(filePath);
        } catch (error) {
          console.error('Get file URL error:', error);
          set({ error: error instanceof Error ? error.message : 'Errore nel recupero URL' });
          return null;
        }
      },

      // Clear files
      clearFiles: () => {
        set({ 
          files: [], 
          uploadProgress: [],
          error: null 
        });
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Get files by category
      getFilesByCategory: (category: string): PatientFile[] => {
        return get().files.filter(file => file.category === category);
      },

      // Get total file size
      getTotalFileSize: (): number => {
        return get().files.reduce((total, file) => total + file.size, 0);
      },

      // Get file statistics
      getFileStats: (): Record<string, number> => {
        const files = get().files;
        return files.reduce((stats, file) => {
          const category = file.category || 'uncategorized';
          stats[category] = (stats[category] || 0) + 1;
          return stats;
        }, {} as Record<string, number>);
      }
    }),
    {
      name: 'file-store', // for devtools
    }
  )
);

// Utility hooks for specific categories
export const useOTPFiles = (patientId?: string) => {
  const { files, load, getFilesByCategory } = useFileStore();
  
  const otpFiles = getFilesByCategory('otp');
  
  const loadOTPFiles = async () => {
    if (patientId) {
      await load(patientId);
    }
  };

  return { otpFiles, loadOTPFiles };
};

export const useTACFiles = (patientId?: string) => {
  const { files, load, getFilesByCategory } = useFileStore();
  
  const tacFiles = getFilesByCategory('tac');
  
  const loadTACFiles = async () => {
    if (patientId) {
      await load(patientId);
    }
  };

  return { tacFiles, loadTACFiles };
};

export const useConeBeamFiles = (patientId?: string) => {
  const { files, load, getFilesByCategory } = useFileStore();
  
  const coneBeamFiles = getFilesByCategory('cone_beam');
  
  const loadConeBeamFiles = async () => {
    if (patientId) {
      await load(patientId);
    }
  };

  return { coneBeamFiles, loadConeBeamFiles };
};

export default useFileStore;