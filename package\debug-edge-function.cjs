/**
 * Debug dettagliato dell'Edge Function issue-jwt
 * Verifica se l'Edge Function crea correttamente il record utente
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 === DEBUG EDGE FUNCTION ISSUE-JWT ===\n');

async function debugEdgeFunction() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // Genera email unica per il test
    const timestamp = Date.now();
    const testEmail = `debug.edge.${timestamp}@demo.com`;
    const testPassword = 'demo123456';
    
    console.log('1️⃣ Registrazione nuovo utente...');
    console.log(`📧 Email: ${testEmail}`);
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Debug',
          last_name: 'Edge',
          clinic_name: 'Demo Clinic',
          clinic_vat_number: '12345678901'
        }
      }
    });
    
    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }
    
    console.log('✅ Registrazione completata');
    console.log(`  - User ID: ${signUpData.user?.id}`);
    
    console.log('\n2️⃣ Verifica JWT claims iniziali...');
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('❌ Errore recupero utente:', userError?.message);
      return;
    }
    
    console.log('📋 JWT claims iniziali:');
    console.log('  - App Metadata:', JSON.stringify(user.app_metadata, null, 2));
    console.log('  - User Metadata:', JSON.stringify(user.user_metadata, null, 2));
    
    console.log('\n3️⃣ Verifica record in public.users PRIMA di Edge Function...');
    
    const { data: usersBefore, error: usersBeforeError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id);
    
    if (usersBeforeError) {
      console.log('❌ Errore verifica users (prima):', usersBeforeError.message);
    } else {
      console.log(`📊 Record trovati (prima): ${usersBefore.length}`);
      if (usersBefore.length > 0) {
        console.log('📋 Record esistente:', usersBefore[0]);
      }
    }
    
    console.log('\n4️⃣ Chiamata Edge Function con logging dettagliato...');
    
    const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
      body: { user: user }
    });
    
    if (functionError) {
      console.log('❌ Errore Edge Function:', functionError.message);
      console.log('📋 Dettagli completi:', functionError);
      return;
    }
    
    console.log('✅ Edge Function eseguita');
    console.log('📋 Risultato completo:', JSON.stringify(functionResult, null, 2));
    
    console.log('\n5️⃣ Verifica record in public.users DOPO Edge Function...');
    
    const { data: usersAfter, error: usersAfterError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id);
    
    if (usersAfterError) {
      console.log('❌ Errore verifica users (dopo):', usersAfterError.message);
    } else {
      console.log(`📊 Record trovati (dopo): ${usersAfter.length}`);
      if (usersAfter.length > 0) {
        console.log('📋 Record creato/aggiornato:', usersAfter[0]);
      } else {
        console.log('❌ PROBLEMA: Record utente non creato dall\'Edge Function');
      }
    }
    
    console.log('\n6️⃣ Verifica JWT claims aggiornati...');
    
    const { data: { user: updatedUser }, error: updateError } = await supabase.auth.getUser();
    
    if (updateError || !updatedUser) {
      console.log('❌ Errore ricarica utente:', updateError?.message);
    } else {
      console.log('📋 JWT claims aggiornati:');
      console.log('  - App Metadata:', JSON.stringify(updatedUser.app_metadata, null, 2));
      
      const clinicId = updatedUser.app_metadata?.clinic_id;
      const appRole = updatedUser.app_metadata?.app_role;
      
      if (clinicId && appRole) {
        console.log(`✅ JWT claims presenti - Clinic ID: ${clinicId}, Role: ${appRole}`);
      } else {
        console.log('❌ JWT claims ancora mancanti');
      }
    }
    
    console.log('\n7️⃣ Verifica clinica demo...');
    
    const { data: demoClinic, error: clinicError } = await supabase
      .from('clinics')
      .select('*')
      .eq('id', '00000000-0000-0000-0000-000000000000');
    
    if (clinicError) {
      console.log('❌ Errore verifica clinica:', clinicError.message);
    } else if (demoClinic.length === 0) {
      console.log('❌ PROBLEMA: Clinica demo non esiste!');
      console.log('🔧 Questo potrebbe essere il motivo per cui l\'Edge Function fallisce');
    } else {
      console.log('✅ Clinica demo trovata:', demoClinic[0].name);
    }
    
    console.log('\n8️⃣ Test creazione paziente finale...');
    
    const finalUser = updatedUser || user;
    const finalClinicId = finalUser.app_metadata?.clinic_id || '00000000-0000-0000-0000-000000000000';
    
    const testPatient = {
      first_name: 'Debug',
      last_name: 'Final',
      phone: '+39 ************',
      date_of_birth: '1985-01-01',
      address: 'Via Debug',
      city: 'Test',
      postal_code: '12345',
      province: 'TS',
      anamnesis: 'Debug test',
      medical_history: 'None',
      clinic_id: finalClinicId
    };
    
    const { data: newPatient, error: patientError } = await supabase
      .from('patients')
      .insert(testPatient)
      .select()
      .single();
    
    if (patientError) {
      console.log('\n❌ CREAZIONE PAZIENTE FALLITA:');
      console.log(`  - Codice: ${patientError.code}`);
      console.log(`  - Messaggio: ${patientError.message}`);
      console.log(`  - Clinic ID usato: ${finalClinicId}`);
      console.log(`  - JWT claims: ${JSON.stringify(finalUser.app_metadata)}`);
    } else {
      console.log('\n🎉 SUCCESSO! Paziente creato:');
      console.log(`  - ID: ${newPatient.id}`);
      console.log(`  - Nome: ${newPatient.first_name} ${newPatient.last_name}`);
      console.log(`  - Clinic ID: ${newPatient.clinic_id}`);
    }
    
    // Logout
    await supabase.auth.signOut();
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

debugEdgeFunction().then(() => {
  console.log('\n🏁 Debug Edge Function completato');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
