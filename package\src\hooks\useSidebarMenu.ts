/**
 * Hook per la selezione del menu della sidebar in base al ruolo dell'utente
 * Aggiornato per utilizzare Supabase auth invece di localStorage
 */
import { MenuItem, dentistMenu, clinicMenu } from '../constants/menus';
import { useAuth } from './useAuth';

/**
 * Hook che restituisce il menu appropriato in base al ruolo dell'utente
 */
export const useSidebarMenu = (): MenuItem[] => {
  const { user, loading } = useAuth();

  console.log('🔍 [useSidebarMenu] Current state:', { user, loading });

  // Se l'utente non è autenticato o stiamo caricando, restituisci un array vuoto
  if (loading || !user) {
    console.log('⏳ [useSidebarMenu] User not authenticated or loading, returning empty menu');
    return [];
  }

  // TUTTI GLI UTENTI RICEVONO IL MENU COMPLETO DELLA CLINICA
  console.log('👤 [useSidebarMenu] User role detected:', user.role);
  console.log('🏥 [useSidebarMenu] Returning complete clinicMenu for all users (as requested)');
  console.log('📋 [useSidebarMenu] ClinicMenu sections:', clinicMenu.map(section => section.label));

  // Restituisci sempre il menu completo della clinica
  return clinicMenu;
};

export default useSidebarMenu;
