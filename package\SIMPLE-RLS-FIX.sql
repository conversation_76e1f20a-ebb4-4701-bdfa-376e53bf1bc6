-- ===================================================================
-- SCRIPT SQL SEMPLIFICATO PER FIX RLS PATIENTS
-- ===================================================================
-- 
-- ISTRUZIONI:
-- 1. Copia TUTTO questo script
-- 2. Vai su Supabase Dashboard > SQL Editor
-- 3. Incolla e clicca "Run"
-- 4. Verifica che tutte le query vengano eseguite senza errori
-- 
-- ===================================================================

-- STEP 1: Mostra stato attuale
SELECT 'STEP 1: STATO ATTUALE RLS PATIENTS' as step;

SELECT 
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

SELECT 
  policyname,
  cmd
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- STEP 2: Disabilita RLS temporaneamente
SELECT 'STEP 2: DISABILITA RLS TEMPORANEAMENTE' as step;

ALTER TABLE public.patients DISABLE ROW LEVEL SECURITY;

-- STEP 3: Elimina TUTTE le policy esistenti
SELECT 'STEP 3: ELIMINA TUTTE LE POLICY ESISTENTI' as step;

DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_delete" ON public.patients;
DROP POLICY IF EXISTS "patients_select" ON public.patients;
DROP POLICY IF EXISTS "patients_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_update" ON public.patients;
DROP POLICY IF EXISTS "patients_delete" ON public.patients;
DROP POLICY IF EXISTS "select_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "insert_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "update_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "delete_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_select_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_insert_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_update_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_delete_v2" ON public.patients;

-- STEP 4: Verifica che non ci siano più policy
SELECT 'STEP 4: VERIFICA NESSUNA POLICY RIMASTA' as step;

SELECT COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- STEP 5: Riabilita RLS
SELECT 'STEP 5: RIABILITA RLS' as step;

ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;

-- STEP 6: Crea policy SELECT
SELECT 'STEP 6A: CREA POLICY SELECT' as step;

CREATE POLICY "patients_rls_select_v2" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- STEP 7: Crea policy INSERT
SELECT 'STEP 6B: CREA POLICY INSERT' as step;

CREATE POLICY "patients_rls_insert_v2" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- STEP 8: Crea policy UPDATE
SELECT 'STEP 6C: CREA POLICY UPDATE' as step;

CREATE POLICY "patients_rls_update_v2" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- STEP 9: Crea policy DELETE
SELECT 'STEP 6D: CREA POLICY DELETE' as step;

CREATE POLICY "patients_rls_delete_v2" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- STEP 10: Verifica policy create
SELECT 'STEP 7: VERIFICA POLICY CREATE' as step;

SELECT 
  policyname,
  cmd,
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
ORDER BY cmd;

-- STEP 11: Verifica RLS abilitato
SELECT 'STEP 8: VERIFICA RLS ABILITATO' as step;

SELECT 
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- STEP 12: Test JWT path
SELECT 'STEP 9: TEST JWT PATH (se autenticato)' as step;

SELECT 
  'JWT Test' as test,
  auth.jwt() -> 'app_metadata' as app_metadata,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_extracted,
  (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid as clinic_id_as_uuid,
  auth.uid() as user_id;

-- STEP 13: Messaggio finale
SELECT 'STEP 10: COMPLETATO - POLICY RLS PATIENTS RICREATE' as step;

-- ===================================================================
-- RISULTATO ATTESO:
-- 
-- Dovresti vedere:
-- - STEP 1: Stato iniziale (RLS enabled, policy esistenti)
-- - STEP 2: RLS disabilitato
-- - STEP 3: Policy eliminate (nessun errore)
-- - STEP 4: 0 policy rimanenti
-- - STEP 5: RLS riabilitato
-- - STEP 6A-6D: 4 policy create (nessun errore)
-- - STEP 7: 4 policy elencate (patients_rls_*_v2)
-- - STEP 8: RLS enabled = true
-- - STEP 9: JWT test (se autenticato)
-- - STEP 10: Completato
-- 
-- SE VEDI ERRORI:
-- - Copia l'errore e segnalalo
-- - Non procedere se ci sono errori nelle policy
-- 
-- ===================================================================
