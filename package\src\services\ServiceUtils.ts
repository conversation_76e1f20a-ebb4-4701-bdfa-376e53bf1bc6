/**
 * MOKO SOSTANZA Dental CRM - Service Utils
 * Utility functions shared across all services for clinic isolation
 */

import { supabase } from '../lib/supabase';

/**
 * Retrieves the clinic_id from authenticated user's JWT claims
 * Falls back to Demo Clinic for development if no clinic_id in claims
 */
export async function getClinicId(): Promise<string> {
  try {
    // Get current user from auth
    const { data: { user }, error } = await supabase.auth.getUser();

    // If no user is authenticated, return a hardcoded demo clinic ID for development
    if (error || !user) {
      console.warn('⚠️ [ServiceUtils] No authenticated user, using hardcoded demo clinic for development');
      // Return hardcoded UUID for demo clinic (this should match the one in the database)
      return '00000000-0000-0000-0000-000000000000';
    }

    // 🔍 DEBUG: Log dettagliato per identificare il problema
    console.log('🔍 [DEBUG] getClinicId - Informazioni utente:');
    console.log('  - User ID:', user.id);
    console.log('  - Email:', user.email);
    console.log('  - App Metadata:', JSON.stringify(user.app_metadata, null, 2));
    console.log('  - User Metadata:', JSON.stringify(user.user_metadata, null, 2));

    // Extract clinic_id from user metadata or JWT claims
    let clinicId = user.app_metadata?.clinic_id || user.user_metadata?.clinic_id;

    console.log('  - Clinic ID estratto:', clinicId);
    
    // 🔧 TEMPORANEO: Assegnazione clinic_id basata sull'email per test isolamento
    if (!clinicId) {
      console.warn('⚠️ [ServiceUtils] No clinic_id in user claims, usando assegnazione temporanea basata su email');

      // Assegnazione temporanea per test isolamento multi-clinica
      if (user.email?.includes('clinica-a') || user.email?.includes('test1') || user.email?.includes('user1')) {
        clinicId = '11111111-1111-1111-1111-111111111111'; // Clinica A
        console.log('📍 [DEBUG] Assegnato temporaneamente alla Clinica A per test isolamento');
      } else if (user.email?.includes('clinica-b') || user.email?.includes('test2') || user.email?.includes('user2')) {
        clinicId = '22222222-2222-2222-2222-222222222222'; // Clinica B
        console.log('📍 [DEBUG] Assegnato temporaneamente alla Clinica B per test isolamento');
      } else {
        // Fallback alla clinica demo per tutti gli altri
        console.log('📍 [DEBUG] Fallback alla Clinica Demo per utente:', user.email);
      
        // Try to get demo clinic ID, but if it fails due to RLS, use hardcoded value
        try {
          const { data: demoClinic, error: clinicError } = await supabase
            .from('clinics')
            .select('id')
            .eq('name', 'Demo Clinic')
            .single();

          if (!clinicError && demoClinic) {
            clinicId = demoClinic.id;
          } else {
            clinicId = '00000000-0000-0000-0000-000000000000';
          }
        } catch (clinicError) {
          console.warn('⚠️ [ServiceUtils] Could not fetch Demo Clinic due to RLS, using hardcoded ID');
          clinicId = '00000000-0000-0000-0000-000000000000';
        }
      }
    }

    console.log('🎯 [DEBUG] Clinic ID finale utilizzato:', clinicId);
    return clinicId;
  } catch (error) {
    console.error('❌ [ServiceUtils] Error getting clinic_id, using hardcoded demo clinic:', error);
    // Final fallback for development
    return '00000000-0000-0000-0000-000000000000';
  }
}

/**
 * Injects clinic_id into data object for create operations
 */
export async function withClinicId<T extends Record<string, any>>(data: T): Promise<T & { clinic_id: string }> {
  const clinicId = await getClinicId();
  return { ...data, clinic_id: clinicId };
}
