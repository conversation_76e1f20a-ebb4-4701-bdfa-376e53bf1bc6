/**
 * MOKO SOSTANZA Dental CRM - Patient Event Service
 * Servizio per la gestione degli eventi dei pazienti con integrazione Supabase
 */

import { supabase } from '../lib/supabase';
import { withClinicId } from './ServiceUtils';
import type { Database } from '../types/database';

// Types from database
type PatientEvent = Database['public']['Tables']['patient_events']['Row'];
type PatientEventInsert = Database['public']['Tables']['patient_events']['Insert'];
type PatientEventUpdate = Database['public']['Tables']['patient_events']['Update'];

// Extended types
export interface PatientEventWithAttachments extends PatientEvent {
  attachments?: EventAttachment[];
}

export interface EventAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadDate: string;
}

export interface CreatePatientEventData extends Omit<PatientEventInsert, 'clinic_id'> {
  attachments?: File[];
}

export class PatientEventService {
  /**
   * Ottieni tutti gli eventi di un paziente
   */
  static async getEventsByPatient(patientId: string): Promise<PatientEvent[]> {
    try {
      const { data, error } = await supabase
        .from('patient_events')
        .select('*')
        .eq('patient_id', patientId)
        .order('date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching patient events:', error);
      throw error;
    }
  }

  /**
   * Ottieni un evento per ID
   */
  static async getEventById(id: number): Promise<PatientEvent | null> {
    try {
      const { data, error } = await supabase
        .from('patient_events')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Error fetching event by ID:', error);
      throw error;
    }
  }

  /**
   * Crea un nuovo evento
   */
  static async createEvent(eventData: CreatePatientEventData): Promise<PatientEvent> {
    try {
      // Inject clinic_id automatically
      const { attachments, ...eventFields } = eventData;
      const eventWithClinic = await withClinicId(eventFields);

      const { data, error } = await supabase
        .from('patient_events')
        .insert(eventWithClinic)
        .select()
        .single();

      if (error) throw error;

      // TODO: Handle attachments upload to storage if needed
      if (attachments && attachments.length > 0) {
        console.log('Attachment handling not implemented yet');
      }

      return data;
    } catch (error) {
      console.error('Error creating patient event:', error);
      throw error;
    }
  }

  /**
   * Aggiorna un evento esistente
   */
  static async updateEvent(id: number, eventData: PatientEventUpdate): Promise<PatientEvent> {
    try {
      const { data, error } = await supabase
        .from('patient_events')
        .update(eventData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating patient event:', error);
      throw error;
    }
  }

  /**
   * Elimina un evento
   */
  static async deleteEvent(id: number): Promise<void> {
    try {
      const { error } = await supabase
        .from('patient_events')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting patient event:', error);
      throw error;
    }
  }

  /**
   * Ottieni eventi recenti (per dashboard)
   */
  static async getRecentEvents(limit: number = 10): Promise<PatientEvent[]> {
    try {
      const { data, error } = await supabase
        .from('patient_events')
        .select(`
          *,
          patients!inner (
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching recent events:', error);
      throw error;
    }
  }

  /**
   * Cerca eventi per testo
   */
  static async searchEvents(query: string, patientId?: string): Promise<PatientEvent[]> {
    try {
      let queryBuilder = supabase
        .from('patient_events')
        .select('*')
        .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
        .order('date', { ascending: false });

      if (patientId) {
        queryBuilder = queryBuilder.eq('patient_id', patientId);
      }

      const { data, error } = await queryBuilder;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching events:', error);
      throw error;
    }
  }
}

// Utility functions
export const getEventTypeIcon = (type: string): string => {
  switch (type) {
    case 'visita': return 'solar:stethoscope-outline';
    case 'prescrizione': return 'solar:pill-outline';
    case 'analisi': return 'solar:test-tube-outline';
    case 'nota': return 'solar:notes-outline';
    case 'altro': return 'solar:file-text-outline';
    default: return 'solar:document-outline';
  }
};

export const getEventTypeLabel = (type: string): string => {
  switch (type) {
    case 'visita': return 'Visita';
    case 'prescrizione': return 'Prescrizione';
    case 'analisi': return 'Analisi';
    case 'nota': return 'Nota';
    case 'altro': return 'Altro';
    default: return 'Evento';
  }
};

export const getEventTypeColor = (type: string): string => {
  switch (type) {
    case 'visita': return 'blue';
    case 'prescrizione': return 'green';
    case 'analisi': return 'yellow';
    case 'nota': return 'gray';
    case 'altro': return 'purple';
    default: return 'gray';
  }
};

// Export types for external use
export type { 
  PatientEvent, 
  PatientEventInsert, 
  PatientEventUpdate
};
