/**
 * Test completo del flusso nuovo utente
 * Simula registrazione, verifica Edge Function e creazione paziente
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🆕 === TEST FLUSSO NUOVO UTENTE ===\n');

async function testNewUserFlow() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // Genera email unica per il test
    const timestamp = Date.now();
    const testEmail = `test.newuser.${timestamp}@demo.com`;
    const testPassword = 'demo123456';
    
    console.log('1️⃣ Registrazione nuovo utente...');
    console.log(`📧 Email: ${testEmail}`);
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Nuo<PERSON>',
          last_name: 'Utente',
          clinic_name: 'Demo Clinic',
          clinic_vat_number: '12345678901'
        }
      }
    });
    
    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }
    
    console.log('✅ Registrazione completata');
    console.log(`  - User ID: ${signUpData.user?.id}`);
    console.log(`  - Email: ${signUpData.user?.email}`);
    
    // Attendi un momento per permettere ai trigger di attivarsi
    console.log('\n⏳ Attesa 3 secondi per trigger automatici...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n2️⃣ Verifica JWT claims iniziali...');
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('❌ Errore recupero utente:', userError?.message);
      return;
    }
    
    console.log('📋 JWT claims iniziali:');
    console.log('  - App Metadata:', JSON.stringify(user.app_metadata, null, 2));
    console.log('  - User Metadata:', JSON.stringify(user.user_metadata, null, 2));
    
    const initialClinicId = user.app_metadata?.clinic_id;
    const initialAppRole = user.app_metadata?.app_role;
    
    if (!initialClinicId) {
      console.log('⚠️ JWT claims mancanti - Edge Function non eseguita automaticamente');
      
      console.log('\n3️⃣ Chiamata manuale Edge Function...');
      
      const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
        body: { user: user }
      });
      
      if (functionError) {
        console.log('❌ Errore Edge Function:', functionError.message);
        console.log('📋 Dettagli:', functionError);
        return;
      }
      
      console.log('✅ Edge Function eseguita con successo');
      console.log('📋 Risultato:', functionResult);
      
      // Ricarica utente per vedere i nuovi claims
      const { data: { user: updatedUser }, error: updateError } = await supabase.auth.getUser();
      
      if (updateError || !updatedUser) {
        console.log('❌ Errore ricarica utente:', updateError?.message);
        return;
      }
      
      console.log('\n📋 JWT claims aggiornati:');
      console.log('  - App Metadata:', JSON.stringify(updatedUser.app_metadata, null, 2));
      
      const updatedClinicId = updatedUser.app_metadata?.clinic_id;
      const updatedAppRole = updatedUser.app_metadata?.app_role;
      
      if (updatedClinicId) {
        console.log(`✅ JWT claims ora presenti - Clinic ID: ${updatedClinicId}, Role: ${updatedAppRole}`);
      } else {
        console.log('❌ JWT claims ancora mancanti dopo Edge Function');
        return;
      }
    } else {
      console.log(`✅ JWT claims già presenti - Clinic ID: ${initialClinicId}, Role: ${initialAppRole}`);
    }
    
    console.log('\n4️⃣ Verifica record in public.users...');
    
    const { data: userRecords, error: userRecordsError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id);
    
    if (userRecordsError) {
      console.log('❌ Errore verifica users:', userRecordsError.message);
    } else if (userRecords.length === 0) {
      console.log('⚠️ Record utente non trovato in public.users');
    } else {
      console.log('✅ Record utente trovato in public.users:');
      const userRecord = userRecords[0];
      console.log(`  - ID: ${userRecord.id}`);
      console.log(`  - Email: ${userRecord.email}`);
      console.log(`  - Clinic ID: ${userRecord.clinic_id}`);
      console.log(`  - Role: ${userRecord.role}`);
    }
    
    console.log('\n5️⃣ Test creazione paziente...');
    
    // Usa i claims più aggiornati
    const { data: { user: finalUser } } = await supabase.auth.getUser();
    const finalClinicId = finalUser?.app_metadata?.clinic_id || '00000000-0000-0000-0000-000000000000';
    
    const testPatient = {
      first_name: 'Paziente',
      last_name: 'Nuovo',
      phone: '+39 ************',
      date_of_birth: '1985-05-15',
      address: 'Via Nuovo Utente 456',
      city: 'Roma',
      postal_code: '00100',
      province: 'RM',
      fiscal_code: '****************',
      anamnesis: 'Paziente di test per nuovo utente',
      medical_history: 'Nessuna patologia',
      allergies: null,
      medications: null,
      is_smoker: false,
      anamnesi_signed: false,
      udi: null,
      clinic_id: finalClinicId
    };
    
    console.log('📝 Dati paziente:');
    console.log(`  - Nome: ${testPatient.first_name} ${testPatient.last_name}`);
    console.log(`  - Clinic ID: ${testPatient.clinic_id}`);
    
    const { data: newPatient, error: patientError } = await supabase
      .from('patients')
      .insert(testPatient)
      .select()
      .single();
    
    if (patientError) {
      console.log('\n❌ ERRORE CREAZIONE PAZIENTE:');
      console.log('  - Codice:', patientError.code);
      console.log('  - Messaggio:', patientError.message);
      console.log('  - Dettagli:', patientError.details);
      console.log('  - Hint:', patientError.hint);
      
      console.log('\n🔧 POSSIBILI CAUSE:');
      if (patientError.code === '42501') {
        console.log('  - RLS policy sta bloccando l\'inserimento');
        console.log('  - JWT claims potrebbero non essere riconosciuti dalle policy');
        console.log('  - Verifica che le policy usino il path corretto: auth.jwt() -> \'app_metadata\' ->> \'clinic_id\'');
      } else if (patientError.code === 'PGRST204') {
        console.log('  - Schema della tabella non corretto');
        console.log('  - Colonna mancante o nome errato');
      } else if (patientError.code === '23503') {
        console.log('  - Violazione foreign key constraint');
        console.log('  - clinic_id non esiste nella tabella clinics');
      }
    } else {
      console.log('\n🎉 SUCCESSO! Paziente creato correttamente:');
      console.log(`  - ID: ${newPatient.id}`);
      console.log(`  - Nome: ${newPatient.first_name} ${newPatient.last_name}`);
      console.log(`  - Clinic ID: ${newPatient.clinic_id}`);
      console.log(`  - Data creazione: ${newPatient.created_at}`);
      
      console.log('\n✅ IL FLUSSO NUOVO UTENTE FUNZIONA CORRETTAMENTE!');
    }
    
    console.log('\n6️⃣ Test isolamento...');
    
    const { data: visiblePatients, error: patientsError } = await supabase
      .from('patients')
      .select('id, first_name, last_name, clinic_id');
    
    if (patientsError) {
      console.log('❌ Errore recupero pazienti:', patientsError.message);
    } else {
      console.log(`📊 Pazienti visibili: ${visiblePatients.length}`);
      const ownClinicPatients = visiblePatients.filter(p => p.clinic_id === finalClinicId);
      const otherClinicPatients = visiblePatients.filter(p => p.clinic_id !== finalClinicId);
      
      console.log(`  - Propria clinica: ${ownClinicPatients.length}`);
      console.log(`  - Altre cliniche: ${otherClinicPatients.length}`);
      
      if (otherClinicPatients.length === 0) {
        console.log('✅ Isolamento perfetto!');
      } else {
        console.log('⚠️ Problema isolamento - vede pazienti di altre cliniche');
      }
    }
    
    // Logout
    await supabase.auth.signOut();
    console.log('\n✅ Logout effettuato');
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

testNewUserFlow().then(() => {
  console.log('\n🏁 Test nuovo utente completato');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
