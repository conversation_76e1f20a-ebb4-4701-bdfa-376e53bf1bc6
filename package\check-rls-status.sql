-- Script per verificare lo stato delle policy RLS
-- Esegui questo script nel dashboard Supabase per verificare la configurazione

-- 1. Verifica se RLS è abilitato sulle tabelle principali
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled,
  CASE 
    WHEN rowsecurity THEN '✅ ABILITATO'
    ELSE '❌ DISABILITATO'
  END as status
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN (
    'patients', 'doctors', 'appointments', 'treatments', 'invoices',
    'reminders', 'patient_files', 'patient_events', 'dental_procedures',
    'doctor_availability', 'doctor_specialties', 'event_attachments',
    'invoice_items', 'products', 'medical_devices'
  )
ORDER BY tablename;

-- 2. Verifica policy di isolamento esistenti
SELECT 
  schemaname,
  tablename,
  policyname,
  cmd as operation,
  CASE 
    WHEN policyname LIKE '%clinic_isolation%' THEN '✅ POLICY ISOLAMENTO'
    ELSE '⚠️ ALTRA POLICY'
  END as type
FROM pg_policies 
WHERE schemaname = 'public'
  AND tablename IN (
    'patients', 'doctors', 'appointments', 'treatments', 'invoices',
    'reminders', 'patient_files', 'patient_events', 'dental_procedures',
    'doctor_availability', 'doctor_specialties', 'event_attachments',
    'invoice_items', 'products', 'medical_devices'
  )
ORDER BY tablename, cmd;

-- 3. Conta policy per tabella
SELECT 
  tablename,
  COUNT(*) as total_policies,
  COUNT(CASE WHEN policyname LIKE '%clinic_isolation%' THEN 1 END) as isolation_policies,
  CASE 
    WHEN COUNT(CASE WHEN policyname LIKE '%clinic_isolation%' THEN 1 END) >= 4 THEN '✅ COMPLETO'
    WHEN COUNT(CASE WHEN policyname LIKE '%clinic_isolation%' THEN 1 END) > 0 THEN '⚠️ PARZIALE'
    ELSE '❌ MANCANTE'
  END as isolation_status
FROM pg_policies 
WHERE schemaname = 'public'
  AND tablename IN (
    'patients', 'doctors', 'appointments', 'treatments', 'invoices',
    'reminders', 'patient_files', 'patient_events', 'dental_procedures',
    'doctor_availability', 'doctor_specialties', 'event_attachments',
    'invoice_items', 'products', 'medical_devices'
  )
GROUP BY tablename
ORDER BY tablename;

-- 4. Verifica colonne clinic_id
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable,
  column_default,
  CASE 
    WHEN column_name = 'clinic_id' AND data_type = 'uuid' THEN '✅ CORRETTO'
    WHEN column_name = 'clinic_id' THEN '⚠️ TIPO ERRATO'
    ELSE '❌ MANCANTE'
  END as clinic_id_status
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name IN (
    'patients', 'doctors', 'appointments', 'treatments', 'invoices',
    'reminders', 'patient_files', 'patient_events', 'dental_procedures',
    'doctor_availability', 'doctor_specialties', 'event_attachments',
    'invoice_items', 'products', 'medical_devices'
  )
  AND column_name = 'clinic_id'
ORDER BY table_name;

-- 5. Verifica foreign key constraints per clinic_id
SELECT 
  tc.table_name,
  tc.constraint_name,
  tc.constraint_type,
  kcu.column_name,
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name,
  CASE 
    WHEN tc.constraint_type = 'FOREIGN KEY' AND kcu.column_name = 'clinic_id' THEN '✅ FK CORRETTO'
    ELSE '⚠️ ALTRO CONSTRAINT'
  END as status
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.table_schema = 'public'
  AND tc.table_name IN (
    'patients', 'doctors', 'appointments', 'treatments', 'invoices',
    'reminders', 'patient_files', 'patient_events', 'dental_procedures',
    'doctor_availability', 'doctor_specialties', 'event_attachments',
    'invoice_items', 'products', 'medical_devices'
  )
  AND kcu.column_name = 'clinic_id'
ORDER BY tc.table_name;

-- 6. Test di esempio per verificare che RLS funzioni
-- ATTENZIONE: Questo test potrebbe fallire se RLS è configurato correttamente
-- e l'utente corrente non ha clinic_id nei metadati

-- Test 1: Conta pazienti senza filtro (dovrebbe essere limitato da RLS)
SELECT 
  'Test RLS Pazienti' as test_name,
  COUNT(*) as count_without_filter,
  'Se vedi questo risultato, RLS potrebbe non essere attivo' as note
FROM patients;

-- Test 2: Conta pazienti per clinic_id (per vedere la distribuzione)
SELECT 
  'Distribuzione Pazienti' as info,
  clinic_id,
  COUNT(*) as patient_count,
  CASE 
    WHEN clinic_id = '00000000-0000-0000-0000-000000000000' THEN 'Clinica Demo'
    WHEN clinic_id = '11111111-1111-1111-1111-111111111111' THEN 'Clinica Test A'
    WHEN clinic_id = '22222222-2222-2222-2222-222222222222' THEN 'Clinica Test B'
    ELSE 'Clinica Sconosciuta'
  END as clinic_name
FROM patients 
GROUP BY clinic_id
ORDER BY patient_count DESC;

-- 7. Riepilogo finale
SELECT 
  'RIEPILOGO CONFIGURAZIONE RLS' as summary,
  (SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'public' AND rowsecurity = true 
   AND tablename IN ('patients', 'doctors', 'appointments', 'treatments', 'invoices')) as tables_with_rls,
  (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public' 
   AND policyname LIKE '%clinic_isolation%') as isolation_policies_count,
  (SELECT COUNT(DISTINCT table_name) FROM information_schema.columns 
   WHERE table_schema = 'public' AND column_name = 'clinic_id'
   AND table_name IN ('patients', 'doctors', 'appointments', 'treatments', 'invoices')) as tables_with_clinic_id;

-- 8. Istruzioni per risolvere problemi comuni
SELECT 
  'ISTRUZIONI RISOLUZIONE PROBLEMI' as help,
  'Se RLS non è abilitato: ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;' as enable_rls,
  'Se mancano policy: Esegui le migrazioni RLS dal file 20250728130000_enable_rls_clinic_isolation.sql' as missing_policies,
  'Se manca clinic_id: Esegui le migrazioni dal file 20250728120000_add_clinic_id_remaining_tables.sql' as missing_clinic_id;
