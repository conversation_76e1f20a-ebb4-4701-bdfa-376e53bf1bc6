-- Sincronizzazione utenti esistenti da auth.users a public.users
-- Risolve il problema degli utenti registrati prima dell'implementazione del trigger

-- Funzione per sincronizzare un utente esistente
CREATE OR REPLACE FUNCTION sync_existing_auth_user(user_id uuid)
R<PERSON>UR<PERSON> void AS $$
DECLARE
  auth_user_record record;
  clinic_id_val uuid;
  first_name_val text;
  last_name_val text;
  role_val text;
BEGIN
  -- Recupera i dati dell'utente da auth.users
  SELECT * INTO auth_user_record
  FROM auth.users
  WHERE id = user_id;

  IF NOT FOUND THEN
    RAISE LOG 'User % not found in auth.users', user_id;
    RETURN;
  END IF;

  -- <PERSON>la se l'utente esiste già in public.users
  IF EXISTS (SELECT 1 FROM public.users WHERE id = user_id) THEN
    RAISE LOG 'User % already exists in public.users', user_id;
    RETURN;
  END IF;

  -- Estrai dati dai metadata
  first_name_val := COALESCE(
    auth_user_record.raw_user_meta_data->>'first_name',
    auth_user_record.raw_app_meta_data->>'first_name',
    'Nome'
  );

  last_name_val := COALESCE(
    auth_user_record.raw_user_meta_data->>'last_name',
    auth_user_record.raw_app_meta_data->>'last_name',
    'Cognome'
  );

  role_val := COALESCE(
    auth_user_record.raw_app_meta_data->>'role',
    auth_user_record.raw_user_meta_data->>'role',
    'manager'
  );

  -- Cerca clinic_id dai metadata o dalla tabella clinics
  clinic_id_val := COALESCE(
    (auth_user_record.raw_app_meta_data->>'clinic_id')::uuid,
    (auth_user_record.raw_user_meta_data->>'clinic_id')::uuid
  );

  -- Se non abbiamo clinic_id, prova a trovarlo dalla P.IVA
  IF clinic_id_val IS NULL AND auth_user_record.raw_user_meta_data ? 'clinic_vat_number' THEN
    SELECT id INTO clinic_id_val
    FROM public.clinics
    WHERE vat_number = auth_user_record.raw_user_meta_data->>'clinic_vat_number'
    LIMIT 1;
  END IF;

  -- Inserisci il record in public.users
  INSERT INTO public.users (
    id, email, first_name, last_name, role, clinic_id
  ) VALUES (
    user_id,
    auth_user_record.email,
    first_name_val,
    last_name_val,
    role_val,
    clinic_id_val
  );

  RAISE LOG 'Synced user % (%) to public.users with role % and clinic_id %',
            user_id, auth_user_record.email, role_val, clinic_id_val;

EXCEPTION WHEN OTHERS THEN
  RAISE LOG 'Error syncing user %: %', user_id, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Sincronizza tutti gli utenti esistenti che non sono in public.users
DO $$
DECLARE
  auth_user_record record;
  synced_count integer := 0;
BEGIN
  RAISE NOTICE 'Starting sync of existing auth users to public.users...';

  FOR auth_user_record IN
    SELECT au.id, au.email
    FROM auth.users au
    LEFT JOIN public.users pu ON au.id = pu.id
    WHERE pu.id IS NULL
      AND au.email IS NOT NULL
  LOOP
    BEGIN
      PERFORM sync_existing_auth_user(auth_user_record.id);
      synced_count := synced_count + 1;
      RAISE NOTICE 'Synced user: % (%)', auth_user_record.email, auth_user_record.id;
    EXCEPTION WHEN OTHERS THEN
      RAISE WARNING 'Failed to sync user % (%): %',
                    auth_user_record.email, auth_user_record.id, SQLERRM;
    END;
  END LOOP;

  RAISE NOTICE 'Sync completed. % users synchronized.', synced_count;
END $$;

-- Cleanup: rimuovi la funzione helper dopo l'uso
DROP FUNCTION IF EXISTS sync_existing_auth_user(uuid);