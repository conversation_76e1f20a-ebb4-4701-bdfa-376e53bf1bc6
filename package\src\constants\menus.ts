/**
 * Definizione delle interfacce e dei menu per la sidebar del gestionale dentistico
 */

export interface MenuItem {
  label: string;
  icon: string;
  href?: string;
  children?: MenuItem[];
}

/**
 * Menu per il dentista
 */
export const dentistMenu: MenuItem[] = [
  {
    label: "HOME",
    icon: "",
    children: [
      {
        label: "Dashboard",
        icon: "solar:widget-add-line-duotone",
        href: "/"
      },
      {
        label: "Calendario",
        icon: "solar:calendar-mark-line-duotone",
        href: "/calendar"
      }
    ]
  },
  {
    label: "GESTIONE",
    icon: "",
    children: [
      {
        label: "Appuntamenti",
        icon: "solar:calendar-mark-line-duotone",
        children: [
          {
            label: "I miei appuntamenti",
            icon: "solar:clock-circle-outline",
            href: "/appointments"
          },
          {
            label: "Nuovo appuntamento",
            icon: "solar:add-square-outline",
            href: "/appointments/new"
          }
        ]
      },
      {
        label: "<PERSON>ient<PERSON>",
        icon: "solar:users-group-rounded-line-duotone",
        children: [
          {
            label: "Elenco pazienti",
            icon: "solar:users-group-rounded-bold",
            href: "/patients"
          },
          {
            label: "Nuovo paziente",
            icon: "solar:add-circle-outline",
            href: "/patients/new"
          },
          {
            label: "Ricerca paziente",
            icon: "solar:magnifer-line-duotone",
            href: "/patients/search"
          }
        ]
      },
      {
        label: "Trattamenti",
        icon: "solar:stethoscope-outline",
        href: "/treatments"
      },
      {
        label: "Contabilità",
        icon: "solar:calculator-outline",
        children: [
          {
            label: "Nuova Fattura",
            icon: "solar:add-square-outline",
            href: "/billing/new"
          },
          {
            label: "Elenco Fatture",
            icon: "solar:file-text-outline",
            href: "/billing/invoices"
          },
          {
            label: "Reportistica",
            icon: "solar:chart-line-duotone",
            href: "/accounting/reports"
          }
        ]
      },
      {
        label: "Magazzino",
        icon: "solar:box-outline",
        children: [
          {
            label: "Visualizza",
            icon: "solar:eye-outline",
            href: "/inventory"
          },
          {
            label: "Aggiungi",
            icon: "solar:add-circle-outline",
            href: "/inventory/add"
          },
          {
            label: "Modifica",
            icon: "solar:pen-outline",
            href: "/inventory/edit"
          }
        ]
      }
    ]
  },
  {
    label: "ALTRE",
    icon: "",
    children: [
      {
        label: "Profilo",
        icon: "solar:user-outline",
        href: "/profile"
      },
      {
        label: "Impostazioni",
        icon: "solar:settings-outline",
        href: "/settings"
      }
    ]
  }
];

/**
 * Menu per la clinica
 */
export const clinicMenu: MenuItem[] = [
  {
    label: "HOME",
    icon: "",
    children: [
      {
        label: "Dashboard",
        icon: "solar:widget-add-line-duotone",
        href: "/dashboard"
      },
      {
        label: "Calendario",
        icon: "solar:calendar-mark-line-duotone",
        href: "/calendar"
      }
    ]
  },
  {
    label: "GESTIONE",
    icon: "",
    children: [
      {
        label: "Appuntamenti",
        icon: "solar:calendar-mark-line-duotone",
        children: [
          {
            label: "Tutti gli appuntamenti",
            icon: "solar:clock-circle-outline",
            href: "/appointments"
          },
          {
            label: "Nuovo appuntamento",
            icon: "solar:add-square-outline",
            href: "/appointments/new"
          }
        ]
      },
      {
        label: "Pazienti",
        icon: "solar:users-group-rounded-line-duotone",
        children: [
          {
            label: "Elenco pazienti",
            icon: "solar:users-group-rounded-bold",
            href: "/patients"
          },
          {
            label: "Nuovo paziente",
            icon: "solar:add-circle-outline",
            href: "/patients/new"
          },
          {
            label: "Ricerca paziente",
            icon: "solar:eye-outline",
            href: "/patients/search"
          }
        ]
      },
      {
        label: "Dottori",
        icon: "solar:user-id-outline",
        children: [
          {
            label: "Elenco dottori",
            icon: "solar:user-id-bold-duotone",
            href: "/doctors"
          },
          {
            label: "Nuovo dottore",
            icon: "solar:add-circle-outline",
            href: "/doctors/new"
          },
          {
            label: "Ricerca dottore",
            icon: "solar:eye-outline",
            href: "/doctors/search"
          }
        ]
      },
      {
        label: "Personale",
        icon: "solar:users-group-rounded-line-duotone",
        children: [
          {
            label: "Elenco personale",
            icon: "solar:user-id-broken",
            href: "/staff"
          },
          {
            label: "Nuovo dipendente",
            icon: "solar:add-circle-outline",
            href: "/staff/new"
          },
          {
            label: "Ricerca dipendente",
            icon: "solar:eye-outline",
            href: "/staff/search"
          },
          {
            label: "Presenze",
            icon: "solar:checklist-minimalistic-outline",
            href: "/staff/attendance"
          }
        ]
      },      {
        label: "Reparti",
        icon: "solar:structure-broken",
        children: [
          {
            label: "Lista Reparti",
            icon: "solar:list-linear",
            href: "/departments"
          },
          {
            label: "Nuovo Reparto",
            icon: "solar:add-circle-outline",
            href: "/departments/new"
          }
        ]
      },
      {
        label: "Stanze",
        icon: "solar:armchair-outline",
        children: [
          {
            label: "Lista Stanze",
            icon: "solar:list-linear",
            href: "/rooms"
          },
          {
            label: "Nuova Stanza",
            icon: "solar:add-circle-outline",
            href: "/rooms/new"
          }
        ]
      },
      {
        label: "Trattamenti",
        icon: "solar:stethoscope-outline",
        href: "/treatments"
      },
      {
        label: "Magazzino",
        icon: "solar:box-outline",
        children: [
          {
            label: "Elenco Prodotti",
            icon: "solar:eye-outline",
            href: "/inventory"
          },
          {
            label: "Nuovo Prodotto",
            icon: "solar:add-circle-outline",
            href: "/inventory/add"
          }
        ]
      },
      {
        label: "Contabilità",
        icon: "solar:calculator-outline",
        children: [
          {
            label: "Nuova Fattura",
            icon: "solar:add-square-outline",
            href: "/billing/new"
          },
          {
            label: "Elenco Fatture",
            icon: "solar:file-text-outline",
            href: "/billing/invoices"
          },
          {
            label: "Reportistica",
            icon: "solar:chart-line-duotone",
            href: "/accounting/reports"
          }
        ]
      },
      {
        label: "Eventi",
        icon: "solar:calendar-mark-line-duotone",
        href: "/events"
      },
      {
        label: "Galleria",
        icon: "solar:gallery-outline",
        href: "/gallery"
      }
    ]
  },
  {
    label: "ALTRE",
    icon: "",
    children: [
      {
        label: "Profilo",
        icon: "solar:user-outline",
        href: "/profile"
      },
      {
        label: "Impostazioni",
        icon: "solar:settings-outline",
        href: "/settings"
      }
    ]
  }
];
