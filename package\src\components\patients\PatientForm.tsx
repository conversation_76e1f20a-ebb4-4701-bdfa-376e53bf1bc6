import { useState, useEffect } from 'react';
import { Button, Label, TextInput, Select, Textarea } from 'flowbite-react';
import { useNavigate } from 'react-router-dom';
import { PatientService } from '../../services/PatientService';
import { usePatientStore } from '../../store/usePatientStore';
import { useToast } from '../shared/Toast';
import FileUpload from './FileUpload';
import FileList from './FileList';

interface PatientFormProps {
  isEdit?: boolean;
  patientData?: {
    id?: string; // Cambiato da number a string
    name: string;
    phone: string;
    email: string;
    birthdate: string;
    gender: string;
    address: string;
    notes: string;
    udiCode?: string;
    fiscalCode?: string;
    medicalHistory?: string;
    isSmoker?: boolean;
    medications?: string;
    anamnesis?: string;
    udi?: string;
    anamnesi_signed?: boolean;
  };
}

const PatientForm = ({ isEdit = false, patientData }: PatientFormProps) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { showSuccess, showError, ToastContainer } = useToast();
  
  // Store Zustand per gestire i nuovi campi
  const { setUdi, setAnamnesiSigned } = usePatientStore();

  const [formData, setFormData] = useState({
    name: patientData?.name || '',
    phone: patientData?.phone || '',
    email: patientData?.email || '',
    birthdate: patientData?.birthdate || '',
    gender: patientData?.gender || 'M',
    address: patientData?.address || '',
    notes: patientData?.notes || '',
    udiCode: patientData?.udiCode || patientData?.udi || '', // Mappa udi -> udiCode
    fiscalCode: patientData?.fiscalCode || '',
    medicalHistory: patientData?.medicalHistory || '',
    isSmoker: patientData?.isSmoker || false,
    medications: patientData?.medications || '',
    anamnesis: patientData?.anamnesis || '',
    // Solo il campo anamnesi_signed è nuovo
    anamnesi_signed: patientData?.anamnesi_signed || false
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      // Prepara i dati per il database
      const newPatientData = {
        first_name: formData.name.split(' ')[0] || '',
        last_name: formData.name.split(' ').slice(1).join(' ') || '',
        email: formData.email || null,
        phone: formData.phone,
        date_of_birth: formData.birthdate,
        fiscal_code: formData.fiscalCode || null,
        address: formData.address,
        city: formData.address.split(',')[1]?.trim() || '', // Estrae la città dall'indirizzo
        postal_code: '', // Da aggiungere un campo separato se necessario
        province: '', // Da aggiungere un campo separato se necessario
        medical_history: formData.medicalHistory,
        allergies: null, // Da aggiungere se hai un campo allergies
        medications: formData.medications || null,
        is_smoker: formData.isSmoker,
        anamnesis: formData.anamnesis,
        // Mappa il campo esistente udiCode al campo Supabase udi
        udi: formData.udiCode,
        anamnesi_signed: formData.anamnesi_signed
      };

      if (isEdit && patientData?.id) {
        // Aggiorna paziente esistente
        console.log('🔄 Updating patient with ID:', patientData.id);
        await PatientService.updatePatient(patientData.id, newPatientData);
        showSuccess('Paziente aggiornato con successo!');
        console.log('✅ Paziente aggiornato con successo');
      } else {
        // Crea nuovo paziente
        const newPatient = await PatientService.createPatient(newPatientData);
        showSuccess('Nuovo paziente creato con successo!');
        console.log('Nuovo paziente creato:', newPatient);
      }

      // Reindirizza alla lista pazienti dopo il salvataggio
      setTimeout(() => navigate('/patients'), 1500); // Delay per mostrare il toast
      
    } catch (error) {
      console.error('Errore durante il salvataggio del paziente:', error);
      showError('Errore durante il salvataggio del paziente. Riprova.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form className="space-y-4" onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="name" value="Nome e Cognome" />
          </div>
          <TextInput
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Mario Rossi"
            required
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="phone" value="Telefono" />
          </div>
          <TextInput
            id="phone"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleChange}
            placeholder="+39 333 1234567"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="email" value="Email" />
          </div>
          <TextInput
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="birthdate" value="Data di nascita" />
          </div>
          <TextInput
            id="birthdate"
            name="birthdate"
            type="date"
            value={formData.birthdate}
            onChange={handleChange}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="gender" value="Genere" />
          </div>
          <Select
            id="gender"
            name="gender"
            value={formData.gender}
            onChange={handleChange}
            required
          >
            <option value="M">Maschio</option>
            <option value="F">Femmina</option>
            <option value="A">Altro</option>
          </Select>
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="address" value="Indirizzo" />
          </div>
          <TextInput
            id="address"
            name="address"
            value={formData.address}
            onChange={handleChange}
            placeholder="Via Roma 123, Milano"
          />
        </div>

        <div>
          <div className="mb-2 block">
            <Label htmlFor="fiscalCode" value="Codice Fiscale" />
          </div>
          <TextInput
            id="fiscalCode"
            name="fiscalCode"
            value={formData.fiscalCode}
            onChange={handleChange}
            placeholder="****************"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <div className="mb-2 block">
            <Label htmlFor="udiCode" value="Codice UDI" />
          </div>
          <TextInput
            id="udiCode"
            name="udiCode"
            value={formData.udiCode}
            onChange={handleChange}
            placeholder="***********"
          />
        </div>

        <div className="flex items-center mt-8">
          <div className="flex items-center">
            <input
              id="anamnesi_signed"
              name="anamnesi_signed"
              type="checkbox"
              checked={formData.anamnesi_signed}
              onChange={handleChange}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label htmlFor="anamnesi_signed" className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
              Anamnesi Firmata
            </label>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="flex items-center mt-8">
          <div className="flex items-center">
            <input
              id="isSmoker"
              name="isSmoker"
              type="checkbox"
              checked={formData.isSmoker}
              onChange={handleChange}
              className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary"
            />
            <Label htmlFor="isSmoker" value="Fumatore" className="ms-2" />
          </div>
        </div>
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="medicalHistory" value="Patologie Pregresse" />
        </div>
        <Textarea
          id="medicalHistory"
          name="medicalHistory"
          value={formData.medicalHistory}
          onChange={handleChange}
          placeholder="Inserisci eventuali patologie pregresse del paziente..."
          rows={3}
        />
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="medications" value="Farmaci" />
        </div>
        <Textarea
          id="medications"
          name="medications"
          value={formData.medications}
          onChange={handleChange}
          placeholder="Inserisci i farmaci che il paziente assume regolarmente..."
          rows={3}
        />
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="anamnesis" value="Anamnesi" />
        </div>
        <Textarea
          id="anamnesis"
          name="anamnesis"
          value={formData.anamnesis}
          onChange={handleChange}
          placeholder="Inserisci l'anamnesi completa del paziente..."
          rows={4}
        />
      </div>

      <div>
        <div className="mb-2 block">
          <Label htmlFor="notes" value="Note" />
        </div>
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleChange}
          placeholder="Inserisci eventuali note sul paziente..."
          rows={3}
        />
      </div>

      {/* Sezione Allegati Diagnostici - Solo per pazienti esistenti */}
      {isEdit && patientData?.id && (
        <div className="border-t pt-6 mt-8">
          <div className="mb-4">
            <Label value="📎 Allegati Diagnostici" className="text-lg font-semibold" />
            <p className="text-sm text-gray-500 mt-1">
              Carica file OTP, TAC, Cone Beam e altri documenti diagnostici del paziente
            </p>
          </div>
          
          <div className="space-y-4">
            <FileUpload patientId={patientData.id} />
            <FileList patientId={patientData.id} />
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3">
        <Button color="light" onClick={() => navigate('/patients')} disabled={isSubmitting}>
          Annulla
        </Button>
        <Button type="submit" color="primary" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              {isEdit ? 'Aggiornamento...' : 'Salvataggio...'}
            </>
          ) : (
            isEdit ? 'Aggiorna' : 'Salva'
          )}
        </Button>
      </div>

      {/* Toast Container per feedback utente */}
      <ToastContainer />
    </form>
  );
};

export default PatientForm;
