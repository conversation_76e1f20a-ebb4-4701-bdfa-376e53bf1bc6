/**
 * <PERSON><PERSON>O SOSTANZA Dental CRM - File List Component
 * 
 * Componente per visualizzare griglia file con preview e azioni
 */

import React, { useState } from 'react';
import { Card, Badge, Button, Tooltip, Modal } from 'flowbite-react';
import { Icon } from '@iconify/react';
import { useFileStore, PatientFileDB } from '../../store/useFileStore';
import { useToast } from '../shared/Toast';

interface FileListProps {
  patientId: string;
  category?: string;
  onFileDeleted?: () => void;
}

const FileList: React.FC<FileListProps> = ({ 
  patientId, 
  category,
  onFileDeleted 
}) => {
  const { files, softDelete, getFileUrl, getFilesByCategory } = useFileStore();
  const { showSuccess, showError } = useToast();
  const [deleteModal, setDeleteModal] = useState<{ open: boolean; file: PatientFileDB | null }>({
    open: false,
    file: null
  });
  const [previewModal, setPreviewModal] = useState<{ open: boolean; file: PatientFileDB | null; url?: string }>({
    open: false,
    file: null,
    url: undefined
  });
  const [loadingStates, setLoadingStates] = useState<{
    preview: string | null;
    download: string | null;
  }>({
    preview: null,
    download: null
  });

  const displayFiles = category ? getFilesByCategory(category) : files;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string, category: string): string => {
    if (mimeType.startsWith('image/')) {
      return 'solar:gallery-bold';
    }
    if (mimeType === 'application/pdf') {
      return 'solar:file-text-bold';
    }
    if (mimeType === 'application/dicom') {
      return 'solar:scanner-bold';
    }
    
    // Category-based icons
    switch (category) {
      case 'otp':
        return 'solar:medical-kit-bold';
      case 'cbct':
        return 'solar:scanner-bold';
      default:
        return 'solar:document-bold';
    }
  };

  const getCategoryBadge = (category: string) => {
    const configs = {
      otp: { color: 'info', label: 'OTP' },
      cbct: { color: 'purple', label: 'CBCT' },
      tac: { color: 'purple', label: 'TAC' },
      cone_beam: { color: 'purple', label: 'Cone Beam' },
      document: { color: 'gray', label: 'Documento' },
      image: { color: 'green', label: 'Immagine' }
    };
    
    const config = configs[category as keyof typeof configs] || configs.document;
    return <Badge color={config.color} size="sm">{config.label}</Badge>;
  };

  const handleDelete = async (file: PatientFileDB) => {
    try {
      const success = await softDelete(file.id);
      if (success) {
        showSuccess(`${file.filename} eliminato con successo`);
        onFileDeleted?.();
      } else {
        showError('Errore durante l\'eliminazione del file');
      }
    } catch (error) {
      console.error('Delete error:', error);
      showError('Errore durante l\'eliminazione del file');
    } finally {
      setDeleteModal({ open: false, file: null });
    }
  };

  const handlePreview = async (file: PatientFileDB) => {
    try {
      setLoadingStates(prev => ({ ...prev, preview: file.id }));
      
      const url = await getFileUrl(file.path);
      if (url) {
        // Always show in modal for preview
        setPreviewModal({ open: true, file, url });
      } else {
        showError('Impossibile aprire il file');
      }
    } catch (error) {
      console.error('Preview error:', error);
      showError('Errore nell\'apertura del file');
    } finally {
      setLoadingStates(prev => ({ ...prev, preview: null }));
    }
  };

  const handleDownload = async (file: PatientFileDB) => {
    try {
      setLoadingStates(prev => ({ ...prev, download: file.id }));
      
      const url = await getFileUrl(file.path);
      if (url) {
        // Fetch the file as blob to force download
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        
        const blob = await response.blob();
        
        // Create blob URL and download
        const blobUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = file.filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up blob URL
        window.URL.revokeObjectURL(blobUrl);
        
        showSuccess(`${file.filename} scaricato con successo`);
      } else {
        showError('Impossibile scaricare il file');
      }
    } catch (error) {
      console.error('Download error:', error);
      showError('Errore nel download del file');
    } finally {
      setLoadingStates(prev => ({ ...prev, download: null }));
    }
  };

  if (displayFiles.length === 0) {
    return (
      <Card className="text-center py-8">
        <div className="space-y-4">
          <Icon icon="solar:folder-open-outline" height={48} className="text-gray-400 mx-auto" />
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Nessun file caricato</h3>
            <p className="text-gray-500">
              {category 
                ? `Non ci sono file di tipo ${category} per questo paziente`
                : 'Non ci sono file caricati per questo paziente'
              }
            </p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {displayFiles.map((file) => (
          <Card key={file.id} className="relative group hover:shadow-lg transition-shadow">
            <div className="space-y-3">
              {/* File Preview */}
              <div className="relative">
                <div className="aspect-square bg-gray-50 rounded-lg flex items-center justify-center overflow-hidden">
                  {file.mime_type.startsWith('image/') ? (
                    <img
                      src={`data:${file.mime_type};base64,placeholder`} // Placeholder - in produzione usare getFileUrl
                      alt={file.filename}
                      className="w-full h-full object-cover cursor-pointer"
                      onClick={() => handlePreview(file)}
                      onError={(e) => {
                        // Fallback to icon if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        target.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                  ) : null}
                  
                  <div className={`flex items-center justify-center ${file.mime_type.startsWith('image/') ? 'hidden' : ''}`}>
                    <Icon 
                      icon={getFileIcon(file.mime_type, file.category || 'document')} 
                      height={48} 
                      className="text-gray-400"
                    />
                  </div>
                </div>

                {/* Category Badge */}
                <div className="absolute top-2 left-2">
                  {getCategoryBadge(file.category || 'document')}
                </div>

                {/* Action Buttons - Show on Hover */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="flex gap-1">
                    <Tooltip content="Anteprima">
                      <Button
                        size="xs"
                        color="light"
                        onClick={() => handlePreview(file)}
                        className="!p-1"
                        disabled={loadingStates.preview === file.id || loadingStates.download === file.id}
                      >
                        {loadingStates.preview === file.id ? (
                          <div className="animate-spin h-3.5 w-3.5 border border-gray-400 border-t-transparent rounded-full"></div>
                        ) : (
                          <Icon icon="solar:eye-outline" height={14} />
                        )}
                      </Button>
                    </Tooltip>
                    
                    <Tooltip content="Scarica">
                      <Button
                        size="xs"
                        color="light"
                        onClick={() => handleDownload(file)}
                        className="!p-1"
                        disabled={loadingStates.preview === file.id || loadingStates.download === file.id}
                      >
                        {loadingStates.download === file.id ? (
                          <div className="animate-spin h-3.5 w-3.5 border border-gray-400 border-t-transparent rounded-full"></div>
                        ) : (
                          <Icon icon="solar:download-outline" height={14} />
                        )}
                      </Button>
                    </Tooltip>
                    
                    <Tooltip content="Elimina">
                      <Button
                        size="xs"
                        color="failure"
                        onClick={() => setDeleteModal({ open: true, file })}
                        className="!p-1"
                      >
                        <Icon icon="solar:trash-bin-trash-outline" height={14} />
                      </Button>
                    </Tooltip>
                  </div>
                </div>
              </div>

              {/* File Info */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm truncate" title={file.filename}>
                  {file.filename}
                </h4>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{formatFileSize(file.size)}</span>
                  <span>{new Date(file.created_at || new Date()).toLocaleDateString('it-IT')}</span>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal
        show={deleteModal.open}
        onClose={() => setDeleteModal({ open: false, file: null })}
        size="md"
      >
        <Modal.Header>Conferma Eliminazione</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>
              Sei sicuro di voler eliminare il file <strong>{deleteModal.file?.filename}</strong>?
            </p>
            <p className="text-sm text-gray-500">
              Il file verrà spostato nel cestino e potrà essere recuperato entro 30 giorni.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            color="failure"
            onClick={() => deleteModal.file && handleDelete(deleteModal.file)}
          >
            Elimina
          </Button>
          <Button
            color="gray"
            onClick={() => setDeleteModal({ open: false, file: null })}
          >
            Annulla
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Preview Modal */}
      <Modal
        show={previewModal.open}
        onClose={() => setPreviewModal({ open: false, file: null, url: undefined })}
        size="4xl"
      >
        <Modal.Header>
          {previewModal.file?.filename}
        </Modal.Header>
        <Modal.Body>
          <div className="flex justify-center">
            {previewModal.file?.mime_type.startsWith('image/') ? (
              previewModal.url ? (
                <img
                  src={previewModal.url}
                  alt={previewModal.file.filename}
                  className="max-w-full max-h-96 object-contain"
                  onError={() => showError('Errore nel caricamento dell\'immagine')}
                />
              ) : (
                <div className="flex items-center justify-center h-96 w-full">
                  <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
                </div>
              )
            ) : previewModal.file?.mime_type === 'application/pdf' ? (
              previewModal.url ? (
                <iframe
                  src={previewModal.url}
                  className="w-full h-96 border border-gray-200"
                  title={previewModal.file.filename}
                />
              ) : (
                <div className="flex items-center justify-center h-96 w-full">
                  <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
                </div>
              )
            ) : (
              <div className="text-center py-8">
                <Icon icon="solar:file-outline" height={48} className="text-gray-400 mx-auto mb-4" />
                <p>Anteprima non disponibile per questo tipo di file</p>
                <Button 
                  size="sm" 
                  className="mt-4"
                  onClick={() => previewModal.file && handleDownload(previewModal.file)}
                  disabled={loadingStates.download === previewModal.file?.id}
                >
                  {loadingStates.download === previewModal.file?.id ? (
                    <>
                      <div className="animate-spin h-4 w-4 border border-white border-t-transparent rounded-full mr-2"></div>
                      Scaricando...
                    </>
                  ) : (
                    'Scarica per visualizzare'
                  )}
                </Button>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            onClick={() => previewModal.url && window.open(previewModal.url, '_blank')}
            color="light"
          >
            <Icon icon="solar:external-link-outline" height={16} className="mr-2" />
            Apri in nuova tab
          </Button>
          <Button
            onClick={() => previewModal.file && handleDownload(previewModal.file)}
            disabled={loadingStates.download === previewModal.file?.id}
          >
            {loadingStates.download === previewModal.file?.id ? (
              <>
                <div className="animate-spin h-4 w-4 border border-white border-t-transparent rounded-full mr-2"></div>
                Scaricando...
              </>
            ) : (
              <>
                <Icon icon="solar:download-outline" height={16} className="mr-2" />
                Scarica
              </>
            )}
          </Button>
          <Button
            color="gray"
            onClick={() => setPreviewModal({ open: false, file: null, url: undefined })}
          >
            Chiudi
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default FileList;