/**
 * <PERSON><PERSON><PERSON> SOSTANZA Dental CRM - Enhanced Doctor Store
 * 
 * Zustand store per la gestione dello stato dei dottori
 * Include gestione ottimistica e cache
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { DoctorSer<PERSON>, type DoctorWithRelations, type CreateDoctorData, type DoctorSearchFilters, type PaginationOptions } from '../services/DoctorService';

interface DoctorState {
  // State
  doctors: DoctorWithRelations[];
  currentDoctor: DoctorWithRelations | null;
  isLoading: boolean;
  error: string | null;
  searchQuery: string;
  filters: DoctorSearchFilters;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };

  // Actions
  fetchDoctors: (filters?: DoctorSearchFilters, pagination?: PaginationOptions) => Promise<void>;
  fetchDoctorById: (id: number) => Promise<void>;
  createDoctor: (data: CreateDoctorData) => Promise<DoctorWithRelations>;
  updateDoctor: (id: number, updates: Partial<CreateDoctorData>) => Promise<DoctorWithRelations>;
  deleteDoctor: (id: number) => Promise<void>;
  searchDoctors: (query: string) => Promise<void>;
  setCurrentDoctor: (doctor: DoctorWithRelations | null) => void;
  setFilters: (filters: DoctorSearchFilters) => void;
  setPagination: (pagination: Partial<PaginationOptions>) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  doctors: [],
  currentDoctor: null,
  isLoading: false,
  error: null,
  searchQuery: '',
  filters: {},
  pagination: {
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0
  }
};

export const useDoctorStore = create<DoctorState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      fetchDoctors: async (filters = {}, pagination = {}) => {
        set({ isLoading: true, error: null });
        
        try {
          const result = await DoctorService.getDoctors(filters, pagination);
          
          set({
            doctors: result.doctors,
            pagination: result.pagination,
            filters,
            isLoading: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Errore nel caricamento dei dottori',
            isLoading: false
          });
        }
      },

      fetchDoctorById: async (id: number) => {
        set({ isLoading: true, error: null });
        
        try {
          const doctor = await DoctorService.getDoctorById(id);
          
          set({
            currentDoctor: doctor,
            isLoading: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Errore nel caricamento del dottore',
            currentDoctor: null,
            isLoading: false
          });
        }
      },

      createDoctor: async (data: CreateDoctorData) => {
        set({ isLoading: true, error: null });
        
        try {
          const newDoctor = await DoctorService.createDoctor(data);
          
          // Aggiornamento ottimistico dello stato
          set((state) => ({
            doctors: [newDoctor, ...state.doctors],
            pagination: {
              ...state.pagination,
              total: state.pagination.total + 1
            },
            isLoading: false
          }));
          
          return newDoctor;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Errore nella creazione del dottore',
            isLoading: false
          });
          throw error;
        }
      },

      updateDoctor: async (id: number, updates: Partial<CreateDoctorData>) => {
        set({ isLoading: true, error: null });
        
        try {
          const updatedDoctor = await DoctorService.updateDoctor(id, updates);
          
          // Aggiornamento ottimistico dello stato
          set((state) => ({
            doctors: state.doctors.map(doctor =>
              doctor.id === id ? updatedDoctor : doctor
            ),
            currentDoctor: state.currentDoctor?.id === id ? updatedDoctor : state.currentDoctor,
            isLoading: false
          }));
          
          return updatedDoctor;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Errore nell\'aggiornamento del dottore',
            isLoading: false
          });
          throw error;
        }
      },

      deleteDoctor: async (id: number) => {
        set({ isLoading: true, error: null });
        
        try {
          await DoctorService.deleteDoctor(id);
          
          // Aggiornamento ottimistico dello stato
          set((state) => ({
            doctors: state.doctors.filter(doctor => doctor.id !== id),
            currentDoctor: state.currentDoctor?.id === id ? null : state.currentDoctor,
            pagination: {
              ...state.pagination,
              total: Math.max(0, state.pagination.total - 1)
            },
            isLoading: false
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Errore nell\'eliminazione del dottore',
            isLoading: false
          });
          throw error;
        }
      },

      searchDoctors: async (query: string) => {
        set({ isLoading: true, error: null, searchQuery: query });
        
        try {
          if (!query.trim()) {
            // Se la query è vuota, ricarica tutti i dottori
            const { filters, pagination } = get();
            await get().fetchDoctors(filters, pagination);
            return;
          }

          const doctors = await DoctorService.searchDoctors(query);
          
          set({
            doctors,
            isLoading: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Errore nella ricerca dei dottori',
            isLoading: false
          });
        }
      },

      setCurrentDoctor: (doctor: DoctorWithRelations | null) => {
        set({ currentDoctor: doctor });
      },

      setFilters: (filters: DoctorSearchFilters) => {
        set({ filters });
      },

      setPagination: (newPagination: Partial<PaginationOptions>) => {
        set((state) => ({
          pagination: {
            ...state.pagination,
            ...newPagination
          }
        }));
      },

      clearError: () => {
        set({ error: null });
      },

      reset: () => {
        set(initialState);
      }
    }),
    {
      name: 'doctor-store'
    }
  )
);
