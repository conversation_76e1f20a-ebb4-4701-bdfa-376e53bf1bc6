/**
 * MOKO SOSTANZA Dental CRM - InvoiceService Unit Tests
 * Test per i metodi CRUD e soft-delete del servizio fatture
 */

import InvoiceService, { type InvoiceWithDetails, type InvoiceItem } from '../InvoiceService';
import { supabase } from '../../lib/supabase';
import type { Database } from '../../types/database';

// Mock del client Supabase
jest.mock('../../lib/supabase', () => ({
  supabase: {
    from: jest.fn(),
    rpc: jest.fn(),
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
}));

type InvoiceInsert = Database['public']['Tables']['invoices']['Insert'];
type InvoiceUpdate = Database['public']['Tables']['invoices']['Update'];

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

// Mock data
const mockInvoice: InvoiceWithDetails = {
  id: 'test-invoice-id',
  invoice_number: 'INV-2025-001',
  issue_date: '2025-01-15',
  due_date: '2025-02-15',
  subtotal: 100.00,
  tax_rate: 22.00,
  tax_amount: 22.00,
  total: 122.00,
  status: 'draft',
  payment_method: null,
  payment_date: null,
  patient_id: 'patient-id-123',
  description: 'Test invoice',
  notes: 'Test notes',
  created_at: '2025-01-15T10:00:00Z',
  updated_at: '2025-01-15T10:00:00Z',
  clinic_id: 'test-clinic-id',
  deleted_at: null,
  rate_number: null,
  sal: null,
  patient: {
    id: 'patient-id-123',
    first_name: 'Mario',
    last_name: 'Rossi',
    email: '<EMAIL>',
    phone: '*********',
    date_of_birth: '1980-01-01',
    fiscal_code: '****************',
    address: 'Via Roma 1',
    city: 'Milano',
    postal_code: '20100',
    province: 'MI',
    medical_history: 'Nessuna allergia nota',
    allergies: null,
    medications: null,
    is_smoker: false,
    anamnesis: 'Anamnesi test',
    anamnesi_signed: true,
    udi: null,
    created_at: '2025-01-15T10:00:00Z',
    updated_at: '2025-01-15T10:00:00Z'
  }
};

const mockItems: InvoiceItem[] = [
  {
    id: 'item-1',
    invoice_id: 'test-invoice-id',
    treatment_id: 1,
    quantity: 1,
    unit_price: 50.00,
    subtotal: 50.00,
    iva: 22.00,
    total: 61.00,
    treatment: {
      id: 1,
      name: 'Visita di controllo',
      duration: 30,
      price: 50.00,
      category: 'Prevenzione',
      description: null,
      created_at: null,
      updated_at: null
    }
  },
  {
    id: 'item-2',
    invoice_id: 'test-invoice-id',
    treatment_id: 2,
    quantity: 1,
    unit_price: 50.00,
    subtotal: 50.00,
    iva: 22.00,
    total: 61.00,
    treatment: {
      id: 2,
      name: 'Pulizia dentale',
      duration: 45,
      price: 50.00,
      category: 'Igiene',
      description: null,
      created_at: null,
      updated_at: null
    }
  }
];

describe('InvoiceService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup completo per i mock di Supabase
    const createMockQuery = () => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      lt: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      like: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn(),
    });

    mockSupabase.from.mockReturnValue(createMockQuery() as any);
  });

  describe('getInvoices', () => {
    it('should fetch invoices with filters and pagination', async () => {
      const mockResult = {
        data: [mockInvoice],
        error: null,
        count: 1,
      };

      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockReturnValue(mockResult),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.getInvoices(
        { search: 'test' },
        { page: 1, limit: 10 }
      );

      expect(mockSupabase.from).toHaveBeenCalledWith('invoices');
      expect(result.invoices).toEqual([mockInvoice]);
      expect(result.pagination.total).toBe(1);
    });

    it('should handle error when fetching invoices', async () => {
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockReturnValue({
          data: null,
          error: { message: 'Database error' },
          count: 0,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      await expect(InvoiceService.getInvoices()).rejects.toThrow('Errore nel recupero delle fatture: Database error');
    });
  });

  describe('getInvoiceById', () => {
    it('should fetch invoice by ID', async () => {
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnValue({
          data: mockInvoice,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.getInvoiceById('test-invoice-id');

      expect(mockSupabase.from).toHaveBeenCalledWith('invoices');
      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'test-invoice-id');
      expect(result).toEqual(mockInvoice);
    });

    it('should return null when invoice not found', async () => {
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnValue({
          data: null,
          error: { code: 'PGRST116' },
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.getInvoiceById('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('createInvoiceWithItems', () => {
    it('should create invoice with items successfully', async () => {
      const invoiceData: Omit<InvoiceInsert, 'status'> = {
        invoice_number: 'INV-2025-001',
        patient_id: 'patient-id-123',
        issue_date: '2025-01-15',
        due_date: '2025-02-15',
        subtotal: 100.00,
        tax_rate: 22.00,
        tax_amount: 22.00,
        total: 122.00,
        description: 'Test invoice',
        notes: 'Test notes',
      };

      const itemsData = mockItems.map(item => ({
        treatment_id: item.treatment_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        subtotal: item.subtotal,
        iva: item.iva,
        total: item.total,
      }));

      // Mock per la creazione della fattura
      const mockInvoiceQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnValue({
          data: mockInvoice,
          error: null,
        }),
      };

      // Mock per l'inserimento degli items
      const mockItemsQuery = {
        insert: jest.fn().mockReturnValue({
          data: mockItems,
          error: null,
        }),
      };

      mockSupabase.from
        .mockReturnValueOnce(mockInvoiceQuery as any)
        .mockReturnValueOnce(mockItemsQuery as any);

      const result = await InvoiceService.createInvoiceWithItems(invoiceData, itemsData);

      expect(mockSupabase.from).toHaveBeenNthCalledWith(1, 'invoices');
      expect(mockSupabase.from).toHaveBeenNthCalledWith(2, 'invoice_items');
      expect(mockInvoiceQuery.insert).toHaveBeenCalledWith({
        ...invoiceData,
        status: 'draft',
      });
      expect(mockItemsQuery.insert).toHaveBeenCalledWith(
        itemsData.map(item => ({
          ...item,
          invoice_id: mockInvoice.id,
        }))
      );
      expect(result).toEqual(mockInvoice);
    });

    it('should rollback invoice creation if items insertion fails', async () => {
      const invoiceData: Omit<InvoiceInsert, 'status'> = {
        invoice_number: 'INV-2025-002',
        patient_id: 'patient-id-123',
        issue_date: '2025-01-15',
        due_date: '2025-02-15',
        subtotal: 100.00,
        tax_rate: 22.00,
        tax_amount: 22.00,
        total: 122.00,
        description: 'Test invoice',
      };

      const itemsData = [{ treatment_id: 1, quantity: 1, unit_price: 50, subtotal: 50, iva: 22, total: 61 }];

      // Mock successful invoice creation
      const mockInvoiceQuery = {
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnValue({
          data: mockInvoice,
          error: null,
        }),
      };

      // Mock failed items insertion
      const mockItemsQuery = {
        insert: jest.fn().mockReturnValue({
          data: null,
          error: { message: 'Items insertion failed' },
        }),
      };

      // Mock rollback deletion
      const mockDeleteQuery = {
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnValue({
          data: null,
          error: null,
        }),
      };

      mockSupabase.from
        .mockReturnValueOnce(mockInvoiceQuery as any)
        .mockReturnValueOnce(mockItemsQuery as any)
        .mockReturnValueOnce(mockDeleteQuery as any);

      await expect(InvoiceService.createInvoiceWithItems(invoiceData, itemsData))
        .rejects.toThrow('Errore nell\'inserimento degli items: Items insertion failed');

      expect(mockDeleteQuery.delete).toHaveBeenCalled();
      expect(mockDeleteQuery.eq).toHaveBeenCalledWith('id', mockInvoice.id);
    });
  });

  describe('updateInvoice', () => {
    it('should update invoice successfully', async () => {
      const updateData: InvoiceUpdate = {
        description: 'Updated description',
        total: 150.00,
      };

      const updatedInvoice = { ...mockInvoice, ...updateData };

      const mockQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnValue({
          data: updatedInvoice,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.updateInvoice('test-invoice-id', updateData);

      expect(mockSupabase.from).toHaveBeenCalledWith('invoices');
      expect(mockQuery.update).toHaveBeenCalledWith(updateData);
      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'test-invoice-id');
      expect(result).toEqual(updatedInvoice);
    });
  });

  describe('softDeleteInvoice', () => {
    it('should soft delete invoice successfully', async () => {
      const mockQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnValue({
          data: null,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      await InvoiceService.softDeleteInvoice('test-invoice-id');

      expect(mockSupabase.from).toHaveBeenCalledWith('invoices');
      expect(mockQuery.update).toHaveBeenCalledWith(
        expect.objectContaining({
          deleted_at: expect.any(String),
        })
      );
      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'test-invoice-id');
    });

    it('should handle error during soft delete', async () => {
      const mockQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnValue({
          data: null,
          error: { message: 'Update failed' },
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      await expect(InvoiceService.softDeleteInvoice('test-invoice-id'))
        .rejects.toThrow('Errore nel soft delete della fattura: Update failed');
    });
  });

  describe('restoreInvoice', () => {
    it('should restore soft-deleted invoice successfully', async () => {
      const restoredInvoice = { ...mockInvoice, deleted_at: null };

      const mockQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnValue({
          data: restoredInvoice,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.restoreInvoice('test-invoice-id');

      expect(mockSupabase.from).toHaveBeenCalledWith('invoices');
      expect(mockQuery.update).toHaveBeenCalledWith({ deleted_at: null });
      expect(mockQuery.eq).toHaveBeenCalledWith('id', 'test-invoice-id');
      expect(result).toEqual(restoredInvoice);
    });
  });

  describe('markAsPaid', () => {
    it('should mark invoice as paid successfully', async () => {
      const paymentDate = '2025-01-20';
      const paymentMethod = 'Carta di credito';
      const paidInvoice = {
        ...mockInvoice,
        status: 'paid' as const,
        payment_date: paymentDate,
        payment_method: paymentMethod,
      };

      const mockQuery = {
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnValue({
          data: paidInvoice,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.markAsPaid('test-invoice-id', paymentDate, paymentMethod);

      expect(mockSupabase.from).toHaveBeenCalledWith('invoices');
      expect(mockQuery.update).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'paid',
          payment_date: paymentDate,
          payment_method: paymentMethod,
        })
      );
      expect(result).toEqual(paidInvoice);
    });
  });

  describe('generateInvoiceNumber', () => {
    it('should generate invoice number for new year', async () => {
      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        like: jest.fn().mockReturnThis(),
        is: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnValue({
          data: [],
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.generateInvoiceNumber();
      const currentYear = new Date().getFullYear();

      expect(result).toBe(`INV-${currentYear}-001`);
    });

    it('should generate incremental invoice number', async () => {
      const currentYear = new Date().getFullYear();
      const lastInvoiceNumber = `INV-${currentYear}-005`;

      const mockQuery = {
        select: jest.fn().mockReturnThis(),
        like: jest.fn().mockReturnThis(),
        is: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnValue({
          data: [{ invoice_number: lastInvoiceNumber }],
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockQuery as any);

      const result = await InvoiceService.generateInvoiceNumber();

      expect(result).toBe(`INV-${currentYear}-006`);
    });
  });
});
