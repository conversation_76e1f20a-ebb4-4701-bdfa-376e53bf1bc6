/**
 * Verifica lo stato delle policy RLS senza service role
 * Usa un approccio indiretto per verificare se le policy funzionano
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 === VERIFICA STATO POLICY RLS ===\n');

async function checkPolicyStatus() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('1️⃣ Test con utente autenticato...');
    
    // Login con utente esistente
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123456'
    });
    
    if (signInError) {
      console.log('❌ Errore login:', signInError.message);
      return;
    }
    
    console.log('✅ Login riuscito:', signInData.user.email);
    
    // Chiama Edge Function per assicurarsi che i claims siano presenti
    await supabase.functions.invoke('issue-jwt', {
      body: { user: signInData.user }
    });
    
    console.log('\n2️⃣ Verifica JWT claims...');
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('❌ Errore recupero utente:', userError?.message);
      return;
    }
    
    console.log('📋 JWT claims:');
    console.log(`  - Clinic ID: ${user.app_metadata?.clinic_id}`);
    console.log(`  - App Role: ${user.app_metadata?.app_role}`);
    
    const clinicId = user.app_metadata?.clinic_id;
    
    if (!clinicId) {
      console.log('❌ JWT claims mancanti');
      return;
    }
    
    console.log('\n3️⃣ Test inserimento paziente con dati minimi...');
    
    const minimalPatient = {
      first_name: 'Test',
      last_name: 'Policy',
      phone: '+39 ************',
      date_of_birth: '1990-01-01',
      address: 'Via Test',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      anamnesis: 'Test',
      medical_history: 'Test',
      clinic_id: clinicId
    };
    
    const { data: newPatient, error: patientError } = await supabase
      .from('patients')
      .insert(minimalPatient)
      .select()
      .single();
    
    if (patientError) {
      console.log('❌ INSERIMENTO FALLITO:');
      console.log(`  - Codice: ${patientError.code}`);
      console.log(`  - Messaggio: ${patientError.message}`);
      
      if (patientError.code === '42501') {
        console.log('\n🔧 DIAGNOSI: Policy RLS non aggiornate correttamente');
        console.log('📋 Le policy potrebbero ancora usare il path vecchio:');
        console.log('  ❌ auth.jwt() ->> \'clinic_id\'');
        console.log('  ✅ Dovrebbe essere: auth.jwt() -> \'app_metadata\' ->> \'clinic_id\'');
        
        console.log('\n🛠️ SOLUZIONE NECESSARIA:');
        console.log('1. Vai su Supabase Dashboard > SQL Editor');
        console.log('2. Esegui queste query per correggere le policy INSERT:');
        console.log('');
        console.log('-- Fix policy INSERT per patients');
        console.log('DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;');
        console.log('CREATE POLICY "clinic_isolation_insert" ON public.patients');
        console.log('  FOR INSERT WITH CHECK (clinic_id = (auth.jwt() -> \'app_metadata\' ->> \'clinic_id\')::uuid);');
        console.log('');
        console.log('-- Verifica che la policy sia stata creata');
        console.log('SELECT policyname, cmd, with_check FROM pg_policies');
        console.log('WHERE schemaname = \'public\' AND tablename = \'patients\'');
        console.log('AND policyname = \'clinic_isolation_insert\';');
      }
    } else {
      console.log('🎉 INSERIMENTO RIUSCITO!');
      console.log(`  - ID: ${newPatient.id}`);
      console.log(`  - Nome: ${newPatient.first_name} ${newPatient.last_name}`);
      console.log(`  - Clinic ID: ${newPatient.clinic_id}`);
      console.log('\n✅ Le policy RLS funzionano correttamente!');
    }
    
    console.log('\n4️⃣ Test isolamento...');
    
    const { data: allPatients, error: patientsError } = await supabase
      .from('patients')
      .select('id, first_name, last_name, clinic_id');
    
    if (patientsError) {
      console.log('❌ Errore recupero pazienti:', patientsError.message);
    } else {
      console.log(`📊 Pazienti visibili: ${allPatients.length}`);
      
      const ownClinicPatients = allPatients.filter(p => p.clinic_id === clinicId);
      const otherClinicPatients = allPatients.filter(p => p.clinic_id !== clinicId);
      
      console.log(`  - Propria clinica (${clinicId}): ${ownClinicPatients.length}`);
      console.log(`  - Altre cliniche: ${otherClinicPatients.length}`);
      
      if (otherClinicPatients.length === 0) {
        console.log('✅ Isolamento funziona correttamente');
      } else {
        console.log('⚠️ Problema isolamento - policy SELECT potrebbero essere sbagliate');
      }
    }
    
    console.log('\n5️⃣ Test senza autenticazione...');
    
    await supabase.auth.signOut();
    
    const { data: unauthPatients, error: unauthError } = await supabase
      .from('patients')
      .select('id')
      .limit(1);
    
    if (unauthError) {
      if (unauthError.code === '42501') {
        console.log('✅ RLS blocca correttamente utenti non autenticati');
      } else {
        console.log('⚠️ Errore inaspettato:', unauthError.message);
      }
    } else {
      console.log('❌ PROBLEMA: Utenti non autenticati possono accedere ai dati!');
      console.log(`  - Record visibili: ${unauthPatients.length}`);
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

checkPolicyStatus().then(() => {
  console.log('\n🏁 Verifica completata');
  console.log('\n📋 RIEPILOGO:');
  console.log('- Se inserimento fallisce con 42501: Policy non aggiornate');
  console.log('- Se inserimento riesce: Policy funzionano correttamente');
  console.log('- Se isolamento fallisce: Policy SELECT sbagliate');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
