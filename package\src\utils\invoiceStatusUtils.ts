/**
 * MOKO SOSTANZA Dental CRM - Invoice Status Utilities
 * 
 * Utility functions per la gestione degli stati delle fatture nell'UI
 */

export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';

/**
 * Configurazione dei colori e stili per i badge degli stati fattura
 */
export const InvoiceStatusConfig = {
  draft: {
    label: 'Bozza',
    color: 'gray',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
    icon: '📝'
  },
  sent: {
    label: 'Inviata', 
    color: 'blue',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    icon: '📤'
  },
  paid: {
    label: 'Pagata',
    color: 'green', 
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    icon: '✅'
  },
  overdue: {
    label: 'Scaduta',
    color: 'red',
    bgColor: 'bg-red-100', 
    textColor: 'text-red-800',
    icon: '⏰'
  },
  cancelled: {
    label: 'Cancellata',
    color: 'yellow',
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-800', 
    icon: '❌'
  }
} as const;

/**
 * Ottiene la configurazione del badge per uno stato specifico
 */
export function getInvoiceStatusConfig(status: string | null): typeof InvoiceStatusConfig[keyof typeof InvoiceStatusConfig] {
  const normalizedStatus = (status || 'draft') as InvoiceStatus;
  return InvoiceStatusConfig[normalizedStatus] || InvoiceStatusConfig.draft;
}

/**
 * Formatta lo stato della fattura per la visualizzazione
 */
export function formatInvoiceStatus(status: string | null): string {
  const config = getInvoiceStatusConfig(status);
  return `${config.icon} ${config.label}`;
}

/**
 * Ottiene le classi CSS Tailwind per il badge dello stato
 */
export function getInvoiceStatusBadgeClasses(status: string | null): string {
  const config = getInvoiceStatusConfig(status);
  return `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`;
}
