import { Button, Label, TextInput, Alert } from "flowbite-react";
import { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams, useLocation } from "react-router-dom";
import { supabase } from "../../../lib/supabase";

/**
 * Componente di login con email e password
 * Sostituisce il sistema Magic Link con autenticazione classica
 */
const AuthLogin = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    // Controlla se c'è un messaggio di successo dai parametri URL
    const message = searchParams.get('message');
    if (message === 'password-updated') {
      setSuccessMessage('Password aggiornata con successo! Ora puoi accedere con la nuova password.');
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('🔐 [Login] Starting login process...');

    if (!formData.email.trim() || !formData.password.trim()) {
      setError('Email e password sono obbligatori');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Login con Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      });

      if (authError) {
        throw authError;
      }

      console.log('✅ [Login] User logged in successfully:', authData.user?.email);

      // Verifica che l'utente abbia un record nella tabella users
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('clinic_id, role')
        .eq('id', authData.user.id)
        .single();

      if (userError || !userData) {
        console.warn('⚠️ [Login] User record not found in public.users table');
        // Procedi comunque - potrebbe essere un utente legacy
      } else {
        console.log('✅ [Login] User data found:', userData);
      }

      // Redirect al dashboard (o alla pagina precedente se specificata)
      console.log('🔄 [Login] Redirecting to dashboard...');
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });

    } catch (error: any) {
      console.error('❌ [Login] Login failed:', error);

      if (error.message?.includes('Invalid login credentials')) {
        setError('Email o password non corretti');
      } else if (error.message?.includes('Email not confirmed')) {
        setError('Email non confermata. Controlla la tua casella di posta.');
      } else if (error.message?.includes('Too many requests')) {
        setError('Troppi tentativi di login. Riprova tra qualche minuto.');
      } else {
        setError(error.message || 'Errore durante il login. Riprova.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {successMessage && (
        <Alert color="success" className="mb-4">
          <span className="font-medium">Successo!</span> {successMessage}
        </Alert>
      )}

      {error && (
        <Alert color="failure" className="mb-4">
          <span className="font-medium">Errore!</span> {error}
        </Alert>
      )}

      <div>
        <Label htmlFor="email" value="Email" />
        <TextInput
          id="email"
          name="email"
          type="email"
          required
          value={formData.email}
          onChange={handleInputChange}
          placeholder="<EMAIL>"
          disabled={isLoading}
          className="form-control form-rounded-xl"
        />
      </div>

      <div>
        <Label htmlFor="password" value="Password" />
        <TextInput
          id="password"
          name="password"
          type="password"
          required
          value={formData.password}
          onChange={handleInputChange}
          placeholder="Inserisci la tua password"
          disabled={isLoading}
          className="form-control form-rounded-xl"
        />
      </div>

      <Button
        type="submit"
        color="primary"
        className="w-full"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Accesso in corso...
          </>
        ) : (
          'Accedi'
        )}
      </Button>

      {/* Link recupero password */}
      <div className="text-center">
        <Link
          to="/auth/forgot-password"
          className="text-sm text-primary hover:underline"
        >
          Password dimenticata?
        </Link>
      </div>

      {/* Link registrazione */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-300">
          Non hai un account?{" "}
          <Link
            to="/auth/register"
            className="text-primary hover:underline"
          >
            Registrati qui
          </Link>
        </p>
      </div>

      {/* Note informative */}
      <div className="border-t pt-4">
        <p className="text-xs text-gray-500 text-center">
          🔒 Accesso sicuro con email e password.
          <br />
          Solo per titolari di clinica registrati.
        </p>
      </div>
    </form>
  );
};

export default AuthLogin;
