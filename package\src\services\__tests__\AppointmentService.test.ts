import { AppointmentService } from '../AppointmentService';
import { supabase } from '../../lib/supabase';

// Mock Supabase
jest.mock('../../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          is: jest.fn(() => ({
            gte: jest.fn(() => ({
              lte: jest.fn(() => ({
                order: jest.fn(() => ({
                  data: [],
                  error: null
                }))
              }))
            }))
          }))
        })),
        single: jest.fn(() => ({
          data: null,
          error: null
        }))
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(() => ({
            data: null,
            error: null
          }))
        }))
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(() => ({
              data: null,
              error: null
            }))
          }))
        }))
      })),
      delete: jest.fn(() => ({
        eq: jest.fn(() => ({
          data: null,
          error: null
        }))
      }))
    }))
  }
}));

describe('AppointmentService', () => {
  const mockAppointment = {
    id: 1,
    patient_id: 'patient-1',
    doctor_id: 1, // Changed from string to number
    treatment_id: 1,
    date: '2024-01-15',
    start_time: '09:00',
    end_time: '10:00',
    status: 'confermato',
    notes: 'Test appointment',
    clinic_id: '00000000-0000-0000-0000-000000000000',
    deleted_at: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createAppointment', () => {
    it('should create appointment with clinic_id', async () => {
      const mockSupabaseChain = {
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: mockAppointment,
            error: null
          })
        })
      };

      (supabase.from as jest.Mock).mockReturnValue({
        insert: jest.fn().mockReturnValue(mockSupabaseChain)
      });

      const result = await AppointmentService.createAppointment(mockAppointment);

      expect(supabase.from).toHaveBeenCalledWith('appointments');
      expect(result).toEqual(mockAppointment);
    });

    it('should handle creation errors', async () => {
      const mockError = { message: 'Database error' };
      
      (supabase.from as jest.Mock).mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: mockError
            })
          })
        })
      });

      await expect(AppointmentService.createAppointment(mockAppointment))
        .rejects.toThrow('Database error');
    });
  });

  describe('updateAppointment', () => {
    it('should update appointment successfully', async () => {
      const updates = { status: 'completato', notes: 'Updated notes' };
      const updatedAppointment = { ...mockAppointment, ...updates };

      const mockSupabaseChain = {
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: updatedAppointment,
              error: null
            })
          })
        })
      };

      (supabase.from as jest.Mock).mockReturnValue({
        update: jest.fn().mockReturnValue(mockSupabaseChain)
      });

      const result = await AppointmentService.updateAppointment(1, updates);

      expect(supabase.from).toHaveBeenCalledWith('appointments');
      expect(result).toEqual(updatedAppointment);
    });
  });

  describe('deleteAppointment (soft delete)', () => {
    it('should perform soft delete by setting deleted_at', async () => {
      const softDeletedAppointment = {
        ...mockAppointment,
        deleted_at: '2024-01-15T10:00:00.000Z'
      };

      const mockSupabaseChain = {
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: softDeletedAppointment,
              error: null
            })
          })
        })
      };

      (supabase.from as jest.Mock).mockReturnValue({
        update: jest.fn().mockReturnValue(mockSupabaseChain)
      });

      const result = await AppointmentService.deleteAppointment(1);

      expect(supabase.from).toHaveBeenCalledWith('appointments');
      expect(result).toBeUndefined(); // deleteAppointment returns void
    });
  });

  describe('restoreAppointment', () => {
    it('should restore soft-deleted appointment', async () => {
      const restoredAppointment = {
        ...mockAppointment,
        deleted_at: null
      };

      const mockSupabaseChain = {
        eq: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: restoredAppointment,
              error: null
            })
          })
        })
      };

      (supabase.from as jest.Mock).mockReturnValue({
        update: jest.fn().mockReturnValue(mockSupabaseChain)
      });

      const result = await AppointmentService.restoreAppointment(1);

      expect(supabase.from).toHaveBeenCalledWith('appointments');
      expect(result).toBeUndefined(); // restoreAppointment returns void
    });
  });

  describe('getAppointmentsByDateRange', () => {
    it('should filter out deleted appointments', async () => {
      const appointments = [
        mockAppointment,
        { ...mockAppointment, id: 2, deleted_at: '2024-01-15T10:00:00.000Z' }
      ];

      const mockSupabaseChain = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            is: jest.fn().mockReturnValue({
              gte: jest.fn().mockReturnValue({
                lte: jest.fn().mockReturnValue({
                  order: jest.fn().mockResolvedValue({
                    data: [mockAppointment], // Only non-deleted
                    error: null
                  })
                })
              })
            })
          })
        })
      };

      (supabase.from as jest.Mock).mockReturnValue(mockSupabaseChain);

      const result = await AppointmentService.getAppointmentsByDateRange('2024-01-01', '2024-01-31');

      expect(result).toHaveLength(1);
      expect(result[0].deleted_at).toBeFalsy();
    });

    it('should apply clinic_id filter when provided', async () => {
      const clinicId = 'clinic-1';
      
      const mockSupabaseChain = {
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              is: jest.fn().mockReturnValue({
                gte: jest.fn().mockReturnValue({
                  lte: jest.fn().mockReturnValue({
                    order: jest.fn().mockResolvedValue({
                      data: [mockAppointment],
                      error: null
                    })
                  })
                })
              })
            })
          })
        })
      };

      (supabase.from as jest.Mock).mockReturnValue(mockSupabaseChain);

      await AppointmentService.getAppointmentsByDateRange('2024-01-01', '2024-01-31');

      // Verify the method was called correctly
      expect(supabase.from).toHaveBeenCalledWith('appointments');
    });
  });

  describe('utility functions', () => {
    const appointmentWithDetails = {
      ...mockAppointment,
      patient: { first_name: 'Mario', last_name: 'Rossi' },
      treatment: { name: 'Pulizia' },
      doctor: { calendar_color: '#FF5733' }
    };

    it('should format appointment title correctly', () => {
      const { getAppointmentTitle } = require('../AppointmentService');
      
      const title = getAppointmentTitle(appointmentWithDetails);
      expect(title).toBe('Mario Rossi - Pulizia');
    });

    it('should get appointment color from doctor', () => {
      const { getAppointmentColor } = require('../AppointmentService');
      
      const color = getAppointmentColor(appointmentWithDetails);
      expect(color).toBe('#FF5733');
    });

    it('should fall back to status-based color', () => {
      const { getAppointmentColor } = require('../AppointmentService');
      
      const appointmentNoDoctor = {
        ...appointmentWithDetails,
        doctor: { calendar_color: null }
      };
      
      const color = getAppointmentColor(appointmentNoDoctor);
      expect(color).toBe('#4CAF50'); // confermato status color
    });
  });
});
