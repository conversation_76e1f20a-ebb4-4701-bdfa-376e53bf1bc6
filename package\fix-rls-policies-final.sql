-- ===================================================================
-- FIX DEFINITIVO POLICY RLS - GESTIONALE DENTISTI
-- ===================================================================
-- 
-- PROBLEMA IDENTIFICATO:
-- Le policy RLS per l'inserimento pazienti usano ancora il path JWT sbagliato
-- 
-- CAUSA:
-- Le policy potrebbero usare: auth.jwt() ->> 'clinic_id'
-- Invece del path corretto: auth.jwt() -> 'app_metadata' ->> 'clinic_id'
--
-- SOLUZIONE:
-- Ricreare tutte le policy con il path corretto
-- ===================================================================

-- 1. VERIFICA POLICY ATTUALI
SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients' 
  AND policyname LIKE 'clinic_isolation_%'
ORDER BY cmd;

-- ===================================================================
-- 2. RIMOZIONE POLICY ESISTENTI (SICUREZZA)
-- ===================================================================

-- Rimuovi tutte le policy esistenti per patients
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

-- ===================================================================
-- 3. CREAZIONE POLICY CORRETTE PER PATIENTS
-- ===================================================================

-- Policy SELECT: Utenti vedono solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy INSERT: Utenti possono inserire solo pazienti nella propria clinica
CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy UPDATE: Utenti possono aggiornare solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy DELETE: Utenti possono eliminare solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- ===================================================================
-- 4. POLICY PER ALTRE TABELLE (OPZIONALE)
-- ===================================================================

-- DOCTORS
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.doctors;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.doctors;

CREATE POLICY "clinic_isolation_select" ON public.doctors
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.doctors
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.doctors
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.doctors
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- APPOINTMENTS
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.appointments;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.appointments;

CREATE POLICY "clinic_isolation_select" ON public.appointments
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.appointments
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.appointments
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.appointments
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- TREATMENTS
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.treatments;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.treatments;

CREATE POLICY "clinic_isolation_select" ON public.treatments
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_insert" ON public.treatments
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_update" ON public.treatments
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "clinic_isolation_delete" ON public.treatments
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- ===================================================================
-- 5. VERIFICA FINALE
-- ===================================================================

-- Verifica che tutte le policy siano state create correttamente
SELECT 
  tablename,
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('patients', 'doctors', 'appointments', 'treatments')
  AND policyname LIKE 'clinic_isolation_%'
ORDER BY tablename, cmd;

-- Verifica che RLS sia abilitato
SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('patients', 'doctors', 'appointments', 'treatments');

-- ===================================================================
-- 6. TEST RAPIDO (OPZIONALE)
-- ===================================================================

-- Test per verificare che le policy funzionino
-- NOTA: Questo test funziona solo se hai un utente autenticato con JWT claims

-- Mostra i JWT claims attuali (se disponibili)
SELECT 
  'JWT Test' as test,
  auth.jwt() as full_jwt,
  auth.jwt() -> 'app_metadata' as app_metadata,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_from_jwt,
  auth.uid() as user_id;

-- ===================================================================
-- ISTRUZIONI PER L'ESECUZIONE:
-- ===================================================================
-- 
-- 1. Vai su Supabase Dashboard > SQL Editor
-- 2. Copia e incolla questo intero file
-- 3. Esegui tutto il contenuto
-- 4. Verifica che non ci siano errori
-- 5. Testa la creazione di un nuovo paziente dall'applicazione
-- 
-- IMPORTANTE:
-- - Questo script rimuove e ricrea tutte le policy RLS
-- - Assicurati di avere un backup del database prima di eseguire
-- - Testa su un ambiente di sviluppo prima della produzione
-- 
-- ===================================================================
