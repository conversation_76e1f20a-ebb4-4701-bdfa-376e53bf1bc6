-- Add patient_id and channel columns to reminders table
-- This migration adds the ability to link reminders to specific patients
-- and categorize them by communication channel

-- Add patient_id column to link reminders to patients
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS patient_id UUID REFERENCES public.patients(id) ON DELETE CASCADE;

-- Add channel column to categorize reminder types
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS channel VARCHAR(50) DEFAULT 'general' 
CHECK (channel IN ('general', 'email', 'sms', 'phone', 'appointment', 'follow-up', 'treatment'));

-- Add index for better query performance when filtering by patient
CREATE INDEX IF NOT EXISTS idx_reminders_patient_id ON public.reminders(patient_id);

-- Add index for better query performance when filtering by channel
CREATE INDEX IF NOT EXISTS idx_reminders_channel ON public.reminders(channel);

-- Add composite index for patient + date queries
CREATE INDEX IF NOT EXISTS idx_reminders_patient_date ON public.reminders(patient_id, date);
