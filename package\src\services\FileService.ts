/**
 * <PERSON><PERSON><PERSON> SOSTANZA Dental CRM - File Service
 *
 * Servizio centralizzato per gestione file pazienti
 * Supporta upload O<PERSON>, T<PERSON>, Cone <PERSON>am e altri documenti
 */

import { supabase } from '../lib/supabase';
import { createClient } from '@supabase/supabase-js';
import { withClinicId, getClinicId } from './ServiceUtils';

// Create a service role client for admin operations
const supabaseAdmin = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU',
);
import { Database } from '../types/database';

type PatientFile = Database['public']['Tables']['patient_files']['Row'];
type CreatePatientFileData = Database['public']['Tables']['patient_files']['Insert'];
type UpdatePatientFileData = Database['public']['Tables']['patient_files']['Update'];

// Export types
export { type PatientFile, type CreatePatientFileData, type UpdatePatientFileData };

export interface FileUploadResult {
  success: boolean;
  file?: PatientFile;
  error?: string;
  path?: string;
}

export interface FileCategory {
  key: string;
  label: string;
  extensions: string[];
  maxSize: number; // in bytes
}

// File categories configuration
export const FILE_CATEGORIES: Record<string, FileCategory> = {
  otp: {
    key: 'otp',
    label: 'OTP (Ortopantomografia)',
    extensions: ['.jpg', '.jpeg', '.png', '.pdf', '.dcm'],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
  tac: {
    key: 'tac',
    label: 'TAC',
    extensions: ['.jpg', '.jpeg', '.png', '.pdf', '.dcm'],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
  cone_beam: {
    key: 'cone_beam',
    label: 'Cone Beam',
    extensions: ['.jpg', '.jpeg', '.png', '.pdf', '.dcm'],
    maxSize: 10 * 1024 * 1024, // 10MB
  },
  document: {
    key: 'document',
    label: 'Documento',
    extensions: ['.pdf', '.jpg', '.jpeg', '.png'],
    maxSize: 5 * 1024 * 1024, // 5MB
  },
  image: {
    key: 'image',
    label: 'Immagine',
    extensions: ['.jpg', '.jpeg', '.png'],
    maxSize: 5 * 1024 * 1024, // 5MB
  },
};

/**
 * Servizio per la gestione completa dei file pazienti
 */
export class FileService {
  private static readonly BUCKET_NAME = 'patient-files';

  /**
   * Valida un file prima dell'upload
   */
  static validateFile(file: File, category: string): { valid: boolean; error?: string } {
    const categoryConfig = FILE_CATEGORIES[category];
    if (!categoryConfig) {
      return { valid: false, error: 'Categoria file non valida' };
    }

    // Check file size
    if (file.size > categoryConfig.maxSize) {
      const maxSizeMB = Math.round(categoryConfig.maxSize / (1024 * 1024));
      return { valid: false, error: `File troppo grande. Massimo ${maxSizeMB}MB` };
    }

    // Check file extension
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!categoryConfig.extensions.includes(fileExtension)) {
      return {
        valid: false,
        error: `Tipo file non supportato. Formati accettati: ${categoryConfig.extensions.join(
          ', ',
        )}`,
      };
    }

    return { valid: true };
  }

  /**
   * Genera un percorso univoco per il file con clinic isolation
   */
  private static async generateFilePath(patientId: string, category: string, filename: string): Promise<string> {
    try {
      // Get current user's clinic_id from JWT claims
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) {
        throw new Error('User not authenticated');
      }

      // Extract clinic_id from user metadata or JWT claims
      let clinicId = user.app_metadata?.clinic_id || user.user_metadata?.clinic_id;
      
      // Fallback: get from Demo Clinic for development
      if (!clinicId) {
        console.warn('⚠️ [FileService] No clinic_id in user claims, using Demo Clinic fallback');
        const { data: demoClinic } = await supabase
          .from('clinics')
          .select('id')
          .eq('name', 'Demo Clinic')
          .single();
        
        if (demoClinic) {
          clinicId = demoClinic.id;
        } else {
          throw new Error('No clinic_id available and Demo Clinic not found');
        }
      }

      const timestamp = new Date().getTime();
      const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
      return `${clinicId}/${patientId}/${category}/${timestamp}_${sanitizedFilename}`;
    } catch (error) {
      console.error('❌ [FileService] Error generating file path:', error);
      throw error;
    }
  }

  /**
   * Controlla se un file esiste già nel storage
   */
  static async fileExists(path: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.storage.from(this.BUCKET_NAME).download(path);

      // Se il file esiste, download non darà errore
      return !error;
    } catch (error) {
      console.error('Error checking file existence:', error);
      return false;
    }
  }

  /**
   * Upload di un file nel bucket Supabase
   */
  static async uploadFile(
    file: File,
    patientId: string,
    category: string = 'document',
  ): Promise<FileUploadResult> {
    console.log('🚀 Starting file upload:', {
      fileName: file.name,
      patientId,
      category,
      fileSize: file.size,
    });

    try {
      // Validate file
      const validation = this.validateFile(file, category);
      if (!validation.valid) {
        console.error('❌ File validation failed:', validation.error);
        return { success: false, error: validation.error };
      }
      console.log('✅ File validation passed');

      // Generate unique path with clinic isolation
      const filePath = await this.generateFilePath(patientId, category, file.name);
      console.log('📁 Generated file path:', filePath);

      // Skip file existence check for now to debug
      console.log('⏭️ Skipping file existence check for debugging');

      // Upload to Supabase Storage
      console.log('☁️ Uploading to Supabase Storage...');
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true, // Allow overwrite for debugging
        });

      if (uploadError) {
        console.error('❌ Upload error:', uploadError);
        return { success: false, error: `Errore upload: ${uploadError.message}` };
      }
      console.log('✅ File uploaded to storage:', uploadData);

      // Save file metadata to database
      const fileData = {
        patient_id: patientId,
        filename: file.name,
        path: filePath,
        mime_type: file.type || 'application/octet-stream',
        size: file.size,
        category: category,
      };
      console.log('💾 Saving metadata to database:', fileData);

      // Inject clinic_id automatically
      const fileDataWithClinic = await withClinicId(fileData);

      const { data: dbData, error: dbError } = await supabaseAdmin
        .from('patient_files')
        .insert([fileDataWithClinic])
        .select()
        .single();

      if (dbError) {
        console.error('❌ Database error:', dbError);
        // Cleanup uploaded file if database insert fails
        await supabase.storage.from(this.BUCKET_NAME).remove([filePath]);
        return { success: false, error: `Errore database: ${dbError.message}` };
      }
      console.log('✅ Metadata saved to database:', dbData);

      return {
        success: true,
        file: dbData,
        path: filePath,
      };
    } catch (error) {
      console.error('❌ FileService upload error:', error);
      return {
        success: false,
        error: `Errore imprevisto: ${
          error instanceof Error ? error.message : 'Errore sconosciuto'
        }`,
      };
    }
  }

  /**
   * Recupera tutti i file di un paziente della clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getPatientFiles(patientId: string, category?: string): Promise<PatientFile[]> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();

      let query = supabase
        .from('patient_files')
        .select('*')
        .eq('patient_id', patientId)
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching patient files:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('FileService getPatientFiles error:', error);
      return [];
    }
  }

  /**
   * Genera URL firmato per download sicuro
   */
  static async getFileUrl(filePath: string, expiresIn: number = 3600): Promise<string | null> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn);

      if (error) {
        console.error('Error creating signed URL:', error);
        return null;
      }

      return data.signedUrl;
    } catch (error) {
      console.error('FileService getFileUrl error:', error);
      return null;
    }
  }

  /**
   * Soft delete di un file
   */
  static async deleteFile(fileId: string): Promise<boolean> {
    try {
      console.log('🗑️ Attempting soft delete for file ID:', fileId);
      
      const { data, error } = await supabase
        .from('patient_files')
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', fileId)
        .select(); // Aggiungiamo select per vedere il risultato

      if (error) {
        console.error('❌ Error soft deleting file:', error);
        return false;
      }

      console.log('✅ Soft delete successful, updated records:', data?.length || 0);
      return true;
    } catch (error) {
      console.error('❌ FileService deleteFile error:', error);
      return false;
    }
  }

  /**
   * Hard delete di un file (rimuove anche dal storage)
   */
  static async permanentDeleteFile(fileId: string): Promise<boolean> {
    try {
      // Get file info first
      const { data: fileData, error: fetchError } = await supabase
        .from('patient_files')
        .select('path')
        .eq('id', fileId)
        .single();

      if (fetchError || !fileData) {
        console.error('Error fetching file for deletion:', fetchError);
        return false;
      }

      // Remove from storage
      const { error: storageError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([fileData.path]);

      if (storageError) {
        console.error('Error removing file from storage:', storageError);
        // Continue with database deletion even if storage fails
      }

      // Remove from database
      const { error: dbError } = await supabase.from('patient_files').delete().eq('id', fileId);

      if (dbError) {
        console.error('Error deleting file from database:', dbError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('FileService permanentDeleteFile error:', error);
      return false;
    }
  }

  /**
   * Recupera statistiche file per un paziente della clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getPatientFileStats(patientId: string) {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();

      const { data, error } = await supabase
        .from('patient_files')
        .select('category, size')
        .eq('patient_id', patientId)
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('deleted_at', null);

      if (error) {
        console.error('Error fetching file stats:', error);
        return { totalFiles: 0, totalSize: 0, byCategory: {} };
      }

      const stats = {
        totalFiles: data.length,
        totalSize: data.reduce((sum, file) => sum + file.size, 0),
        byCategory: data.reduce((acc, file) => {
          const category = file.category || 'unknown';
          acc[category] = (acc[category] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
      };

      return stats;
    } catch (error) {
      console.error('FileService getPatientFileStats error:', error);
      return { totalFiles: 0, totalSize: 0, byCategory: {} };
    }
  }
}

export default FileService;
