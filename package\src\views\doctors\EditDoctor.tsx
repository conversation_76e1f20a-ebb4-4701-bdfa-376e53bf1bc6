/**
 * <PERSON><PERSON><PERSON> SOSTANZA Dental CRM - Edit Doctor
 * Simplified edit form for existing doctors
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link, useLocation } from 'react-router-dom';
import { Button, Label, TextInput, Select, Textarea, Card, Spinner } from 'flowbite-react';
import { Icon } from '@iconify/react';
import { useDoctorStore } from '../../store/useDoctorStore';

const EditDoctor = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const { currentDoctor, fetchDoctorById, updateDoctor } = useDoctorStore();
  
  const isClinic = location.pathname.startsWith('/clinic');
  const basePath = isClinic ? '/clinic/doctors' : '/doctors';
  
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: 'DOTT.',
    first_name: '',
    last_name: '',
    fiscal_code: '',
    birth_date: '',
    sex: '',
    citizenship: 'ITALIANA',
    residence_street: '',
    residence_cap: '',
    pec: '',
    email: '',
    mobile: '',
    phone: '',
    order_province: '',
    order_number: '',
    order_date: '',
    albo_code: 'O',
    practice_status: true,
    calendar_color: '#1E88E5',
    qualifications: '',
    vat_id: '',
    iban: '',
    avatar_url: '',
    notes: ''
  });

  useEffect(() => {
    const loadDoctor = async () => {
      if (id) {
        try {
          await fetchDoctorById(parseInt(id));
          if (currentDoctor) {
            setFormData({
              title: currentDoctor.title || 'DOTT.',
              first_name: currentDoctor.first_name || '',
              last_name: currentDoctor.last_name || '',
              fiscal_code: currentDoctor.fiscal_code || '',
              birth_date: currentDoctor.birth_date || '',
              sex: currentDoctor.sex || '',
              citizenship: currentDoctor.citizenship || 'ITALIANA',
              residence_street: currentDoctor.residence_street || '',
              residence_cap: currentDoctor.residence_cap || '',
              pec: currentDoctor.pec || '',
              email: currentDoctor.email || '',
              mobile: currentDoctor.mobile || '',
              phone: currentDoctor.phone || '',
              order_province: currentDoctor.order_province || '',
              order_number: currentDoctor.order_number || '',
              order_date: currentDoctor.order_date || '',
              albo_code: currentDoctor.albo_code || 'O',
              practice_status: currentDoctor.practice_status !== false,
              calendar_color: currentDoctor.calendar_color || '#1E88E5',
              qualifications: currentDoctor.qualifications || '',
              vat_id: currentDoctor.vat_id || '',
              iban: currentDoctor.iban || '',
              avatar_url: currentDoctor.avatar_url || '',
              notes: currentDoctor.notes || ''
            });
          } else {
            console.error('Dottore non trovato');
            navigate(basePath);
          }
        } catch (error) {
          console.error('Errore nel caricamento del dottore:', error);
          navigate(basePath);
        } finally {
          setLoading(false);
        }
      }
    };

    loadDoctor();
  }, [id, fetchDoctorById, currentDoctor, navigate, basePath]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (id) {
        await updateDoctor(parseInt(id), formData);
        console.log('✅ Dottore aggiornato con successo!');
        navigate(basePath);
      }
    } catch (error) {
      console.error('❌ Errore nell\'aggiornamento del dottore:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" />
      </div>
    );
  }

  return (
    <div className="rounded-xl dark:shadow-dark-md shadow-md bg-white dark:bg-darkgray p-6 relative w-full break-words">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <h5 className="card-title">Modifica Medico/Odontoiatra</h5>
          <Link to={basePath} className="text-gray-500 hover:text-primary">
            <Icon icon="solar:arrow-left-linear" height={20} />
            <span className="sr-only">Torna all'elenco dottori</span>
          </Link>
        </div>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {/* Sezione Anagrafe */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Dati Anagrafici</h6>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="title" value="Titolo *" />
              <Select
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
              >
                <option value="DOTT.">DOTT.</option>
                <option value="DOTT.SSA">DOTT.SSA</option>
                <option value="PROF.">PROF.</option>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="first_name" value="Nome *" />
              <TextInput
                id="first_name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="last_name" value="Cognome *" />
              <TextInput
                id="last_name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="fiscal_code" value="Codice Fiscale *" />
              <TextInput
                id="fiscal_code"
                name="fiscal_code"
                value={formData.fiscal_code}
                onChange={handleChange}
                required
                maxLength={16}
                pattern="^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$"
              />
            </div>
            
            <div>
              <Label htmlFor="birth_date" value="Data di Nascita *" />
              <TextInput
                id="birth_date"
                name="birth_date"
                type="date"
                value={formData.birth_date}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="sex" value="Sesso *" />
              <Select
                id="sex"
                name="sex"
                value={formData.sex}
                onChange={handleChange}
                required
              >
                <option value="">Seleziona</option>
                <option value="M">Maschio</option>
                <option value="F">Femmina</option>
              </Select>
            </div>
          </div>
        </Card>

        {/* Sezione Contatti */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Contatti</h6>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pec" value="PEC (obbligatoria) *" />
              <TextInput
                id="pec"
                name="pec"
                type="email"
                value={formData.pec}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="email" value="Email" />
              <TextInput
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            
            <div>
              <Label htmlFor="mobile" value="Cellulare" />
              <TextInput
                id="mobile"
                name="mobile"
                value={formData.mobile}
                onChange={handleChange}
              />
            </div>
            
            <div>
              <Label htmlFor="phone" value="Telefono" />
              <TextInput
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
              />
            </div>
          </div>
        </Card>

        {/* Sezione Ordine Professionale */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Iscrizione Ordine</h6>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="order_province" value="Provincia Ordine *" />
              <TextInput
                id="order_province"
                name="order_province"
                value={formData.order_province}
                onChange={handleChange}
                required
                maxLength={2}
              />
            </div>
            
            <div>
              <Label htmlFor="order_number" value="Numero Iscrizione *" />
              <TextInput
                id="order_number"
                name="order_number"
                value={formData.order_number}
                onChange={handleChange}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="order_date" value="Data Iscrizione *" />
              <TextInput
                id="order_date"
                name="order_date"
                type="date"
                value={formData.order_date}
                onChange={handleChange}
                required
              />
            </div>
          </div>
        </Card>

        {/* Sezione CRM */}
        <Card>
          <h6 className="text-lg font-semibold mb-4">Dati Operativi</h6>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="calendar_color" value="Colore Calendario" />
              <div className="flex gap-2">
                <TextInput
                  id="calendar_color"
                  name="calendar_color"
                  value={formData.calendar_color}
                  onChange={handleChange}
                />
                <input
                  type="color"
                  value={formData.calendar_color}
                  onChange={handleChange}
                  name="calendar_color"
                  className="w-12 h-10 border rounded"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="qualifications" value="Qualifiche e Titoli" />
              <Textarea
                id="qualifications"
                name="qualifications"
                value={formData.qualifications}
                onChange={handleChange}
                placeholder="Es: Specialista in Ortodonzia, Master in Implantologia, Perfezionamento in Endodonzia..."
                rows={3}
              />
            </div>
            
            <div className="md:col-span-2">
              <Label htmlFor="notes" value="Note" />
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
              />
            </div>
          </div>
        </Card>

        {/* Pulsanti */}
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            color="gray"
            onClick={() => navigate(basePath)}
            disabled={isSubmitting}
          >
            <Icon icon="solar:arrow-left-linear" className="mr-2" />
            Annulla
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-primary hover:bg-primary/90"
          >
            {isSubmitting ? (
              <>
                <Icon icon="solar:refresh-linear" className="mr-2 animate-spin" />
                Salvataggio...
              </>
            ) : (
              <>
                <Icon icon="solar:diskette-linear" className="mr-2" />
                Aggiorna Medico
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default EditDoctor;
