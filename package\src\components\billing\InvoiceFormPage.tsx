/**
 * MOKO SOSTANZA Dental CRM - Invoice Form Page
 * Form per creazione fatture come pagina completa (riusa logica di InvoiceModal)
 */

import { Button, TextInput, Label, Select, Textarea, Table, Badge, Spinner } from "flowbite-react";
import { Icon } from "@iconify/react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { PatientService, type Patient } from "../../services/PatientService";
import { TreatmentService, type Treatment } from "../../services/TreatmentService";
import InvoiceService, { type InvoiceItem } from "../../services/InvoiceService";
import type { Database } from "../../types/database";

type InvoiceInsert = Database['public']['Tables']['invoices']['Insert'];

interface InvoiceFormData {
  patient_id: string;
  issue_date: string;
  due_date: string;
  description: string;
  notes: string;
  sal?: number;
  rate_number?: number;
}

const InvoiceFormPage = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<InvoiceFormData>({
    patient_id: '',
    issue_date: new Date().toISOString().split('T')[0],
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    description: '',
    notes: '',
  });
  const [items, setItems] = useState<Omit<InvoiceItem, 'id' | 'invoice_id'>[]>([]);
  const [selectedTreatment, setSelectedTreatment] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [customPrice, setCustomPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [treatments, setTreatments] = useState<Treatment[]>([]);

  // Stati per l'autocompletamento pazienti (come in InvoiceModal)
  const [patientSearch, setPatientSearch] = useState('');
  const [showPatientSuggestions, setShowPatientSuggestions] = useState(false);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);

  // Carica dati iniziali
  useEffect(() => {
    const loadData = async () => {
      try {
        const [patientsResult, treatmentsResult] = await Promise.all([
          PatientService.getPatients({}, { limit: 100 }),
          TreatmentService.getTreatments()
        ]);
        setPatients(patientsResult.patients);
        setTreatments(treatmentsResult.treatments);
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };
    
    loadData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Gestisce la ricerca dei pazienti (come in InvoiceModal)
  const handlePatientSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPatientSearch(value);
    
    if (value.length > 0) {
      const filtered = patients.filter(patient => 
        `${patient.first_name} ${patient.last_name}`.toLowerCase().includes(value.toLowerCase()) ||
        patient.fiscal_code?.toLowerCase().includes(value.toLowerCase()) ||
        patient.phone?.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredPatients(filtered);
      setShowPatientSuggestions(true);
    } else {
      setShowPatientSuggestions(false);
      setFormData(prev => ({ ...prev, patient_id: '' }));
    }
  };

  // Seleziona un paziente dall'autocompletamento
  const handleSelectPatient = (patient: Patient) => {
    setPatientSearch(`${patient.first_name} ${patient.last_name}`);
    setFormData(prev => ({ ...prev, patient_id: patient.id }));
    setShowPatientSuggestions(false);
  };

  // Nasconde i suggerimenti quando si clicca fuori
  const handlePatientInputBlur = () => {
    setTimeout(() => setShowPatientSuggestions(false), 200);
  };

  const addItem = () => {
    if (!selectedTreatment) return;

    const treatment = treatments.find(t => t.id === parseInt(selectedTreatment));
    if (!treatment) return;

    const unitPrice = customPrice ? parseFloat(customPrice) : treatment.price;
    const subtotal = quantity * unitPrice;
    const iva = 22; // Default IVA
    const ivaAmount = (subtotal * iva) / 100;
    const total = subtotal + ivaAmount;

    const newItem: Omit<InvoiceItem, 'id' | 'invoice_id'> = {
      treatment_id: treatment.id,
      quantity,
      unit_price: unitPrice,
      subtotal,
      iva,
      total,
      treatment
    };

    setItems(prev => [...prev, newItem]);
    setSelectedTreatment('');
    setQuantity(1);
    setCustomPrice('');
  };

  const removeItem = (index: number) => {
    setItems(prev => prev.filter((_, i) => i !== index));
  };

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0);
    const taxAmount = items.reduce((sum, item) => sum + (item.subtotal * item.iva / 100), 0);
    const total = subtotal + taxAmount;
    return { subtotal, taxAmount, total };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.patient_id) {
      alert('Seleziona un paziente');
      return;
    }

    if (items.length === 0) {
      alert('Aggiungi almeno un trattamento');
      return;
    }

    setLoading(true);
    try {
      const { subtotal, taxAmount, total } = calculateTotals();
      
      const invoiceData: Omit<InvoiceInsert, 'clinic_id' | 'status'> = {
        ...formData,
        invoice_number: `FAT-${Date.now()}`, // Genera numero fattura temporaneo
        subtotal,
        tax_rate: 22,
        tax_amount: taxAmount,
        total
      };

      await InvoiceService.createInvoiceWithItems(invoiceData, items);
      console.log('Invoice created successfully');
      
      // Torna all'elenco fatture
      navigate('/billing/invoices');
    } catch (error) {
      console.error('Error creating invoice:', error);
      alert('Errore nel salvataggio della fattura');
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < 3) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
        1. Seleziona Paziente
      </h3>
      
      {/* Autocomplete Paziente */}
      <div className="relative">
        <Label htmlFor="patientSearch" value="Paziente *" />
        <TextInput
          id="patientSearch"
          name="patientSearch"
          value={patientSearch}
          onChange={handlePatientSearch}
          onBlur={handlePatientInputBlur}
          onFocus={() => patientSearch.length > 0 && setShowPatientSuggestions(true)}
          placeholder="Digita il nome, cognome, codice fiscale o telefono..."
          autoComplete="off"
          required
        />
        {showPatientSuggestions && filteredPatients.length > 0 && (
          <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
            {filteredPatients.map(patient => (
              <div
                key={patient.id}
                className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                onClick={() => handleSelectPatient(patient)}
              >
                <div className="font-medium">{patient.first_name} {patient.last_name}</div>
                <div className="text-sm text-gray-500">
                  {patient.fiscal_code && `CF: ${patient.fiscal_code} • `}
                  Tel: {patient.phone}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="issue_date" value="Data Emissione *" />
          <TextInput
            type="date"
            id="issue_date"
            name="issue_date"
            value={formData.issue_date}
            onChange={handleInputChange}
            required
          />
        </div>
        <div>
          <Label htmlFor="due_date" value="Data Scadenza *" />
          <TextInput
            type="date"
            id="due_date"
            name="due_date"
            value={formData.due_date}
            onChange={handleInputChange}
            required
          />
        </div>
      </div>

      <div>
        <Label htmlFor="description" value="Descrizione *" />
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleInputChange}
          rows={3}
          placeholder="Descrizione della fattura..."
          required
        />
      </div>

      <div>
        <Label htmlFor="notes" value="Note" />
        <Textarea
          id="notes"
          name="notes"
          value={formData.notes}
          onChange={handleInputChange}
          rows={2}
          placeholder="Note aggiuntive..."
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
        2. Aggiungi Trattamenti
      </h3>
      
      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="treatment" value="Trattamento *" />
          <Select
            id="treatment"
            value={selectedTreatment}
            onChange={(e) => setSelectedTreatment(e.target.value)}
          >
            <option value="">Seleziona trattamento...</option>
            {treatments.map(treatment => (
              <option key={treatment.id} value={treatment.id}>
                {treatment.name} - €{treatment.price}
              </option>
            ))}
          </Select>
        </div>
        <div>
          <Label htmlFor="quantity" value="Quantità *" />
          <TextInput
            type="number"
            id="quantity"
            min="1"
            value={quantity}
            onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
          />
        </div>
        <div>
          <Label htmlFor="customPrice" value="Prezzo personalizzato" />
          <TextInput
            type="number"
            step="0.01"
            id="customPrice"
            placeholder="Lascia vuoto per prezzo standard"
            value={customPrice}
            onChange={(e) => setCustomPrice(e.target.value)}
          />
        </div>
      </div>
      
      <Button onClick={addItem} disabled={!selectedTreatment}>
        <Icon icon="solar:add-circle-outline" className="mr-2" height={16} />
        Aggiungi Trattamento
      </Button>

      {/* Lista trattamenti aggiunti */}
      {items.length > 0 && (
        <div>
          <h4 className="font-medium mb-2">Trattamenti aggiunti:</h4>
          <Table>
            <Table.Head>
              <Table.HeadCell>Trattamento</Table.HeadCell>
              <Table.HeadCell>Quantità</Table.HeadCell>
              <Table.HeadCell>Prezzo Unit.</Table.HeadCell>
              <Table.HeadCell>IVA</Table.HeadCell>
              <Table.HeadCell>Totale</Table.HeadCell>
              <Table.HeadCell>Azioni</Table.HeadCell>
            </Table.Head>
            <Table.Body>
              {items.map((item, index) => (
                <Table.Row key={index}>
                  <Table.Cell>{item.treatment?.name}</Table.Cell>
                  <Table.Cell>{item.quantity}</Table.Cell>
                  <Table.Cell>€{item.unit_price.toFixed(2)}</Table.Cell>
                  <Table.Cell>{item.iva}%</Table.Cell>
                  <Table.Cell>€{item.total.toFixed(2)}</Table.Cell>
                  <Table.Cell>
                    <Button
                      color="failure"
                      size="xs"
                      onClick={() => removeItem(index)}
                    >
                      <Icon icon="solar:trash-bin-minimalistic-outline" height={14} />
                    </Button>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      )}
    </div>
  );

  const renderStep3 = () => {
    const { subtotal, taxAmount, total } = calculateTotals();
    
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          3. Riepilogo e Salvataggio
        </h3>
        
        <div className="bg-gray-50 dark:bg-darkgray p-4 rounded-lg">
          <h4 className="font-medium mb-2">Riepilogo Fattura</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Paziente:</span>
              <span>{patientSearch}</span>
            </div>
            <div className="flex justify-between">
              <span>Data Emissione:</span>
              <span>{new Date(formData.issue_date).toLocaleDateString('it-IT')}</span>
            </div>
            <div className="flex justify-between">
              <span>Data Scadenza:</span>
              <span>{new Date(formData.due_date).toLocaleDateString('it-IT')}</span>
            </div>
            <div className="flex justify-between">
              <span>Trattamenti:</span>
              <span>{items.length}</span>
            </div>
            <hr className="my-2" />
            <div className="flex justify-between">
              <span>Subtotale:</span>
              <span>€{subtotal.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>IVA (22%):</span>
              <span>€{taxAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Totale:</span>
              <span>€{total.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const getStepComponent = () => {
    switch (currentStep) {
      case 1: return renderStep1();
      case 2: return renderStep2();
      case 3: return renderStep3();
      default: return renderStep1();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Progress indicator */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex space-x-4">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === currentStep
                    ? 'bg-primary text-white'
                    : step < currentStep
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}
              >
                {step < currentStep ? (
                  <Icon icon="solar:check-outline" height={16} />
                ) : (
                  step
                )}
              </div>
              {step < 3 && (
                <div
                  className={`w-16 h-1 ml-2 ${
                    step < currentStep ? 'bg-green-500' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step content */}
      <div className="min-h-[400px]">
        {getStepComponent()}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between pt-4 border-t">
        <Button
          type="button"
          color="light"
          onClick={prevStep}
          disabled={currentStep === 1}
        >
          <Icon icon="solar:arrow-left-outline" className="mr-2" height={16} />
          Indietro
        </Button>

        <div className="flex gap-2">
          {currentStep < 3 ? (
            <Button
              type="button"
              color="primary"
              onClick={nextStep}
              disabled={
                (currentStep === 1 && !formData.patient_id) ||
                (currentStep === 2 && items.length === 0)
              }
            >
              Avanti
              <Icon icon="solar:arrow-right-outline" className="ml-2" height={16} />
            </Button>
          ) : (
            <Button
              type="submit"
              color="success"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Salvataggio...
                </>
              ) : (
                <>
                  <Icon icon="solar:diskette-outline" className="mr-2" height={16} />
                  Salva Fattura
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </form>
  );
};

export default InvoiceFormPage;
