/**
 * MOKO SOSTANZA Dental CRM - User Service
 * Gestione inviti utenti e autenticazione con custom claims
 */

import { supabase } from '../lib/supabase';

export type AppRole = 'manager' | 'doctor' | 'accountant' | 'assistant';

export interface User {
  id: string;
  email: string;
  clinic_id: string | null;
  role: AppRole;
  created_at: string | null;
}

export interface InviteUserRequest {
  email: string;
  role: AppRole;
  clinic_id: string;
}

class UserServiceClass {
  /**
   * Invia invito utente via magic link con ruolo specifico
   */
  async invite(email: string, role: AppRole): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`👥 [UserService] Inviting user ${email} with role ${role}`);

      // Get current user's clinic_id from session
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        throw new Error('Not authenticated');
      }

      // For now, use demo clinic - in production would get from user's metadata
      const { data: demoClinic } = await supabase
        .from('clinics')
        .select('id')
        .eq('name', 'Demo Clinic')
        .single();

      if (!demoClinic) {
        throw new Error('Demo clinic not found');
      }

      // Check if user already exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('email')
        .eq('email', email)
        .single();

      if (existingUser) {
        return {
          success: false,
          message: 'Utente già presente nel sistema'
        };
      }

      // Create user record (will be linked during auth hook)
      const { error: insertError } = await supabase
        .from('users')
        .insert({
          id: crypto.randomUUID(), // Temporary UUID, will be replaced by auth hook
          email,
          role,
          clinic_id: demoClinic.id
        });

      if (insertError) {
        throw new Error(`Failed to create user record: ${insertError.message}`);
      }

      // Send magic link signup (stub implementation)
      console.log(`📧 [UserService] Sending magic link to ${email}...`);
      
      // TODO: Implement actual magic link signup
      // const { error: signUpError } = await supabase.auth.signUp({
      //   email,
      //   options: {
      //     data: {
      //       role,
      //       clinic_id: demoClinic.id
      //     }
      //   }
      // });

      console.log(`✅ [UserService] User ${email} invited successfully with role ${role}`);

      return {
        success: true,
        message: `Invito inviato a ${email} come ${role}`
      };

    } catch (error: any) {
      console.error(`❌ [UserService] Failed to invite user:`, error);
      return {
        success: false,
        message: error.message || 'Errore durante invito utente'
      };
    }
  }

  /**
   * Lista tutti gli utenti della clinica corrente
   */
  async getUsers(): Promise<User[]> {
    try {
      // TODO: Filter by current user's clinic_id
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch users: ${error.message}`);
      }

      return data || [];

    } catch (error: any) {
      console.error(`❌ [UserService] Failed to fetch users:`, error);
      throw error;
    }
  }

  /**
   * Rimuove utente (soft delete o disabilitazione)
   */
  async removeUser(userId: string): Promise<{ success: boolean; message: string }> {
    try {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', userId);

      if (error) {
        throw new Error(`Failed to remove user: ${error.message}`);
      }

      return {
        success: true,
        message: 'Utente rimosso con successo'
      };

    } catch (error: any) {
      console.error(`❌ [UserService] Failed to remove user:`, error);
      return {
        success: false,
        message: error.message || 'Errore durante rimozione utente'
      };
    }
  }
}

const UserService = new UserServiceClass();
export default UserService;
