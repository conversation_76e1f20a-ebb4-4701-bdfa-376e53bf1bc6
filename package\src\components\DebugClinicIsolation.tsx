/**
 * Componente temporaneo per debug dell'isolamento cliniche
 * Aggiungilo temporaneamente alla dashboard per vedere cosa succede
 */

import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { getClinicId } from '../services/ServiceUtils';

interface DebugInfo {
  user: any;
  clinicId: string;
  allPatients: any[];
  filteredPatients: any[];
  rlsTest: any;
}

export const DebugClinicIsolation: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const runDebug = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔍 === INIZIO DEBUG ISOLAMENTO ===');
      
      // 1. Test utente corrente
      console.log('👤 Test utente corrente...');
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error(`Utente non autenticato: ${userError?.message}`);
      }
      
      console.log('✅ Utente trovato:', {
        id: user.id,
        email: user.email,
        app_metadata: user.app_metadata,
        user_metadata: user.user_metadata
      });
      
      // 2. Test getClinicId
      console.log('🏥 Test getClinicId...');
      const clinicId = await getClinicId();
      console.log('✅ Clinic ID ottenuto:', clinicId);
      
      // 3. Test query tutti i pazienti (senza filtro)
      console.log('📊 Test query tutti i pazienti...');
      const { data: allPatients, error: allError } = await supabase
        .from('patients')
        .select('id, first_name, last_name, clinic_id, email')
        .limit(20);
      
      if (allError) {
        console.log('❌ Errore query tutti pazienti:', allError);
      } else {
        console.log('✅ Pazienti totali:', allPatients.length);
        console.log('📋 Lista completa:', allPatients);
      }
      
      // 4. Test query filtrata per clinic_id
      console.log('🔍 Test query filtrata per clinic_id...');
      const { data: filteredPatients, error: filteredError } = await supabase
        .from('patients')
        .select('id, first_name, last_name, clinic_id, email')
        .eq('clinic_id', clinicId)
        .limit(20);
      
      if (filteredError) {
        console.log('❌ Errore query filtrata:', filteredError);
      } else {
        console.log('✅ Pazienti filtrati:', filteredPatients.length);
        console.log('📋 Lista filtrata:', filteredPatients);
      }
      
      // 5. Test RLS indiretto
      console.log('🛡️ Test RLS indiretto...');
      let rlsTest = { active: false, message: 'Non testato' };
      
      try {
        // Prova una query che dovrebbe essere limitata da RLS
        const { data: rlsTestData, error: rlsTestError } = await supabase
          .from('patients')
          .select('count')
          .limit(1);
        
        if (rlsTestError) {
          rlsTest = { 
            active: true, 
            message: `RLS attivo - Errore: ${rlsTestError.message}` 
          };
        } else {
          rlsTest = { 
            active: false, 
            message: 'RLS non sembra attivo - Query riuscita senza restrizioni' 
          };
        }
      } catch (err) {
        rlsTest = { 
          active: true, 
          message: `RLS probabilmente attivo - Eccezione: ${err}` 
        };
      }
      
      console.log('🛡️ Risultato test RLS:', rlsTest);
      
      // Aggiorna lo stato
      setDebugInfo({
        user,
        clinicId,
        allPatients: allPatients || [],
        filteredPatients: filteredPatients || [],
        rlsTest
      });
      
      console.log('🏁 === FINE DEBUG ISOLAMENTO ===');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore sconosciuto';
      console.error('❌ Errore durante debug:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Esegui debug automaticamente al mount
    runDebug();
  }, []);

  const getClinicName = (clinicId: string) => {
    switch (clinicId) {
      case '00000000-0000-0000-0000-000000000000':
        return 'Clinica Demo';
      case '11111111-1111-1111-1111-111111111111':
        return 'Clinica Test A';
      case '22222222-2222-2222-2222-222222222222':
        return 'Clinica Test B';
      default:
        return 'Clinica Sconosciuta';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          🔍 Debug Isolamento Cliniche
        </h2>
        <button
          onClick={runDebug}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? '🔄 Testing...' : '🔍 Esegui Debug'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>❌ Errore:</strong> {error}
        </div>
      )}

      {debugInfo && (
        <div className="space-y-6">
          {/* Informazioni Utente */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">👤 Informazioni Utente</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>ID:</strong> {debugInfo.user.id}
              </div>
              <div>
                <strong>Email:</strong> {debugInfo.user.email}
              </div>
              <div>
                <strong>Clinic ID:</strong> {debugInfo.clinicId}
              </div>
              <div>
                <strong>Clinica:</strong> {getClinicName(debugInfo.clinicId)}
              </div>
            </div>
            
            {debugInfo.user.user_metadata && Object.keys(debugInfo.user.user_metadata).length > 0 && (
              <div className="mt-2">
                <strong>User Metadata:</strong>
                <pre className="bg-gray-100 p-2 rounded text-xs mt-1">
                  {JSON.stringify(debugInfo.user.user_metadata, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* Test RLS */}
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">🛡️ Test Row Level Security</h3>
            <div className={`p-2 rounded ${debugInfo.rlsTest.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              <strong>Status:</strong> {debugInfo.rlsTest.active ? '✅ Attivo' : '❌ Non Attivo'}
              <br />
              <strong>Dettagli:</strong> {debugInfo.rlsTest.message}
            </div>
          </div>

          {/* Confronto Query */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Tutti i Pazienti */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">📊 Tutti i Pazienti (Senza Filtro)</h3>
              <div className="text-sm mb-2">
                <strong>Totale:</strong> {debugInfo.allPatients.length}
              </div>
              
              {debugInfo.allPatients.length > 0 && (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {debugInfo.allPatients.map((patient, index) => (
                    <div key={index} className="bg-white p-2 rounded border text-xs">
                      <div><strong>{patient.first_name} {patient.last_name}</strong></div>
                      <div className="text-gray-600">
                        Clinic: {getClinicName(patient.clinic_id)}
                      </div>
                      <div className="text-gray-500 font-mono">
                        {patient.clinic_id}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Pazienti Filtrati */}
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">🔍 Pazienti Filtrati (Con clinic_id)</h3>
              <div className="text-sm mb-2">
                <strong>Totale:</strong> {debugInfo.filteredPatients.length}
              </div>
              
              {debugInfo.filteredPatients.length > 0 ? (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {debugInfo.filteredPatients.map((patient, index) => (
                    <div key={index} className="bg-white p-2 rounded border text-xs">
                      <div><strong>{patient.first_name} {patient.last_name}</strong></div>
                      <div className="text-gray-600">
                        Clinic: {getClinicName(patient.clinic_id)}
                      </div>
                      <div className="text-gray-500 font-mono">
                        {patient.clinic_id}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-gray-600 text-sm">
                  Nessun paziente trovato per questa clinica
                </div>
              )}
            </div>
          </div>

          {/* Diagnosi */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">🔧 Diagnosi</h3>
            <div className="space-y-2 text-sm">
              {debugInfo.allPatients.length === debugInfo.filteredPatients.length && debugInfo.allPatients.length > 0 && (
                <div className="bg-red-100 text-red-800 p-2 rounded">
                  ❌ <strong>PROBLEMA:</strong> L'utente vede tutti i pazienti invece che solo quelli della sua clinica
                </div>
              )}
              
              {debugInfo.filteredPatients.length === 0 && debugInfo.allPatients.length > 0 && (
                <div className="bg-yellow-100 text-yellow-800 p-2 rounded">
                  ⚠️ <strong>INFO:</strong> Nessun paziente nella clinica corrente, ma ci sono pazienti in altre cliniche
                </div>
              )}
              
              {debugInfo.filteredPatients.length > 0 && debugInfo.filteredPatients.length < debugInfo.allPatients.length && (
                <div className="bg-green-100 text-green-800 p-2 rounded">
                  ✅ <strong>SUCCESSO:</strong> L'isolamento sembra funzionare correttamente
                </div>
              )}
              
              {!debugInfo.rlsTest.active && (
                <div className="bg-red-100 text-red-800 p-2 rounded">
                  ❌ <strong>PROBLEMA RLS:</strong> Row Level Security non sembra attivo
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 text-xs text-gray-500">
        💡 <strong>Nota:</strong> Questo componente è temporaneo per debug. 
        Controlla anche la console browser per log dettagliati di ServiceUtils.
      </div>
    </div>
  );
};

export default DebugClinicIsolation;
