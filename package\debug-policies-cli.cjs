/**
 * Debug delle policy RLS tramite Supabase CLI
 * Verifica lo stato attuale delle policy dopo le modifiche
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 === DEBUG POLICY RLS DOPO MODIFICHE ===\n');

async function debugPolicies() {
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    console.log('1️⃣ Login come utente amministratore...');
    
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123456'
    });
    
    if (signInError) {
      console.log('❌ Errore login:', signInError.message);
      return;
    }
    
    console.log('✅ Login riuscito:', signInData.user.email);
    
    console.log('\n2️⃣ Test JWT claims dell\'utente esistente...');
    
    const { data: { user } } = await supabase.auth.getUser();
    console.log('📋 JWT claims utente esistente:');
    console.log('  - App Metadata:', JSON.stringify(user.app_metadata, null, 2));
    console.log('  - Clinic ID:', user.app_metadata?.clinic_id);
    console.log('  - App Role:', user.app_metadata?.app_role);
    
    console.log('\n3️⃣ Test creazione paziente con utente esistente...');
    
    const testPatient1 = {
      first_name: 'Test',
      last_name: 'UtenteEsistente',
      phone: '+39 ************',
      date_of_birth: '1985-01-01',
      address: 'Via Test Esistente',
      city: 'Milano',
      postal_code: '20100',
      province: 'MI',
      anamnesis: 'Test utente esistente',
      medical_history: 'None',
      clinic_id: user.app_metadata?.clinic_id || '00000000-0000-0000-0000-000000000000'
    };
    
    const { data: patient1, error: error1 } = await supabase
      .from('patients')
      .insert(testPatient1)
      .select()
      .single();
    
    if (error1) {
      console.log('❌ UTENTE ESISTENTE FALLISCE:');
      console.log(`  - Codice: ${error1.code}`);
      console.log(`  - Messaggio: ${error1.message}`);
    } else {
      console.log('✅ UTENTE ESISTENTE FUNZIONA:');
      console.log(`  - Paziente: ${patient1.first_name} ${patient1.last_name}`);
      console.log(`  - ID: ${patient1.id}`);
    }
    
    console.log('\n4️⃣ Logout e test nuovo utente...');
    
    await supabase.auth.signOut();
    
    // Registra nuovo utente
    const timestamp = Date.now();
    const newUserEmail = `debug.policy.${timestamp}@demo.com`;
    
    console.log(`📧 Registrazione nuovo utente: ${newUserEmail}`);
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: newUserEmail,
      password: 'demo123456'
    });
    
    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }
    
    console.log('✅ Nuovo utente registrato');
    
    console.log('\n5️⃣ Chiamata Edge Function per nuovo utente...');
    
    const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
      body: { user: signUpData.user }
    });
    
    if (functionError) {
      console.log('❌ Edge Function fallita:', functionError.message);
      return;
    }
    
    console.log('✅ Edge Function eseguita:', functionResult);
    
    console.log('\n6️⃣ Verifica JWT claims nuovo utente...');
    
    const { data: { user: newUser }, error: newUserError } = await supabase.auth.getUser();
    
    if (newUserError || !newUser) {
      console.log('❌ Errore recupero nuovo utente:', newUserError?.message);
      return;
    }
    
    console.log('📋 JWT claims nuovo utente:');
    console.log('  - App Metadata:', JSON.stringify(newUser.app_metadata, null, 2));
    console.log('  - Clinic ID:', newUser.app_metadata?.clinic_id);
    console.log('  - App Role:', newUser.app_metadata?.app_role);
    
    console.log('\n7️⃣ Test creazione paziente con nuovo utente...');
    
    const testPatient2 = {
      first_name: 'Test',
      last_name: 'NuovoUtente',
      phone: '+39 ************',
      date_of_birth: '1990-01-01',
      address: 'Via Test Nuovo',
      city: 'Roma',
      postal_code: '00100',
      province: 'RM',
      anamnesis: 'Test nuovo utente',
      medical_history: 'None',
      clinic_id: newUser.app_metadata?.clinic_id || '00000000-0000-0000-0000-000000000000'
    };
    
    const { data: patient2, error: error2 } = await supabase
      .from('patients')
      .insert(testPatient2)
      .select()
      .single();
    
    if (error2) {
      console.log('❌ NUOVO UTENTE FALLISCE:');
      console.log(`  - Codice: ${error2.code}`);
      console.log(`  - Messaggio: ${error2.message}`);
      console.log(`  - Clinic ID usato: ${testPatient2.clinic_id}`);
      
      console.log('\n🔧 ANALISI PROBLEMA:');
      console.log('  - JWT claims presenti:', !!newUser.app_metadata?.clinic_id);
      console.log('  - Clinic ID corretto:', newUser.app_metadata?.clinic_id);
      console.log('  - App Role presente:', !!newUser.app_metadata?.app_role);
      
      if (error2.code === '42501') {
        console.log('\n🚨 PROBLEMA RLS CONFERMATO');
        console.log('Le policy RLS per patients non riconoscono i JWT claims del nuovo utente');
        console.log('Possibili cause:');
        console.log('1. Policy non aggiornate correttamente nella Dashboard');
        console.log('2. Path JWT ancora sbagliato nelle policy');
        console.log('3. Differenza tra utenti esistenti e nuovi nella gestione JWT');
      }
    } else {
      console.log('🎉 NUOVO UTENTE FUNZIONA!');
      console.log(`  - Paziente: ${patient2.first_name} ${patient2.last_name}`);
      console.log(`  - ID: ${patient2.id}`);
      console.log('\n✅ PROBLEMA RISOLTO COMPLETAMENTE!');
    }
    
  } catch (error) {
    console.error('💥 Errore generale:', error);
  }
}

debugPolicies().then(() => {
  console.log('\n🏁 Debug policy completato');
  process.exit(0);
}).catch(error => {
  console.error('💥 Errore fatale:', error);
  process.exit(1);
});
