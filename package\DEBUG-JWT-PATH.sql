-- ===================================================================
-- DEBUG JWT PATH - VERIFICA PATH JWT NELLE POLICY RLS
-- ===================================================================
-- 
-- Questo script testa diversi path JWT per trovare quello corretto
-- 
-- ISTRUZIONI:
-- 1. Prima AUTENTICATI nella tua app con un utente esistente
-- 2. Poi esegui questo script nel dashboard Supabase
-- 3. Verifica quale path JWT restituisce il clinic_id corretto
-- 
-- ===================================================================

-- TEST 1: Verifica JWT completo
SELECT 'TEST 1: JWT COMPLETO' as test;

SELECT 
  auth.jwt() as jwt_completo,
  auth.uid() as user_id;

-- TEST 2: Verifica app_metadata
SELECT 'TEST 2: APP_METADATA' as test;

SELECT 
  auth.jwt() -> 'app_metadata' as app_metadata,
  auth.jwt() -> 'user_metadata' as user_metadata;

-- TEST 3: Verifica clinic_id con diversi path
SELECT 'TEST 3: CLINIC_ID CON DIVERSI PATH' as test;

SELECT 
  'Path 1' as path_name,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_extracted,
  (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid as clinic_id_as_uuid
UNION ALL
SELECT 
  'Path 2' as path_name,
  auth.jwt() ->> 'clinic_id' as clinic_id_extracted,
  (auth.jwt() ->> 'clinic_id')::uuid as clinic_id_as_uuid
UNION ALL
SELECT 
  'Path 3' as path_name,
  auth.jwt() -> 'user_metadata' ->> 'clinic_id' as clinic_id_extracted,
  (auth.jwt() -> 'user_metadata' ->> 'clinic_id')::uuid as clinic_id_as_uuid;

-- TEST 4: Verifica se il clinic_id è presente
SELECT 'TEST 4: VERIFICA PRESENZA CLINIC_ID' as test;

SELECT 
  CASE 
    WHEN auth.jwt() -> 'app_metadata' ->> 'clinic_id' IS NOT NULL 
    THEN 'TROVATO in app_metadata'
    WHEN auth.jwt() ->> 'clinic_id' IS NOT NULL 
    THEN 'TROVATO in root'
    WHEN auth.jwt() -> 'user_metadata' ->> 'clinic_id' IS NOT NULL 
    THEN 'TROVATO in user_metadata'
    ELSE 'NON TROVATO'
  END as clinic_id_location,
  
  COALESCE(
    auth.jwt() -> 'app_metadata' ->> 'clinic_id',
    auth.jwt() ->> 'clinic_id',
    auth.jwt() -> 'user_metadata' ->> 'clinic_id',
    'NULL'
  ) as clinic_id_value;

-- TEST 5: Verifica policy attuali
SELECT 'TEST 5: POLICY ATTUALI' as test;

SELECT 
  policyname,
  cmd,
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
ORDER BY cmd;

-- TEST 6: Test simulazione policy
SELECT 'TEST 6: SIMULAZIONE POLICY' as test;

SELECT 
  'Policy Test' as test_name,
  auth.uid() as current_user_id,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as extracted_clinic_id,
  (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid as clinic_id_uuid,
  
  -- Simula la condizione della policy
  CASE 
    WHEN (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid IS NOT NULL 
    THEN 'POLICY PASSEREBBE'
    ELSE 'POLICY FALLIREBBE'
  END as policy_result;

-- ===================================================================
-- RISULTATI ATTESI:
-- 
-- Se sei AUTENTICATO:
-- - TEST 1: Dovresti vedere il JWT completo
-- - TEST 2: Dovresti vedere app_metadata con clinic_id
-- - TEST 3: Path 1 dovrebbe mostrare il clinic_id
-- - TEST 4: Dovrebbe dire "TROVATO in app_metadata"
-- - TEST 5: Dovresti vedere 4 policy patients_rls_*_v2
-- - TEST 6: Dovrebbe dire "POLICY PASSEREBBE"
-- 
-- Se NON sei autenticato:
-- - Tutti i test mostreranno NULL
-- - Questo è normale, devi autenticarti prima
-- 
-- ===================================================================
