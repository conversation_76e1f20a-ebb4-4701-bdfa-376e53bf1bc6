/**
 * MOKO SOSTANZA Dental CRM - Invoice Status Change Modal
 * Modal intelligente per cambiare stato fatture con validazioni e note
 */

import { Modal, Button, Select, Label, Textarea, Alert, Badge } from "flowbite-react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import { InvoiceStatusConfig, getInvoiceStatusBadgeClasses, formatInvoiceStatus } from "../../utils/invoiceStatusUtils";
import InvoiceService, { type InvoiceWithDetails } from "../../services/InvoiceService";

interface StatusChangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: InvoiceWithDetails | null;
  onStatusChanged: () => void;
}

const StatusChangeModal = ({ isOpen, onClose, invoice, onStatusChanged }: StatusChangeModalProps) => {
  const [newStatus, setNewStatus] = useState(invoice?.status || 'draft');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Definisce le transizioni permesse per ogni stato
  const allowedTransitions: Record<string, string[]> = {
    draft: ['sent', 'cancelled'],
    sent: ['paid', 'overdue', 'cancelled'],
    paid: [], // Stato finale, ma permettiamo modifica manuale per correzioni
    overdue: ['paid', 'cancelled'],
    cancelled: ['draft'] // Permette di riaprire una fattura
  };

  // Stati che richiedono note obbligatorie
  const requiresNotes = ['cancelled'];
  
  // Stati che mostrano warning
  const warningStates = ['cancelled', 'overdue'];

  const getAvailableStatuses = () => {
    const currentStatus = invoice?.status || 'draft';
    const allowed = allowedTransitions[currentStatus] || [];
    
    // Aggiungi sempre lo stato corrente e "paid" per correzioni manuali
    const allStates = [...new Set([currentStatus, ...allowed, 'paid'])];
    
    return Object.entries(InvoiceStatusConfig)
      .filter(([key]) => allStates.includes(key))
      .map(([key, config]) => ({ key, config }));
  };

  const getStatusChangeMessage = () => {
    const current = invoice?.status || 'draft';
    if (newStatus === current) return null;

    const messages: Record<string, string> = {
      'draft_to_sent': 'La fattura sarà marcata come inviata e potrà essere pagata o scadere.',
      'draft_to_cancelled': 'La fattura sarà annullata e non potrà essere pagata.',
      'sent_to_paid': 'La fattura sarà marcata come pagata. Vuoi anche impostare la data di pagamento?',
      'sent_to_overdue': 'La fattura sarà marcata come scaduta.',
      'sent_to_cancelled': 'La fattura sarà annullata.',
      'overdue_to_paid': 'La fattura scaduta sarà marcata come pagata.',
      'overdue_to_cancelled': 'La fattura scaduta sarà annullata.',
      'cancelled_to_draft': 'La fattura sarà riaperta come bozza.',
      'paid_to_draft': '⚠️ ATTENZIONE: Stai riaprendo una fattura pagata. Questa azione dovrebbe essere fatta solo per correzioni.',
      'paid_to_sent': '⚠️ ATTENZIONE: Stai cambiando lo stato di una fattura pagata.',
    };

    const key = `${current}_to_${newStatus}`;
    return messages[key] || `Stato cambierà da ${formatInvoiceStatus(current || '')} a ${formatInvoiceStatus(newStatus)}.`;
  };

  const handleSubmit = async () => {
    if (!invoice) return;
    
    if (requiresNotes.includes(newStatus) && !notes.trim()) {
      const errorMsg = 'Le note sono obbligatorie per questo stato.';
      console.warn(`⚠️ [StatusChangeModal] Validation failed: ${errorMsg}`);
      setError(errorMsg);
      return;
    }

    console.log(`🔄 [StatusChangeModal] Starting status change for invoice ${invoice.id}: ${invoice.status} → ${newStatus}`);
    setLoading(true);
    setError('');

    try {
      // Se il nuovo stato è "paid", usa markAsPaid
      if (newStatus === 'paid') {
        const today = new Date().toISOString().split('T')[0];
        console.log(`💰 [StatusChangeModal] Marking invoice ${invoice.id} as paid with date ${today}`);
        await InvoiceService.markAsPaid(invoice.id, today, 'cash');
      } else {
        // Altrimenti usa updateStatus
        console.log(`📝 [StatusChangeModal] Updating invoice ${invoice.id} status to ${newStatus}`);
        await InvoiceService.updateStatus(invoice.id, newStatus);
      }

      // TODO: Gestire le note in futuro (potrebbero richiedere un campo separato)
      if (notes.trim()) {
        console.log(`📝 [StatusChangeModal] Notes for status change: ${notes.trim()}`);
        // Le note potrebbero essere gestite con un sistema di log separato
      }
      
      console.log(`✅ [StatusChangeModal] Invoice status updated successfully: ${invoice.status} → ${newStatus}`);
      onStatusChanged();
      onClose();
    } catch (error: any) {
      const errorMsg = error?.message || 'Errore sconosciuto';
      console.error(`❌ [StatusChangeModal] Status update failed for invoice ${invoice.id}:`, {
        error: error,
        message: errorMsg,
        fromStatus: invoice.status,
        toStatus: newStatus,
        invoiceId: invoice.id,
        stack: error?.stack
      });
      setError(`Errore: ${errorMsg}`);
      // UI state rollback: loading cleared in finally, status stays unchanged, modal remains open
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    try {
      console.log(`🔒 [StatusChangeModal] Modal closing for invoice ${invoice?.id || 'unknown'}`);
      
      // Reset to original status and clear form
      setNewStatus(invoice?.status || 'draft');
      setNotes('');
      setError('');
      setLoading(false);
      
      onClose();
      console.log(`✅ [StatusChangeModal] Modal closed successfully`);
    } catch (error: any) {
      console.error(`❌ [StatusChangeModal] Error during modal close:`, error);
      // Force close anyway
      onClose();
    }
  };

  if (!invoice) return null;

  const statusMessage = getStatusChangeMessage();
  const isWarning = warningStates.includes(newStatus);
  const hasChanged = newStatus !== invoice?.status;

  return (
    <Modal show={isOpen} onClose={handleClose} size="md">
      <Modal.Header>
        <div className="flex items-center gap-3">
          <Icon icon="solar:refresh-outline" className="text-xl text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold">Cambia Stato Fattura</h3>
            <p className="text-sm text-gray-500">#{invoice?.invoice_number}</p>
          </div>
        </div>
      </Modal.Header>

      <Modal.Body className="space-y-4">
        {/* Stato Attuale */}
        <div>
          <Label value="Stato Attuale" />
          <div className="mt-1">
            <Badge className={getInvoiceStatusBadgeClasses(invoice?.status)}>
              {formatInvoiceStatus(invoice?.status)}
            </Badge>
          </div>
        </div>

        {/* Nuovo Stato */}
        <div>
          <Label htmlFor="newStatus" value="Nuovo Stato *" />
          <Select
            id="newStatus"
            value={newStatus}
            onChange={(e) => setNewStatus(e.target.value)}
            required
          >
            {getAvailableStatuses().map(({ key, config }) => (
              <option key={key} value={key}>
                {config.label}
              </option>
            ))}
          </Select>
        </div>

        {/* Messaggio di Cambio Status */}
        {statusMessage && hasChanged && (
          <Alert color={isWarning ? "warning" : "info"} className="text-sm">
            <div className="flex items-start gap-2">
              <Icon 
                icon={isWarning ? "solar:danger-triangle-outline" : "solar:info-circle-outline"} 
                className="mt-0.5 flex-shrink-0" 
              />
              <span>{statusMessage}</span>
            </div>
          </Alert>
        )}

        {/* Note */}
        <div>
          <Label htmlFor="notes" value={`Note ${requiresNotes.includes(newStatus) ? '*' : '(opzionale)'}`} />
          <Textarea
            id="notes"
            rows={3}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder={
              requiresNotes.includes(newStatus) 
                ? "Motivo del cambio di stato (obbligatorio)" 
                : "Note aggiuntive sul cambio di stato..."
            }
            required={requiresNotes.includes(newStatus)}
          />
        </div>

        {error && (
          <Alert color="failure">
            <span className="font-medium">Errore:</span> {error}
          </Alert>
        )}
      </Modal.Body>

      <Modal.Footer className="flex justify-end gap-3">
        <Button color="gray" onClick={handleClose} disabled={loading}>
          Annulla
        </Button>
        <Button 
          color={isWarning ? "warning" : "primary"}
          onClick={handleSubmit}
          disabled={loading || !hasChanged}
        >
          {loading ? (
            <>
              <Icon icon="solar:refresh-outline" className="animate-spin mr-2" />
              Aggiornamento...
            </>
          ) : (
            <>
              <Icon icon="solar:check-circle-outline" className="mr-2" />
              {hasChanged ? 'Aggiorna Stato' : 'Nessun Cambio'}
            </>
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default StatusChangeModal;
