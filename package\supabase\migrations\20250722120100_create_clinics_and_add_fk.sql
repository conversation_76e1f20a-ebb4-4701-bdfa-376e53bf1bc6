-- Migration: Create clinics table and add clinic_id foreign keys
-- Created: 2025-07-22 12:01:00

-- Create clinics table
create table if not exists clinics (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  created_at timestamptz default now()
);

-- Create app_role enum
create type app_role as enum ('manager', 'doctor', 'accountant', 'assistant');

-- Create users table for auth hook
create table if not exists users (
  id uuid primary key,
  email text unique not null,
  clinic_id uuid references clinics(id),
  role app_role not null default 'doctor',
  created_at timestamptz default now()
);

-- Add clinic_id foreign key to all core tables
alter table patients add column if not exists clinic_id uuid references clinics(id);
alter table doctors add column if not exists clinic_id uuid references clinics(id);
alter table appointments add column if not exists clinic_id uuid references clinics(id);
alter table invoices add column if not exists clinic_id uuid references clinics(id);
alter table treatments add column if not exists clinic_id uuid references clinics(id);
alter table reminders add column if not exists clinic_id uuid references clinics(id);
alter table patient_files add column if not exists clinic_id uuid references clinics(id);
alter table patient_events add column if not exists clinic_id uuid references clinics(id);

-- Seed demo clinic for development with hardcoded ID
insert into clinics (id, name) values ('00000000-0000-0000-0000-000000000000', 'Demo Clinic');

-- Enable RLS and create clinic isolation policies for all tables

-- Patients table
alter table patients enable row level security;
create policy "clinic_isolation_select" on patients
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on patients
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on patients
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Doctors table
alter table doctors enable row level security;
create policy "clinic_isolation_select" on doctors
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on doctors
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on doctors
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Appointments table
alter table appointments enable row level security;
create policy "clinic_isolation_select" on appointments
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on appointments
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on appointments
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Invoices table
alter table invoices enable row level security;
create policy "clinic_isolation_select" on invoices
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on invoices
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on invoices
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Treatments table
alter table treatments enable row level security;
create policy "clinic_isolation_select" on treatments
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on treatments
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on treatments
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Reminders table
alter table reminders enable row level security;
create policy "clinic_isolation_select" on reminders
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on reminders
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on reminders
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Patient files table
alter table patient_files enable row level security;
create policy "clinic_isolation_select" on patient_files
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on patient_files
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on patient_files
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Patient events table
alter table patient_events enable row level security;
create policy "clinic_isolation_select" on patient_events
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on patient_events
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on patient_events
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Users table
alter table users enable row level security;
create policy "clinic_isolation_select" on users
  for select using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_insert" on users
  for insert with check (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);
create policy "clinic_isolation_update" on users
  for update using (clinic_id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Clinics table (users can only see their own clinic)
alter table clinics enable row level security;
create policy "clinic_isolation_select" on clinics
  for select using (id = (auth.jwt() ->> 'clinic_id')::uuid);

-- Storage policies for patient-files bucket
insert into storage.buckets (id, name, public)
values ('patient-files', 'patient-files', false)
on conflict (id) do update set public = false;

-- Storage object policy for clinic isolation
create policy "clinic_isolation_select" on storage.objects
  for select using (
    bucket_id = 'patient-files' AND
    EXISTS (
      select 1 from patient_files 
      where path = name 
      and clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
    )
  );

create policy "clinic_isolation_insert" on storage.objects
  for insert with check (
    bucket_id = 'patient-files' AND
    EXISTS (
      select 1 from patient_files 
      where path = name 
      and clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
    )
  );

create policy "clinic_isolation_update" on storage.objects
  for update using (
    bucket_id = 'patient-files' AND
    EXISTS (
      select 1 from patient_files 
      where path = name 
      and clinic_id = (auth.jwt() ->> 'clinic_id')::uuid
    )
  );
