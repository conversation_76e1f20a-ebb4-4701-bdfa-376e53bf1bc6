/**
 * MOKO SOSTANZA Dental CRM - Invoice Action Modal
 * Modal con azioni principali per le fatture - design pulsanti grandi verticali
 */

import { Modal, Button, TextInput, Label, Alert } from "flowbite-react";
import { Icon } from "@iconify/react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import InvoiceService from "../../services/InvoiceService";
import { EmailService } from "../../services/EmailService";
import type { InvoiceWithDetails } from "../../services/InvoiceService";

interface InvoiceActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: InvoiceWithDetails | null;   // usa tipo DB esistente
  onInvoiceUpdated?: () => void; // Callback per refresh lista
  onOpenEditModal?: (invoice: InvoiceWithDetails) => void; // Callback per aprire edit modal
}

const InvoiceActionModal = ({ 
  isOpen, 
  onClose, 
  invoice,
  onInvoiceUpdated,
  onOpenEditModal
}: InvoiceActionModalProps) => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [emailSent, setEmailSent] = useState(false); // Traccia se l'email è stata inviata in questa sessione

  // Pre-compila email quando si apre il modal
  useEffect(() => {
    if (isOpen && invoice) {
      console.log(`🔄 [InvoiceActionModal] Modal opened for invoice ${invoice.id}`, {
        invoiceNumber: invoice.invoice_number,
        patientEmail: invoice.patient?.email,
        status: invoice.status
      });
      
      // Reset stati UI
      setError("");
      setSuccess("");
      setLoading("");
      setEmailSent(false); // Reset stato email inviata
      
      // Pre-compila email se disponibile
      if (invoice.patient?.email) {
        setEmail(invoice.patient.email);
        console.log(`📧 [InvoiceActionModal] Email pre-filled: ${invoice.patient.email}`);
      } else {
        setEmail("");
        console.log(`📧 [InvoiceActionModal] No patient email available, field cleared`);
      }
    } else if (isOpen && !invoice) {
      console.warn(`⚠️ [InvoiceActionModal] Modal opened but no invoice provided`);
    }
  }, [invoice, isOpen]);

  const showToast = (message: string, type: 'success' | 'error') => {
    try {
      console.log(`🔔 [InvoiceActionModal] Showing ${type} toast: ${message}`);
      
      if (type === 'success') {
        setSuccess(message);
        setError("");
      } else {
        setError(message);
        setSuccess("");
      }
      
      // Auto-clear after 3 seconds
      setTimeout(() => {
        setSuccess("");
        setError("");
      }, 3000);
    } catch (error: any) {
      console.error(`❌ [InvoiceActionModal] Failed to show toast:`, error);
      // Fallback: try to show basic error
      setError("Errore di sistema");
    }
  };

  const handleSendEmail = async () => {
    if (!invoice || !email.trim()) return;
    
    console.log(`📧 [InvoiceActionModal] Starting email send for invoice ${invoice.id} (status: ${invoice.status}) to ${email}`);
    setLoading("email");
    
    try {
      // Step 1: Send email
      console.log(`📧 [InvoiceActionModal] Sending email via EmailService...`);
      await EmailService.sendInvoice(invoice.id, email);
      
      // Step 2: Update status ONLY if not already paid
      const currentStatus = invoice.status || 'draft';
      if (currentStatus !== 'paid') {
        console.log(`📧 [InvoiceActionModal] Updating invoice status from '${currentStatus}' to 'sent'...`);
        await InvoiceService.updateStatus(invoice.id, 'sent');
      } else {
        console.log(`📧 [InvoiceActionModal] Skipping status update - invoice ${invoice.id} is already paid`);
      }
      
      const actionDescription = currentStatus === 'sent' ? 'reinviata' : 
                                currentStatus === 'paid' ? 'copia inviata' : 'inviata';
      console.log(`✅ [InvoiceActionModal] Email ${actionDescription} successfully for invoice ${invoice.id}`);
      showToast(`E-mail ${actionDescription} con successo`, "success");
      setEmailSent(true); // Marca email come inviata
      onInvoiceUpdated?.();
      setTimeout(() => onClose(), 1500);
    } catch (error: any) {
      const errorMsg = error?.message || 'Errore sconosciuto';
      console.error(`❌ [InvoiceActionModal] Email send failed for invoice ${invoice.id}:`, {
        error: error,
        message: errorMsg,
        email: email,
        invoiceId: invoice.id,
        currentStatus: invoice.status,
        stack: error?.stack
      });
      showToast(`Errore: ${errorMsg}`, "error");
      // UI state rollback: loading is cleared in finally, no other state to rollback
    } finally {
      setLoading("");
    }
  };

  const handleMarkAsPaid = async () => {
    if (!invoice) return;
    
    console.log(`💰 [InvoiceActionModal] Starting mark as paid for invoice ${invoice.id}, current status: ${invoice.status}`);
    setLoading("paid");
    
    try {
      const today = new Date().toISOString().split('T')[0];
      console.log(`💰 [InvoiceActionModal] Marking invoice ${invoice.id} as paid with date ${today}`);
      
      await InvoiceService.markAsPaid(invoice.id, today, 'cash');
      
      console.log(`✅ [InvoiceActionModal] Invoice ${invoice.id} marked as paid successfully`);
      showToast("Fattura segnata come pagata", "success");
      onInvoiceUpdated?.();
      setTimeout(() => onClose(), 1500);
    } catch (error: any) {
      const errorMsg = error?.message || 'Errore sconosciuto';
      console.error(`❌ [InvoiceActionModal] Mark as paid failed for invoice ${invoice.id}:`, {
        error: error,
        message: errorMsg,
        invoiceId: invoice.id,
        currentStatus: invoice.status,
        stack: error?.stack
      });
      showToast(`Errore: ${errorMsg}`, "error");
      // UI state rollback: loading is cleared in finally, button remains enabled for retry
    } finally {
      setLoading("");
    }
  };

  const handleEditInvoice = () => {
    if (!invoice) return;
    
    try {
      console.log(`✏️ [InvoiceActionModal] Opening edit modal for invoice ${invoice.id}`);
      onClose();
      onOpenEditModal?.(invoice);
      console.log(`✅ [InvoiceActionModal] Edit modal opened successfully for invoice ${invoice.id}`);
    } catch (error: any) {
      const errorMsg = error?.message || 'Errore sconosciuto';
      console.error(`❌ [InvoiceActionModal] Edit invoice failed for invoice ${invoice.id}:`, {
        error: error,
        message: errorMsg,
        invoiceId: invoice.id,
        stack: error?.stack
      });
      showToast(`Errore: ${errorMsg}`, "error");
      // UI state rollback: modal remains open if edit failed to open
    }
  };

  const handleModalClose = () => {
    try {
      console.log(`🔒 [InvoiceActionModal] Modal closing for invoice ${invoice?.id || 'unknown'}`);
      
      // Reset all UI states on close
      setError("");
      setSuccess("");
      setLoading("");
      setEmail("");
      setEmailSent(false); // Reset stato email
      
      onClose();
      console.log(`✅ [InvoiceActionModal] Modal closed successfully`);
    } catch (error: any) {
      console.error(`❌ [InvoiceActionModal] Error during modal close:`, error);
      // Force close anyway
      onClose();
    }
  };

  if (!invoice) return null;

  return (
    <Modal show={isOpen} onClose={handleModalClose} size="md">
      <Modal.Header>
        <div className="flex items-center gap-3">
          <Icon icon="solar:settings-outline" className="text-xl text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold">Azioni Fattura</h3>
            <p className="text-sm text-gray-500">#{invoice.invoice_number}</p>
          </div>
        </div>
      </Modal.Header>

      <Modal.Body className="space-y-4">
        {/* Alert per messaggi */}
        {success && (
          <Alert color="success" className="text-sm">
            <span className="font-medium">Successo:</span> {success}
          </Alert>
        )}
        
        {error && (
          <Alert color="failure" className="text-sm">
            <span className="font-medium">Errore:</span> {error}
          </Alert>
        )}

        {/* 📧 Invia via e-mail */}
        <div className="space-y-3 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <div className="flex items-center gap-3">
            <Icon icon="solar:letter-outline" className="text-xl text-blue-600" />
            <h4 className="font-medium">Invia via E-mail</h4>
          </div>
          
          {/* Messaggi informativi basati sullo stato della fattura */}
          {/* Fattura mai inviata */}
          {invoice.status === 'draft' && !emailSent && (
            <div className="flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <Icon icon="solar:info-circle-outline" className="text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <strong>Prima invio:</strong> Dopo l'invio, lo stato cambierà automaticamente in "Inviata".
              </div>
            </div>
          )}

          {/* Fattura già inviata (sent o paid) */}
          {(invoice.status === 'sent' || invoice.status === 'paid') && !emailSent && (
            <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <Icon icon="solar:mailbox-outline" className="text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <strong>La mail è già stata inviata</strong> per questa fattura. Puoi inviarla di nuovo se necessario.
              </div>
            </div>
          )}

          {/* Messaggio di conferma dopo l'invio nella sessione corrente */}
          {emailSent && (
            <div className="flex items-start gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <Icon icon="solar:check-circle-outline" className="text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-green-800">
                <strong>Email inviata!</strong> La fattura è stata inviata con successo all'indirizzo specificato.
              </div>
            </div>
          )}
          
          <div>
            <Label htmlFor="email" value="Indirizzo Email" />
            <TextInput
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>
          <Button 
            color="blue"
            size="sm"
            onClick={handleSendEmail}
            disabled={loading === "email" || !email.trim()}
            className="w-full"
          >
            {loading === "email" ? (
              <>
                <Icon icon="solar:refresh-outline" className="animate-spin mr-2" />
                Invio in corso...
              </>
            ) : (
              <>
                <Icon icon="solar:letter-outline" className="mr-2" />
                {(invoice.status === 'sent' || invoice.status === 'paid') ? 'Invia di Nuovo' : 'Invia via E-mail'}
              </>
            )}
          </Button>
        </div>

        {/* Pulsanti azioni principali */}
        <div className="grid grid-cols-1 gap-2">
          {/* ✅ Segna Pagata */}
          <Button 
            color="success"
            size="sm"
            onClick={handleMarkAsPaid}
            disabled={loading === "paid" || invoice.status === 'paid'}
            className="flex items-center justify-start gap-2 p-3 h-auto text-left"
          >
            {loading === "paid" ? (
              <Icon icon="solar:refresh-outline" className="animate-spin text-lg" />
            ) : (
              <Icon icon="solar:check-circle-outline" className="text-lg" />
            )}
            <div>
              <div className="font-medium text-sm">Segna come Pagata</div>
              <div className="text-xs opacity-90">Imposta data pagamento ad oggi</div>
            </div>
          </Button>

          {/* ✏️ Modifica Fattura */}
          <Button 
            color="warning"
            size="sm"
            onClick={handleEditInvoice}
            className="flex items-center justify-start gap-2 p-3 h-auto text-left"
          >
            <Icon icon="solar:pen-outline" className="text-lg" />
            <div>
              <div className="font-medium text-sm">Modifica Fattura</div>
              <div className="text-xs opacity-90">Apri editor fattura</div>
            </div>
          </Button>
        </div>
      </Modal.Body>

      <Modal.Footer className="flex justify-end">
        <Button color="gray" onClick={handleModalClose}>
          Chiudi
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default InvoiceActionModal;
