import { FC, useEffect } from 'react';
import { Outlet, useNavigate } from "react-router-dom";
import ScrollToTop from 'src/components/shared/ScrollToTop';
import Sidebar from '../../components/Sidebar';
import RightSidebar from '../../components/RightSidebar';
import Header from './header/Header';
import Topbar from './header/Topbar';
import { useToast } from '../../components/shared/Toast';
import { useAuth } from '../../hooks/useAuth';
import { DevDebugPanel } from '../../components/shared/DevDebugPanel';

const FullLayout: FC = () => {
  const navigate = useNavigate();
  const { ToastContainer } = useToast();
  const { user, loading } = useAuth();

  // Gestione dell'autenticazione e routing
  useEffect(() => {
    console.log('FullLayout - user:', user, 'loading:', loading);

    if (!loading) {
      if (!user) {
        // Se non c'è un utente autenticato, reindirizza al login
        console.log('FullLayout - Nessun utente autenticato, reindirizzo al login');
        navigate('/auth/login');
      } else {
        console.log('FullLayout - Utente autenticato:', user);
        
        // Gestione routing basata sul ruolo (opzionale per il futuro)
        // Per ora tutti gli utenti vanno alla dashboard principale
        if (window.location.pathname === '/auth/login') {
          navigate('/');
        }
      }
    }
  }, [user, loading, navigate]);

  // Mostra una schermata di caricamento mentre verifichiamo l'autenticazione
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Caricamento...</p>
        </div>
      </div>
    );
  }

  // Se non c'è utente, non renderizzare il layout (il redirect è gestito nell'useEffect)
  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-lightgray dark:bg-dark">
      {/* Topbar */}
      <Topbar />

      <div className="flex relative">
        {/* Desktop Left Sidebar */}
        <div className="hidden lg:block w-[300px] flex-shrink-0">
          <Sidebar />
        </div>

        {/* Main Content */}
        <main className="flex-1 min-h-screen w-full lg:w-[calc(100%-480px)] transition-all duration-300">
          <Header />
          <div className="bg-lightgray dark:bg-dark">
            <ScrollToTop>
              <div className="p-6 md:p-8">
                <Outlet />
              </div>
            </ScrollToTop>
          </div>
        </main>

        {/* Desktop Right Sidebar */}
        <div className="hidden lg:block w-[180px] flex-shrink-0">
          <RightSidebar />
        </div>
      </div>

      {/* Toast Container */}
      <ToastContainer />
      
      {/* Debug Panel (only in development) */}
      <DevDebugPanel />
    </div>
  );
};

export default FullLayout;
