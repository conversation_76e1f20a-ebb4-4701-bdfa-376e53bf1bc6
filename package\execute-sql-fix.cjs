/**
 * 🚀 ESECUZIONE AUTOMATICA SCRIPT SQL RLS PATIENTS
 * 
 * Questo script:
 * 1. Apre automaticamente il dashboard Supabase
 * 2. Mostra lo script SQL da copiare e incollare
 * 3. Testa la soluzione dopo l'esecuzione
 */

const { createClient } = require('@supabase/supabase-js');
const { exec } = require('child_process');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

console.log('🚀 === ESECUZIONE AUTOMATICA SCRIPT SQL RLS PATIENTS ===\n');

// Script SQL completo da eseguire
const sqlScript = `-- ===================================================================
-- RICREAZIONE FORZATA POLICY RLS PATIENTS - ESECUZIONE AUTOMATICA
-- ===================================================================

-- 1. MOSTRA STATO ATTUALE
SELECT 'STATO ATTUALE POLICY PATIENTS' as info;

SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- 2. DISABILITA RLS TEMPORANEAMENTE
SELECT 'DISABILITA RLS PATIENTS' as info;
ALTER TABLE public.patients DISABLE ROW LEVEL SECURITY;

-- 3. ELIMINA TUTTE LE POLICY ESISTENTI
SELECT 'ELIMINA TUTTE LE POLICY ESISTENTI' as info;

DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_delete" ON public.patients;
DROP POLICY IF EXISTS "patients_select" ON public.patients;
DROP POLICY IF EXISTS "patients_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_update" ON public.patients;
DROP POLICY IF EXISTS "patients_delete" ON public.patients;
DROP POLICY IF EXISTS "select_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "insert_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "update_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "delete_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_select_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_insert_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_update_v2" ON public.patients;
DROP POLICY IF EXISTS "patients_rls_delete_v2" ON public.patients;

-- 4. RIABILITA RLS
SELECT 'RIABILITA RLS PATIENTS' as info;
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;

-- 5. CREA POLICY NUOVE CON NOMI UNICI
SELECT 'CREA POLICY NUOVE' as info;

CREATE POLICY "patients_rls_select_v2" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "patients_rls_insert_v2" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "patients_rls_update_v2" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

CREATE POLICY "patients_rls_delete_v2" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- 6. VERIFICA POLICY CREATE
SELECT 'VERIFICA POLICY CREATE' as info;

SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
ORDER BY cmd;

-- 7. VERIFICA RLS ABILITATO
SELECT 'VERIFICA RLS ABILITATO' as info;

SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

SELECT '=== SCRIPT COMPLETATO - TESTA CREAZIONE PAZIENTE ===' as info;`;

async function eseguiSoluzione() {
  console.log('📋 ISTRUZIONI:');
  console.log('1. Sto aprendo il dashboard Supabase...');
  console.log('2. Vai su "SQL Editor" nella barra laterale');
  console.log('3. Crea una nuova query');
  console.log('4. Copia e incolla lo script qui sotto');
  console.log('5. Clicca "Run" per eseguire');
  console.log('6. Torna qui per il test automatico\n');

  // Apri il dashboard Supabase
  const dashboardUrl = 'https://supabase.com/dashboard/project/ctfufjfktpbaufwumacq/sql/new';
  
  console.log('🌐 Apertura dashboard Supabase...\n');
  
  // Apri il browser (funziona su Windows)
  exec(`start "" "${dashboardUrl}"`, (error) => {
    if (error) {
      console.log('⚠️ Impossibile aprire automaticamente il browser');
      console.log(`📋 Apri manualmente: ${dashboardUrl}`);
    }
  });

  console.log('📄 === SCRIPT SQL DA COPIARE E INCOLLARE ===\n');
  console.log(sqlScript);
  console.log('\n📄 === FINE SCRIPT SQL ===\n');

  console.log('⏳ Aspetto che tu esegua lo script...');
  console.log('📋 Premi INVIO quando hai eseguito lo script nel dashboard Supabase');

  // Aspetta input dell'utente
  process.stdin.setRawMode(true);
  process.stdin.resume();
  process.stdin.on('data', async () => {
    process.stdin.setRawMode(false);
    process.stdin.pause();
    
    console.log('\n🧪 === TEST AUTOMATICO DELLA SOLUZIONE ===\n');
    
    // Testa la soluzione
    await testaSoluzione();
  });
}

async function testaSoluzione() {
  const supabaseUser = createClient(supabaseUrl, process.env.VITE_SUPABASE_ANON_KEY);
  const supabaseService = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('1️⃣ Verifica policy RLS create...\n');

    // Verifica policy (usando service client per bypassare RLS)
    const { data: policies, error: policiesError } = await supabaseService
      .rpc('exec', {
        sql: `SELECT policyname, cmd, qual, with_check 
              FROM pg_policies 
              WHERE schemaname = 'public' AND tablename = 'patients'
              ORDER BY cmd;`
      });

    if (policiesError) {
      console.log('⚠️ Impossibile verificare policy:', policiesError.message);
    } else {
      console.log('📋 Policy RLS attive per patients:');
      console.table(policies || []);
    }

    console.log('\n2️⃣ Test con nuovo utente...\n');

    // Logout da eventuali sessioni precedenti
    await supabaseUser.auth.signOut();

    // Crea nuovo utente per test
    const timestamp = Date.now();
    const testEmail = `test.final.${timestamp}@demo.com`;

    console.log(`📧 Creazione nuovo utente: ${testEmail}`);

    const { data: signUpData, error: signUpError } = await supabaseUser.auth.signUp({
      email: testEmail,
      password: 'demo123456'
    });

    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }

    console.log('✅ Nuovo utente creato');

    // Chiama Edge Function per iniettare JWT claims
    console.log('\n🔧 Chiamata Edge Function per JWT claims...');

    const { data: functionResult, error: functionError } = await supabaseUser.functions.invoke('issue-jwt', {
      body: { user: signUpData.user }
    });

    if (functionError) {
      console.log('❌ Errore Edge Function:', functionError.message);
      return;
    }

    console.log('✅ Edge Function eseguita:', functionResult);

    // Verifica JWT claims
    const { data: { user: updatedUser }, error: userError } = await supabaseUser.auth.getUser();

    if (userError || !updatedUser) {
      console.log('❌ Errore ricarica utente:', userError?.message);
      return;
    }

    const clinicId = updatedUser.app_metadata?.clinic_id;
    const appRole = updatedUser.app_metadata?.app_role;

    console.log('\n📋 JWT claims aggiornati:');
    console.log(`  - Clinic ID: ${clinicId}`);
    console.log(`  - App Role: ${appRole}`);

    if (!clinicId) {
      console.log('❌ JWT claims mancanti - problema nell\'Edge Function');
      return;
    }

    console.log('\n3️⃣ Test creazione paziente...\n');

    // Test creazione paziente
    const { data: newPatient, error: createError } = await supabaseUser
      .from('patients')
      .insert({
        first_name: 'Test',
        last_name: 'Finale',
        clinic_id: clinicId
      })
      .select()
      .single();

    if (createError) {
      console.log('❌ ERRORE CREAZIONE PAZIENTE:', createError.message);
      console.log('📋 Dettagli errore:', createError);
      
      if (createError.code === '42501') {
        console.log('\n🔍 ANALISI: Errore di permessi RLS');
        console.log('   - Le policy RLS stanno ancora bloccando l\'inserimento');
        console.log('   - Verifica che lo script SQL sia stato eseguito correttamente');
        console.log('   - Controlla che tutte le policy siano state create');
      }
      
      return;
    }

    console.log('✅ PAZIENTE CREATO CON SUCCESSO!');
    console.log('📋 Dati paziente:', newPatient);

    // Cleanup - rimuovi il paziente di test
    await supabaseService
      .from('patients')
      .delete()
      .eq('id', newPatient.id);

    console.log('🧹 Paziente di test rimosso');

    console.log('\n🎉 === SOLUZIONE APPLICATA CON SUCCESSO ===');
    console.log('✅ Policy RLS ricreate');
    console.log('✅ JWT claims funzionanti');
    console.log('✅ Creazione pazienti funzionante');
    console.log('\n📋 Il problema RLS è stato risolto definitivamente!');

  } catch (error) {
    console.error('💥 Errore durante il test:', error);
  }
}

// Esegui la soluzione
eseguiSoluzione();
