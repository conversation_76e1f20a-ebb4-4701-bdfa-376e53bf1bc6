/**
 * 🔍 SCRIPT AUTOMATIZZATO DIAGNOSI RLS PATIENTS
 * 
 * Questo script esegue automaticamente i test per la diagnosi del problema RLS
 * sui pazienti. Esegue i BLOCCHI B, C parziale e D.
 * 
 * NOTA: I BLOCCHI A e C completo devono essere eseguiti manualmente nel SQL Editor
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

console.log('🔍 === DIAGNOSI AUTOMATIZZATA RLS PATIENTS ===\n');

async function eseguiDiagnosi() {
  // Client per operazioni utente
  const supabaseUser = createClient(supabaseUrl, supabaseAnonKey);
  
  // Client per operazioni service (bypass RLS)
  const supabaseService = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('📋 BLOCCO B - Test JWT con nuovo utente\n');
    
    // Logout da eventuali sessioni precedenti
    await supabaseUser.auth.signOut();
    
    // Crea nuovo utente per test
    const timestamp = Date.now();
    const testEmail = `diagnosi.${timestamp}@test.com`;
    
    console.log(`📧 Creazione nuovo utente: ${testEmail}`);
    
    const { data: signUpData, error: signUpError } = await supabaseUser.auth.signUp({
      email: testEmail,
      password: 'test123456'
    });
    
    if (signUpError) {
      console.log('❌ Errore registrazione:', signUpError.message);
      return;
    }
    
    console.log('✅ Nuovo utente creato');
    console.log(`  - User ID: ${signUpData.user?.id}`);
    
    // Verifica JWT claims iniziali
    console.log('\n🔍 JWT claims PRIMA di Edge Function:');
    const { data: { user: userBefore }, error: userBeforeError } = await supabaseUser.auth.getUser();
    
    if (userBeforeError || !userBefore) {
      console.log('❌ Errore recupero utente:', userBeforeError?.message);
      return;
    }
    
    console.log('📋 App Metadata (PRIMA):', JSON.stringify(userBefore.app_metadata, null, 2));
    console.log('📋 User Metadata (PRIMA):', JSON.stringify(userBefore.user_metadata, null, 2));
    
    // Chiama Edge Function
    console.log('\n🔧 Chiamata Edge Function issue-jwt...');
    
    const { data: functionResult, error: functionError } = await supabaseUser.functions.invoke('issue-jwt', {
      body: { user: userBefore }
    });
    
    if (functionError) {
      console.log('❌ Errore Edge Function:', functionError.message);
      console.log('📋 Dettagli completi:', functionError);
    } else {
      console.log('✅ Edge Function eseguita:', functionResult);
    }
    
    // Verifica JWT claims dopo Edge Function
    console.log('\n🔍 JWT claims DOPO Edge Function:');
    const { data: { user: userAfter }, error: userAfterError } = await supabaseUser.auth.getUser();
    
    if (userAfterError || !userAfter) {
      console.log('❌ Errore ricarica utente:', userAfterError?.message);
    } else {
      console.log('📋 App Metadata (DOPO):', JSON.stringify(userAfter.app_metadata, null, 2));
      console.log('📋 User Metadata (DOPO):', JSON.stringify(userAfter.user_metadata, null, 2));
      
      const clinicId = userAfter.app_metadata?.clinic_id;
      const appRole = userAfter.app_metadata?.app_role;
      
      if (clinicId && appRole) {
        console.log(`✅ JWT claims presenti - Clinic ID: ${clinicId}, Role: ${appRole}`);
      } else {
        console.log('❌ JWT claims ancora mancanti dopo Edge Function');
      }
    }
    
    console.log('\n📋 BLOCCO D - Verifica dati reali in tabella patients\n');
    
    // Usa service client per bypassare RLS
    const { data: recentPatients, error: patientsError } = await supabaseService
      .from('patients')
      .select('id, clinic_id, created_at, first_name, last_name')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (patientsError) {
      console.log('❌ Errore recupero pazienti:', patientsError.message);
    } else {
      console.log('📊 Ultimi 5 pazienti in tabella:');
      console.table(recentPatients);
      
      // Analizza distribuzione clinic_id
      const clinicDistribution = {};
      recentPatients.forEach(patient => {
        const clinicId = patient.clinic_id || 'NULL';
        clinicDistribution[clinicId] = (clinicDistribution[clinicId] || 0) + 1;
      });
      
      console.log('\n📈 Distribuzione per clinic_id:');
      Object.entries(clinicDistribution).forEach(([clinicId, count]) => {
        console.log(`  - ${clinicId}: ${count} pazienti`);
      });
    }
    
    console.log('\n📋 BLOCCO E - Test creazione paziente con nuovo utente\n');
    
    // Test creazione paziente con il nuovo utente
    if (userAfter?.app_metadata?.clinic_id) {
      console.log('🧪 Test creazione paziente con JWT claims...');
      
      const { data: newPatient, error: createError } = await supabaseUser
        .from('patients')
        .insert({
          first_name: 'Test',
          last_name: 'Diagnosi',
          clinic_id: userAfter.app_metadata.clinic_id
        })
        .select()
        .single();
      
      if (createError) {
        console.log('❌ Errore creazione paziente:', createError.message);
        console.log('📋 Dettagli errore:', createError);
      } else {
        console.log('✅ Paziente creato con successo:', newPatient);
        
        // Cleanup - rimuovi il paziente di test
        await supabaseService
          .from('patients')
          .delete()
          .eq('id', newPatient.id);
        
        console.log('🧹 Paziente di test rimosso');
      }
    } else {
      console.log('⚠️ Impossibile testare creazione paziente: JWT claims mancanti');
    }
    
    console.log('\n🎯 === DIAGNOSI COMPLETATA ===');
    console.log('\n📋 PROSSIMI PASSI:');
    console.log('1. Esegui manualmente BLOCCO A nel SQL Editor (policy correnti)');
    console.log('2. Esegui manualmente BLOCCO C nel SQL Editor (test claim simulati)');
    console.log('3. Controlla i log Edge Function per [JWT-DUMP] completo');
    console.log('4. Riporta tutti i risultati per analisi finale');
    
  } catch (error) {
    console.error('💥 Errore durante la diagnosi:', error);
  }
}

// Esegui la diagnosi
eseguiDiagnosi();
