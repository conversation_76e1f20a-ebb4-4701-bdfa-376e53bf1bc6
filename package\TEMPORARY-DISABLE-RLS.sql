-- ===================================================================
-- DISABILITA TEMPORANEAMENTE RLS - SOLUZIONE DI EMERGENZA
-- ===================================================================
-- 
-- PROBLEMA:
-- Le policy RLS per patients non funzionano per nuovi utenti
-- Anche dopo multiple ricreazioni delle policy
-- 
-- SOLUZIONE TEMPORANEA:
-- Disabilitare RLS per patients per permettere il funzionamento
-- Mentre investighi il problema delle policy
-- 
-- ATTENZIONE:
-- Questa è una soluzione temporanea che rimuove l'isolamento cliniche
-- <PERSON><PERSON> gli utenti vedranno tutti i pazienti
-- Usare solo per test e sviluppo
-- ===================================================================

-- 1. VERIFICA STATO ATTUALE
SELECT 'STATO ATTUALE RLS' as info;

SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('patients', 'users', 'clinics', 'doctors')
ORDER BY tablename;

-- ===================================================================
-- 2. DISABILITA RLS PER PATIENTS (TEMPORANEO)
-- ===================================================================

SELECT 'DISABILITA RLS PATIENTS TEMPORANEAMENTE' as info;

ALTER TABLE public.patients DISABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 3. VERIFICA DISABILITAZIONE
-- ===================================================================

SELECT 'VERIFICA RLS DISABILITATO' as info;

SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- ===================================================================
-- 4. LOG TEMPORANEO
-- ===================================================================

DO $$
BEGIN
  RAISE NOTICE '=== RLS PATIENTS DISABILITATO TEMPORANEAMENTE ===';
  RAISE NOTICE 'ATTENZIONE: Isolamento cliniche NON attivo';
  RAISE NOTICE 'Tutti gli utenti possono vedere tutti i pazienti';
  RAISE NOTICE 'Usare solo per test e sviluppo';
  RAISE NOTICE 'Riabilitare RLS appena possibile';
  RAISE NOTICE '=== TESTA CREAZIONE PAZIENTE ===';
END $$;

-- ===================================================================
-- ISTRUZIONI:
-- ===================================================================
-- 
-- 1. Esegui questo script nella Dashboard
-- 2. Testa che i nuovi utenti possano creare pazienti
-- 3. Una volta confermato che funziona, investighi il problema RLS
-- 4. Riabilita RLS appena risolvi le policy
-- 
-- PER RIABILITARE RLS:
-- ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
-- 
-- QUESTO È SOLO TEMPORANEO!
-- 
-- ===================================================================
