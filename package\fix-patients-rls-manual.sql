-- ===================================================================
-- FIX RLS TABELLA PATIENTS - ESECUZIONE MANUALE DASHBOARD
-- ===================================================================
-- 
-- PROBLEMA IDENTIFICATO:
-- Le policy RLS per patients usano ancora il path JWT sbagliato
-- Nuovi utenti non riescono a creare pazienti anche con JWT claims corretti
--
-- SOLUZIONE:
-- Aggiornare tutte le policy per patients con il path JWT corretto:
-- auth.jwt() -> 'app_metadata' ->> 'clinic_id'
-- ===================================================================

-- 1. VERIFICA POLICY ATTUALI PER PATIENTS
SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients' 
  AND policyname LIKE 'clinic_isolation_%'
ORDER BY cmd;

-- ===================================================================
-- 2. RIMOZIONE POLICY ESISTENTI PER PATIENTS
-- ===================================================================

DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

-- ===================================================================
-- 3. CREAZIONE POLICY CORRETTE PER PATIENTS
-- ===================================================================

-- Policy SELECT: Utenti vedono solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_select" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy INSERT: Utenti possono inserire solo pazienti nella propria clinica
CREATE POLICY "clinic_isolation_insert" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy UPDATE: Utenti possono aggiornare solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_update" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy DELETE: Utenti possono eliminare solo pazienti della propria clinica
CREATE POLICY "clinic_isolation_delete" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- ===================================================================
-- 4. VERIFICA POLICY AGGIORNATE
-- ===================================================================

-- Verifica che tutte le policy siano state create correttamente
SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients' 
  AND policyname LIKE 'clinic_isolation_%'
ORDER BY cmd;

-- ===================================================================
-- 5. TEST JWT CLAIMS
-- ===================================================================

-- Mostra i JWT claims attuali per debug
SELECT 
  'JWT Debug' as test,
  auth.jwt() as full_jwt,
  auth.jwt() -> 'app_metadata' as app_metadata,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_from_jwt,
  auth.uid() as user_id;

-- ===================================================================
-- 6. VERIFICA RLS ABILITATO
-- ===================================================================

-- Verifica che RLS sia abilitato per patients
SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- ===================================================================
-- ISTRUZIONI PER L'ESECUZIONE:
-- ===================================================================
-- 
-- 1. Vai su Supabase Dashboard > SQL Editor
-- 2. Copia e incolla questo intero file
-- 3. Esegui tutto il contenuto
-- 4. Verifica che non ci siano errori
-- 5. Testa la creazione di un nuovo paziente
-- 
-- IMPORTANTE:
-- - Questo script aggiorna le policy RLS per patients
-- - Usa il path JWT corretto per app_metadata
-- - Dovrebbe risolvere il problema per i nuovi utenti
-- 
-- DOPO L'ESECUZIONE:
-- - Nuovi utenti potranno creare pazienti
-- - L'isolamento cliniche funzionerà per tutti
-- - Il sistema sarà completamente funzionale
-- 
-- ===================================================================
