/**
 * Script semplice per testare l'isolamento delle cliniche
 * Questo script può essere eseguito con Node.js per verificare che l'isolamento funzioni
 */

console.log('🏥 === TEST ISOLAMENTO CLINICHE ===');
console.log('Questo test verifica che i servizi utilizzino correttamente il filtro clinic_id\n');

// Simuliamo il test verificando che i servizi abbiano i filtri corretti
const fs = require('fs');
const path = require('path');

/**
 * Verifica che un file di servizio contenga i filtri clinic_id necessari
 */
function checkServiceFile(servicePath, serviceName) {
  try {
    const content = fs.readFileSync(servicePath, 'utf8');
    
    // Verifica che il servizio importi getClinicId
    const hasGetClinicIdImport = content.includes('getClinicId');
    
    // Verifica che il servizio usi il filtro clinic_id
    const hasClinicIdFilter = content.includes('.eq(\'clinic_id\', clinicId)');
    
    // Conta quante volte viene usato il filtro
    const filterCount = (content.match(/\.eq\('clinic_id', clinicId\)/g) || []).length;
    
    console.log(`📋 ${serviceName}:`);
    console.log(`  ✅ Import getClinicId: ${hasGetClinicIdImport ? 'SÌ' : 'NO'}`);
    console.log(`  ✅ Usa filtro clinic_id: ${hasClinicIdFilter ? 'SÌ' : 'NO'}`);
    console.log(`  📊 Numero di filtri: ${filterCount}`);
    
    if (hasGetClinicIdImport && hasClinicIdFilter && filterCount > 0) {
      console.log(`  🎉 ${serviceName} è correttamente configurato per l'isolamento!\n`);
      return true;
    } else {
      console.log(`  ⚠️  ${serviceName} potrebbe non essere completamente configurato\n`);
      return false;
    }
    
  } catch (error) {
    console.log(`  ❌ Errore nel leggere ${serviceName}: ${error.message}\n`);
    return false;
  }
}

/**
 * Lista dei servizi da verificare
 */
const servicesToCheck = [
  { path: './src/services/PatientService.ts', name: 'PatientService' },
  { path: './src/services/DoctorService.ts', name: 'DoctorService' },
  { path: './src/services/AppointmentService.ts', name: 'AppointmentService' },
  { path: './src/services/TreatmentService.ts', name: 'TreatmentService' },
  { path: './src/services/InvoiceService.ts', name: 'InvoiceService' },
  { path: './src/services/FileService.ts', name: 'FileService' },
  { path: './src/services/ReminderService.ts', name: 'ReminderService' },
  { path: './src/services/PatientEventService.ts', name: 'PatientEventService' }
];

/**
 * Esegui il test su tutti i servizi
 */
function runIsolationTest() {
  console.log('Verificando che tutti i servizi abbiano l\'isolamento clinic_id...\n');
  
  let passedServices = 0;
  let totalServices = servicesToCheck.length;
  
  servicesToCheck.forEach(service => {
    const servicePath = path.join(__dirname, service.path);
    const passed = checkServiceFile(servicePath, service.name);
    if (passed) passedServices++;
  });
  
  console.log('📊 === RISULTATI FINALI ===');
  console.log(`Servizi verificati: ${totalServices}`);
  console.log(`Servizi con isolamento: ${passedServices}`);
  console.log(`Percentuale successo: ${Math.round((passedServices / totalServices) * 100)}%`);
  
  if (passedServices === totalServices) {
    console.log('\n🎉 TUTTI I SERVIZI SONO CONFIGURATI CORRETTAMENTE!');
    console.log('L\'isolamento delle cliniche dovrebbe funzionare come previsto.');
  } else {
    console.log('\n⚠️  ALCUNI SERVIZI POTREBBERO NON ESSERE COMPLETAMENTE CONFIGURATI');
    console.log('Verifica i servizi segnalati sopra.');
  }
}

/**
 * Verifica anche che le migrazioni siano presenti
 */
function checkMigrations() {
  console.log('\n🗃️  === VERIFICA MIGRAZIONI ===');
  
  const migrationsDir = path.join(__dirname, 'supabase', 'migrations');
  
  try {
    const files = fs.readdirSync(migrationsDir);
    const migrationFiles = files.filter(file => file.endsWith('.sql'));
    
    console.log(`Trovate ${migrationFiles.length} migrazioni:`);
    
    migrationFiles.forEach(file => {
      console.log(`  📄 ${file}`);
    });
    
    // Verifica che ci siano le migrazioni per clinic_id
    const clinicIdMigrations = migrationFiles.filter(file => 
      file.includes('clinic_id') || file.includes('rls') || file.includes('isolation')
    );
    
    console.log(`\nMigrazioni per isolamento cliniche: ${clinicIdMigrations.length}`);
    clinicIdMigrations.forEach(file => {
      console.log(`  🏥 ${file}`);
    });
    
    if (clinicIdMigrations.length > 0) {
      console.log('\n✅ Migrazioni per isolamento cliniche trovate!');
    } else {
      console.log('\n⚠️  Nessuna migrazione per isolamento cliniche trovata');
    }
    
  } catch (error) {
    console.log(`❌ Errore nel leggere le migrazioni: ${error.message}`);
  }
}

/**
 * Verifica la configurazione di ServiceUtils
 */
function checkServiceUtils() {
  console.log('\n🔧 === VERIFICA SERVICE UTILS ===');
  
  try {
    const serviceUtilsPath = path.join(__dirname, 'src', 'services', 'ServiceUtils.ts');
    const content = fs.readFileSync(serviceUtilsPath, 'utf8');
    
    const hasGetClinicId = content.includes('export async function getClinicId');
    const hasWithClinicId = content.includes('export async function withClinicId');
    
    console.log(`✅ Funzione getClinicId: ${hasGetClinicId ? 'PRESENTE' : 'MANCANTE'}`);
    console.log(`✅ Funzione withClinicId: ${hasWithClinicId ? 'PRESENTE' : 'MANCANTE'}`);
    
    if (hasGetClinicId && hasWithClinicId) {
      console.log('🎉 ServiceUtils è configurato correttamente!');
    } else {
      console.log('⚠️  ServiceUtils potrebbe non essere completamente configurato');
    }
    
  } catch (error) {
    console.log(`❌ Errore nel leggere ServiceUtils: ${error.message}`);
  }
}

// Esegui tutti i test
console.log('Iniziando verifica isolamento cliniche...\n');

checkServiceUtils();
runIsolationTest();
checkMigrations();

console.log('\n🏁 === TEST COMPLETATO ===');
console.log('Se tutti i controlli sono passati, l\'isolamento delle cliniche è implementato correttamente!');
