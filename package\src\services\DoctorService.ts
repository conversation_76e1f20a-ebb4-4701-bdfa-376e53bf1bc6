/**
 * MOKO SOSTANZA Dental CRM - Enhanced Doctor Service
 * 
 * Servizio completo per la gestione dei medici/odontoiatri
 * Include gestione dati professionali, specializzazioni e disponibilità
 */

import { supabase } from '../lib/supabase';
import type { Database } from '../types/database';
import { withClinicId, getClinicId } from './ServiceUtils';

// Types from database
type Doctor = Database['public']['Tables']['doctors']['Row'];
type DoctorInsert = Database['public']['Tables']['doctors']['Insert'];
type DoctorUpdate = Database['public']['Tables']['doctors']['Update'];
type DoctorSpecialty = Database['public']['Tables']['doctor_specialties']['Row'];
type DoctorAvailability = Database['public']['Tables']['doctor_availability']['Row'];

// Extended types with relationships
export interface DoctorWithRelations extends Doctor {
  doctor_specialties?: DoctorSpecialty[]
  doctor_availability?: DoctorAvailability[]
}

export interface CreateDoctorData {
  // Anagrafe (obbligatori FNOMCeO)
  title: string
  first_name: string
  last_name: string
  fiscal_code: string
  birth_date: string
  sex: string
  citizenship?: string
  
  // Residenza (obbligatorio)
  residence_street: string
  residence_cap: string
  
  // Contatti (PEC obbligatoria per legge DL 76/2020)
  pec: string
  email?: string
  mobile?: string
  phone?: string
  
  // Iscrizione Ordine (obbligatorio)
  order_province: string
  order_number: string
  order_date: string
  albo_code?: string
  practice_status?: boolean
  
  // Dati CRM
  calendar_color?: string
  qualifications?: string
  
  // Dati fiscali (facoltativi)
  vat_id?: string
  iban?: string
  avatar_url?: string
  notes?: string
  
  // Relations
  specialties?: Array<{
    specialty_code: string
    specialty_name: string
    specialization_date: string
  }>
  availability?: Array<{
    day_of_week: number
    start_time: string
    end_time: string
    is_active: boolean
  }>
}

// Search and pagination interfaces
export interface DoctorSearchFilters {
  search?: string
  specialty?: string
  order_province?: string
  practice_status?: boolean
}

export interface PaginationOptions {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface DoctorListResult {
  doctors: DoctorWithRelations[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

export class DoctorService {
  /**
   * Ottieni tutti i dottori con filtri e paginazione
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getDoctors(
    filters: DoctorSearchFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<DoctorListResult> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();

      let query = supabase
        .from('doctors')
        .select(`
          *,
          doctor_specialties(*),
          doctor_availability(*)
        `, { count: 'exact' })
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento

      // Applica filtri di ricerca
      if (filters.search) {
        query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,pec.ilike.%${filters.search}%`)
      }

      if (filters.order_province) {
        query = query.eq('order_province', filters.order_province)
      }

      if (filters.practice_status !== undefined) {
        query = query.eq('practice_status', filters.practice_status)
      }

      // Only active doctors by default
      query = query.is('deleted_at', null)

      // Applica ordinamento
      const sortBy = pagination.sortBy || 'first_name'
      const sortOrder = pagination.sortOrder || 'asc'
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Applica paginazione
      const page = pagination.page || 1
      const limit = pagination.limit || 50
      const offset = (page - 1) * limit
      query = query.range(offset, offset + limit - 1)

      const { data: doctors, error, count } = await query

      if (error) {
        throw new Error(`Errore nel recupero dei dottori: ${error.message}`)
      }

      const total = count || 0
      const totalPages = Math.ceil(total / limit)

      return {
        doctors: doctors || [],
        pagination: {
          total,
          page,
          limit,
          totalPages
        }
      }
    } catch (error) {
      console.error('Errore in getDoctors:', error)
      throw error
    }
  }

  /**
   * Ottieni un dottore per ID con relazioni
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getDoctorById(id: number): Promise<DoctorWithRelations | null> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();

      const { data: doctor, error } = await supabase
        .from('doctors')
        .select(`
          *,
          doctor_specialties(*),
          doctor_availability(*)
        `)
        .eq('id', id)
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('deleted_at', null)
        .single()

      if (error) {
        if (error.code === 'PGRST116') return null
        throw new Error(`Errore nel recupero del dottore: ${error.message}`)
      }

      return doctor
    } catch (error) {
      console.error('Errore in getDoctorById:', error)
      throw error
    }
  }

  /**
   * Crea un nuovo dottore con specializzazioni e disponibilità
   */
  static async createDoctor(doctorData: CreateDoctorData): Promise<DoctorWithRelations> {
    try {
      // Separa i dati delle relazioni dal record principale
      const { specialties, availability, ...doctorFields } = doctorData
      
      // Inject clinic_id automatically
      const doctorFieldsWithClinic = await withClinicId(doctorFields);
      
      // Pulisci i campi che devono essere omessi se vuoti (per evitare problemi con i constraint)
      const cleanedFields: any = { ...doctorFieldsWithClinic };
      
      // Rimuovi campi vuoti che causano problemi con i constraint
      if (!cleanedFields.iban?.trim()) {
        delete cleanedFields.iban;
      }
      if (!cleanedFields.vat_id?.trim()) {
        delete cleanedFields.vat_id;
      }
      if (!cleanedFields.pec?.trim()) {
        delete cleanedFields.pec;
      }
      if (!cleanedFields.phone?.trim()) {
        delete cleanedFields.phone;
      }
      if (!cleanedFields.mobile?.trim()) {
        delete cleanedFields.mobile;
      }
      
      // Crea il dottore
      const { data: doctor, error: doctorError } = await supabase
        .from('doctors')
        .insert(cleanedFields)
        .select()
        .single()

      if (doctorError) {
        throw new Error(`Errore nella creazione del dottore: ${doctorError.message}`)
      }

      // Aggiungi specializzazioni se fornite
      if (specialties && specialties.length > 0) {
        const specialtiesData = specialties.map(specialty => ({
          doctor_id: doctor.id,
          ...specialty
        }))

        const { error: specialtiesError } = await supabase
          .from('doctor_specialties')
          .insert(specialtiesData)

        if (specialtiesError) {
          console.error('Errore nell\'inserimento delle specializzazioni:', specialtiesError)
        }
      }

      // Aggiungi disponibilità se fornita
      if (availability && availability.length > 0) {
        const availabilityData = availability.map(slot => ({
          doctor_id: doctor.id,
          ...slot
        }))

        const { error: availabilityError } = await supabase
          .from('doctor_availability')
          .insert(availabilityData)

        if (availabilityError) {
          console.error('Errore nell\'inserimento della disponibilità:', availabilityError)
        }
      }

      // Ritorna il dottore creato con le relazioni
      return await this.getDoctorById(doctor.id) as DoctorWithRelations
    } catch (error) {
      console.error('Errore in createDoctor:', error)
      throw error
    }
  }

  /**
   * Aggiorna un dottore esistente
   */
  static async updateDoctor(id: number, updates: Partial<CreateDoctorData>): Promise<DoctorWithRelations> {
    try {
      const { specialties, availability, ...doctorFields } = updates
      
      // Aggiorna il record principale
      if (Object.keys(doctorFields).length > 0) {
        const { error: doctorError } = await supabase
          .from('doctors')
          .update(doctorFields)
          .eq('id', id)
          .is('deleted_at', null)

        if (doctorError) {
          throw new Error(`Errore nell\'aggiornamento del dottore: ${doctorError.message}`)
        }
      }

      // Aggiorna specializzazioni se fornite
      if (specialties !== undefined) {
        // Elimina le specializzazioni esistenti
        await supabase
          .from('doctor_specialties')
          .delete()
          .eq('doctor_id', id)

        // Inserisci le nuove specializzazioni
        if (specialties.length > 0) {
          const specialtiesData = specialties.map(specialty => ({
            doctor_id: id,
            ...specialty
          }))

          const { error: specialtiesError } = await supabase
            .from('doctor_specialties')
            .insert(specialtiesData)

          if (specialtiesError) {
            console.error('Errore nell\'aggiornamento delle specializzazioni:', specialtiesError)
          }
        }
      }

      // Aggiorna disponibilità se fornita
      if (availability !== undefined) {
        // Elimina la disponibilità esistente
        await supabase
          .from('doctor_availability')
          .delete()
          .eq('doctor_id', id)

        // Inserisci la nuova disponibilità
        if (availability.length > 0) {
          const availabilityData = availability.map(slot => ({
            doctor_id: id,
            ...slot
          }))

          const { error: availabilityError } = await supabase
            .from('doctor_availability')
            .insert(availabilityData)

          if (availabilityError) {
            console.error('Errore nell\'aggiornamento della disponibilità:', availabilityError)
          }
        }
      }

      // Ritorna il dottore aggiornato
      return await this.getDoctorById(id) as DoctorWithRelations
    } catch (error) {
      console.error('Errore in updateDoctor:', error)
      throw error
    }
  }

  /**
   * Eliminazione soft di un dottore (marcato come deleted)
   */
  static async deleteDoctor(id: number): Promise<void> {
    try {
      const { error } = await supabase
        .from('doctors')
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', id)

      if (error) {
        throw new Error(`Errore nell\'eliminazione del dottore: ${error.message}`)
      }
    } catch (error) {
      console.error('Errore in deleteDoctor:', error)
      throw error
    }
  }

  /**
   * Ricerca dottori per nome, cognome o PEC
   */
  static async searchDoctors(query: string): Promise<DoctorWithRelations[]> {
    try {
      const { data: doctors, error } = await supabase
        .from('doctors')
        .select(`
          *,
          doctor_specialties(*),
          doctor_availability(*)
        `)
        .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,pec.ilike.%${query}%`)
        .is('deleted_at', null)
        .order('first_name')

      if (error) {
        throw new Error(`Errore nella ricerca dei dottori: ${error.message}`)
      }

      return doctors || []
    } catch (error) {
      console.error('Errore in searchDoctors:', error)
      throw error
    }
  }

  /**
   * Ottieni statistiche sui dottori della clinica corrente
   * Applica automaticamente il filtro clinic_id per l'isolamento multi-tenant
   */
  static async getDoctorStats(): Promise<{
    total: number
    active: number
    byProvince: Array<{ province: string; count: number }>
    bySpecialty: Array<{ specialty_name: string; count: number }>
  }> {
    try {
      // Ottieni clinic_id per l'isolamento
      const clinicId = await getClinicId();

      // Total count
      const { count: total, error: totalError } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true })
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento

      if (totalError) throw totalError

      // Active count
      const { count: active, error: activeError } = await supabase
        .from('doctors')
        .select('*', { count: 'exact', head: true })
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('deleted_at', null)
        .eq('practice_status', true)

      if (activeError) throw activeError

      // By province
      const { data: provinceData, error: provinceError } = await supabase
        .from('doctors')
        .select('order_province')
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('deleted_at', null)

      if (provinceError) throw provinceError

      const byProvince = provinceData?.reduce((acc: Array<{ province: string; count: number }>, doc) => {
        const existing = acc.find(p => p.province === doc.order_province)
        if (existing) {
          existing.count++
        } else {
          acc.push({ province: doc.order_province, count: 1 })
        }
        return acc
      }, []) || []

      // By specialty (con filtro clinic_id)
      const { data: specialtyData, error: specialtyError } = await supabase
        .from('doctor_specialties')
        .select('specialty_name, doctors!inner(*)')
        .eq('clinic_id', clinicId) // Filtro clinic_id per isolamento
        .is('doctors.deleted_at', null)

      if (specialtyError) throw specialtyError

      const bySpecialty = specialtyData?.reduce((acc: Array<{ specialty_name: string; count: number }>, spec) => {
        const existing = acc.find(s => s.specialty_name === spec.specialty_name)
        if (existing) {
          existing.count++
        } else {
          acc.push({ specialty_name: spec.specialty_name, count: 1 })
        }
        return acc
      }, []) || []

      return {
        total: total || 0,
        active: active || 0,
        byProvince: byProvince.sort((a, b) => b.count - a.count),
        bySpecialty: bySpecialty.sort((a, b) => b.count - a.count)
      }
    } catch (error) {
      console.error('Errore in getDoctorStats:', error)
      throw error
    }
  }
}