import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  ReminderService, 
  type Reminder, 
  type ReminderInsert, 
  type ReminderUpdate, 
  type ReminderSearchFilters 
} from '../services/ReminderService';

interface ReminderState {
  // State
  reminders: Reminder[];
  currentPatientReminders: Reminder[];
  loading: boolean;
  error: string | null;
  
  // Actions
  loadReminders: (filters?: ReminderSearchFilters) => Promise<void>;
  loadPatientReminders: (patientId: string) => Promise<void>;
  createReminder: (reminder: ReminderInsert) => Promise<void>;
  createPatientReminder: (
    patientId: string, 
    reminder: Omit<ReminderInsert, 'patient_id'>, 
    channel?: string
  ) => Promise<void>;
  updateReminder: (id: number, reminder: ReminderUpdate) => Promise<void>;
  deleteReminder: (id: number) => Promise<void>;
  toggleCompleted: (id: number) => Promise<void>;
  
  // Helper actions
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useReminderStore = create<ReminderState>()(
  devtools(
    (set, get) => ({
      // Initial state
      reminders: [],
      currentPatientReminders: [],
      loading: false,
      error: null,

      // Actions
      loadReminders: async (filters?: ReminderSearchFilters) => {
        set({ loading: true, error: null });
        try {
          const reminders = await ReminderService.getReminders(filters);
          set({ reminders, loading: false });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Errore nel caricamento promemoria',
            loading: false 
          });
        }
      },

      loadPatientReminders: async (patientId: string) => {
        if (!patientId) {
          set({ 
            error: 'ID paziente non valido',
            loading: false,
            currentPatientReminders: []
          });
          return;
        }

        set({ loading: true, error: null });
        try {
          const currentPatientReminders = await ReminderService.getRemindersByPatient(patientId);
          set({ currentPatientReminders, loading: false });
        } catch (error) {
          console.error('Errore nel caricamento promemoria paziente:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Errore nel caricamento promemoria paziente',
            loading: false,
            currentPatientReminders: []
          });
        }
      },

      createReminder: async (reminder: ReminderInsert) => {
        set({ loading: true, error: null });
        try {
          const newReminder = await ReminderService.createReminder(reminder);
          set(state => ({ 
            reminders: [...state.reminders, newReminder],
            loading: false 
          }));
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Errore nella creazione promemoria',
            loading: false 
          });
        }
      },

      createPatientReminder: async (
        patientId: string, 
        reminder: Omit<ReminderInsert, 'patient_id'>, 
        channel: string = 'general'
      ) => {
        set({ loading: true, error: null });
        try {
          const newReminder = await ReminderService.createPatientReminder(patientId, reminder, channel);
          set(state => ({ 
            reminders: [...state.reminders, newReminder],
            currentPatientReminders: [...state.currentPatientReminders, newReminder],
            loading: false 
          }));
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Errore nella creazione promemoria paziente',
            loading: false 
          });
        }
      },

      updateReminder: async (id: number, reminder: ReminderUpdate) => {
        set({ loading: true, error: null });
        try {
          const updatedReminder = await ReminderService.updateReminder(id, reminder);
          set(state => ({
            reminders: state.reminders.map(r => r.id === id ? updatedReminder : r),
            currentPatientReminders: state.currentPatientReminders.map(r => r.id === id ? updatedReminder : r),
            loading: false
          }));
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Errore nell\'aggiornamento promemoria',
            loading: false 
          });
        }
      },

      deleteReminder: async (id: number) => {
        set({ loading: true, error: null });
        try {
          await ReminderService.deleteReminder(id);
          set(state => ({
            reminders: state.reminders.filter(r => r.id !== id),
            currentPatientReminders: state.currentPatientReminders.filter(r => r.id !== id),
            loading: false
          }));
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Errore nell\'eliminazione promemoria',
            loading: false 
          });
        }
      },

      toggleCompleted: async (id: number) => {
        set({ loading: true, error: null });
        try {
          const updatedReminder = await ReminderService.toggleCompleted(id);
          set(state => ({
            reminders: state.reminders.map(r => r.id === id ? updatedReminder : r),
            currentPatientReminders: state.currentPatientReminders.map(r => r.id === id ? updatedReminder : r),
            loading: false
          }));
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Errore nel cambio stato promemoria',
            loading: false 
          });
        }
      },

      // Helper actions
      clearError: () => set({ error: null }),
      setLoading: (loading: boolean) => set({ loading }),
    }),
    {
      name: 'reminder-store',
    }
  )
);
