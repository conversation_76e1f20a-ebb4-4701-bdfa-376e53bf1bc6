# 🔍 DIAGNOSI COMPLETA RLS PATIENTS - ISTRUZIONI DETTAGLIATE

## 📋 PANORAMICA
Questo documento contiene le istruzioni complete per diagnosticare il problema RLS sulla tabella `patients`. Esegui tutti i blocchi in sequenza e riporta INTEGRALMENTE ogni risultato.

---

## 🔍 BLOCCO A – Policy realmente attive

### A.1 - Esegui query SQL per policy correnti
```sql
-- Esegui nel SQL Editor di Supabase con service-role
SELECT polname,
       permissive,
       roles,
       cmd,
       qual,
       with_check
FROM pg_policies
WHERE schemaname = 'public'
  AND tablename  = 'patients';
```

### A.2 - Verifica stato RLS e owner tabella
```sql
-- Esegui nel SQL Editor di Supabase con service-role
SELECT relrowsecurity,
       relforcerowsecurity,
       relowner
FROM pg_class
WHERE relname = 'patients';
```

**📝 RIPORTA:** Tutte le righe restituite da entrambe le query

---

## 🔍 BLOCCO B – JWT effettivo

### B.1 - Deploy Edge Function con JWT dump
Ho già aggiunto il log JWT dump nell'Edge Function `issue-jwt/index.ts` alla riga 92:
```typescript
// 🔍 BLOCCO B - JWT DUMP COMPLETO per diagnosi
console.log('[JWT-DUMP]', JSON.stringify(user, null, 2));
```

### B.2 - Deploy e test
```bash
# Deploy della funzione aggiornata
cd package
supabase functions deploy issue-jwt
```

### B.3 - Crea un nuovo utente e cattura il JWT dump
```bash
# Esegui il debug script per nuovo utente
node debug-new-user-issue.cjs
```

### B.4 - Controlla i log di Supabase
1. Vai su Supabase Dashboard > Logs > Edge Functions
2. Cerca i log `[JWT-DUMP]` 
3. Copia il JSON completo del nuovo utente

**📝 RIPORTA:** 
- Il JSON completo del JWT dump del nuovo utente
- La sezione `app_metadata` e `user_metadata` del log
- Confronta side-by-side con un utente funzionante esistente

---

## 🔍 BLOCCO C – Test psql con claim simulati

### C.1 - Test con clinic demo (dovrebbe passare)
```sql
-- Esegui nel SQL Editor con service-role
SET request.jwt.claims = '{"clinic_id":"00000000-0000-0000-0000-000000000000"}';

INSERT INTO patients(id, first_name, clinic_id)
VALUES (gen_random_uuid(), 'Test-A', '00000000-0000-0000-0000-000000000000');
```

### C.2 - Test con altra clinic (dovrebbe fallire)
```sql
-- Esegui nel SQL Editor con service-role
SET request.jwt.claims = '{"clinic_id":"11111111-1111-1111-1111-111111111111"}';

INSERT INTO patients(id, first_name, clinic_id)
VALUES (gen_random_uuid(), 'Test-B', '11111111-1111-1111-1111-111111111111');
```

### C.3 - Reset per sicurezza
```sql
-- Esegui sempre dopo i test
RESET request.jwt.claims;
```

**📝 RIPORTA:** 
- Se ogni INSERT passa o fallisce
- Il messaggio di errore completo se fallisce
- Il numero di righe inserite se passa

---

## 🔍 BLOCCO D – Dati reali in tabella

### D.1 - Ultimi record patients
```sql
-- Esegui nel SQL Editor con service-role
SELECT id, clinic_id, created_at, first_name, last_name
FROM patients
ORDER BY created_at DESC
LIMIT 5;
```

**📝 RIPORTA:** Tutte le righe restituite con i valori di `clinic_id`

---

## 🔍 BLOCCO E – Codice client

### E.1 - ServiceUtils.ts
**📝 RIPORTA:** Il codice completo delle funzioni:
- `getClinicId()` (righe 12-78)
- `withClinicId()` (righe 83-86)

### E.2 - PatientService.createPatient()
**📝 RIPORTA:** Il codice completo del metodo:
- `createPatient()` (righe 158-170)
- Specialmente le righe dalla chiamata `.insert()` fino alla `.single()`

---

## 📋 CHECKLIST ESECUZIONE

- [ ] **BLOCCO A**: Eseguito query policy e stato RLS
- [ ] **BLOCCO B**: Deploy Edge Function e catturato JWT dump
- [ ] **BLOCCO C**: Testato INSERT con claim simulati
- [ ] **BLOCCO D**: Verificato dati reali in tabella
- [ ] **BLOCCO E**: Copiato codice ServiceUtils e PatientService

---

## ⚠️ IMPORTANTE

1. **Usa sempre service-role** per le query SQL per bypassare RLS
2. **Non modificare** policy, trigger, Edge Function o dati durante la diagnosi
3. **Riporta INTEGRALMENTE** ogni risultato (tabelle, JSON, errori)
4. **Esegui i comandi UNO ALLA VOLTA** e verifica ogni risultato

---

## 🚀 PROSSIMI PASSI

Una volta completata la diagnosi con tutti i risultati, potremo:
1. Identificare la causa esatta del problema
2. Applicare la correzione mirata
3. Testare la soluzione
4. Aggiornare la documentazione
