-- Migration: Add invoice status enum column
-- Description: Adds a status column to invoices table with proper enum constraints
-- Author: <PERSON><PERSON><PERSON> SOSTANZA Dental CRM
-- Date: 2025-07-21

-- Add status column to invoices table with enum constraints
ALTER TABLE invoices 
ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'draft'
CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled'));

-- Add comment for documentation
COMMENT ON COLUMN invoices.status IS 'Invoice status: draft, sent, paid, overdue, cancelled';

-- Create index for performance on status queries
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_status_due_date ON invoices(status, due_date) WHERE status = 'sent';

-- Update existing invoices to have proper status based on payment_date
UPDATE invoices 
SET status = CASE 
    WHEN payment_date IS NOT NULL THEN 'paid'
    WHEN due_date < CURRENT_DATE THEN 'overdue'
    ELSE 'draft'
END
WHERE deleted_at IS NULL;
