/**
 * MOKO SOSTANZA Dental CRM - Users Settings View
 * Gestione utenti e inviti per la clinica
 */

import { useState } from 'react';
import { Button, TextInput, Label, Select, Alert, Card, Badge } from 'flowbite-react';
import { Icon } from '@iconify/react';
import UserService, { type AppRole } from '../../services/UserService';

const UsersSettings = () => {
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<AppRole>('doctor');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleInviteUser = async () => {
    if (!inviteEmail.trim()) return;

    setLoading(true);
    try {
      const result = await UserService.invite(inviteEmail, inviteRole);
      
      setMessage({
        type: result.success ? 'success' : 'error',
        text: result.message
      });

      if (result.success) {
        setInviteEmail('');
        setInviteRole('doctor');
      }

    } catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || 'Errore durante invito'
      });
    } finally {
      setLoading(false);
    }
  };

  const roleLabels: Record<AppRole, string> = {
    manager: 'Manager',
    doctor: 'Dottore',
    accountant: 'Contabile',
    assistant: 'Assistente'
  };

  const roleColors: Record<AppRole, string> = {
    manager: 'bg-purple-100 text-purple-800',
    doctor: 'bg-blue-100 text-blue-800',
    accountant: 'bg-green-100 text-green-800',
    assistant: 'bg-yellow-100 text-yellow-800'
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Gestione Utenti</h1>
        <p className="text-gray-600">Invita nuovi utenti e gestisci i permessi della clinica</p>
      </div>

      {/* Alert messaggi */}
      {message && (
        <Alert 
          color={message.type === 'success' ? 'success' : 'failure'} 
          className="mb-6"
          onDismiss={() => setMessage(null)}
        >
          {message.text}
        </Alert>
      )}

      {/* Form Invita Utente */}
      <Card className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          <Icon icon="solar:user-plus-outline" className="text-xl text-blue-600" />
          <h2 className="text-lg font-semibold">Invita Nuovo Utente</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <Label htmlFor="invite-email" value="Email" />
            <TextInput
              id="invite-email"
              type="email"
              value={inviteEmail}
              onChange={(e) => setInviteEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <Label htmlFor="invite-role" value="Ruolo" />
            <Select
              id="invite-role"
              value={inviteRole}
              onChange={(e) => setInviteRole(e.target.value as AppRole)}
              required
            >
              <option value="doctor">Dottore</option>
              <option value="manager">Manager</option>
              <option value="accountant">Contabile</option>
              <option value="assistant">Assistente</option>
            </Select>
          </div>

          <div className="flex items-end">
            <Button
              color="blue"
              onClick={handleInviteUser}
              disabled={loading || !inviteEmail.trim()}
              className="w-full"
            >
              {loading ? (
                <>
                  <Icon icon="solar:refresh-outline" className="animate-spin mr-2" />
                  Invio...
                </>
              ) : (
                <>
                  <Icon icon="solar:letter-outline" className="mr-2" />
                  Invita
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Descrizione ruoli */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium mb-2">Descrizione Ruoli:</h3>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div><strong>Manager:</strong> Accesso completo, gestione utenti</div>
            <div><strong>Dottore:</strong> Gestione pazienti, appuntamenti</div>
            <div><strong>Contabile:</strong> Fatturazione, pagamenti</div>
            <div><strong>Assistente:</strong> Appuntamenti, base pazienti</div>
          </div>
        </div>
      </Card>

      {/* Lista Utenti Stub */}
      <Card>
        <div className="flex items-center gap-3 mb-4">
          <Icon icon="solar:users-group-rounded-outline" className="text-xl text-blue-600" />
          <h2 className="text-lg font-semibold">Utenti Clinica</h2>
        </div>

        <div className="space-y-3">
          {/* Utenti mock per visualizzazione */}
          <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Icon icon="solar:user-outline" className="text-blue-600" />
              </div>
              <div>
                <div className="font-medium"><EMAIL></div>
                <div className="text-sm text-gray-500">Attivo dal 22/07/2025</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={roleColors.manager}>
                {roleLabels.manager}
              </Badge>
              <Button size="xs" color="gray">
                <Icon icon="solar:settings-outline" />
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <Icon icon="solar:user-outline" className="text-green-600" />
              </div>
              <div>
                <div className="font-medium"><EMAIL></div>
                <div className="text-sm text-gray-500">Attivo dal 22/07/2025</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={roleColors.doctor}>
                {roleLabels.doctor}
              </Badge>
              <Button size="xs" color="gray">
                <Icon icon="solar:settings-outline" />
              </Button>
            </div>
          </div>

          <div className="text-center py-4 text-gray-500 text-sm">
            {/* Placeholder per quando UserService sarà completamente implementato */}
            <Icon icon="solar:user-plus-outline" className="mx-auto mb-2 text-2xl" />
            Invita nuovi utenti per vederli apparire qui
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UsersSettings;
