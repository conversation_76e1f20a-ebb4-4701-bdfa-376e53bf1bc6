-- ===================================================================
-- RICREAZIONE FORZATA POLICY RLS PATIENTS - ULTIMA SOLUZIONE
-- ===================================================================
-- 
-- PROBLEMA CONFERMATO:
-- - Utenti esistenti funzionano
-- - Nuovi utenti falliscono con JWT claims identici
-- - Le policy RLS per patients NON sono state applicate correttamente
-- 
-- SOLUZIONE DRASTICA:
-- Disabilitare RLS, eliminare TUTTO, ricreare da zero
-- ===================================================================

-- 1. MOSTRA STATO ATTUALE
SELECT 'STATO ATTUALE POLICY PATIENTS' as info;

SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- ===================================================================
-- 2. DISABILITA COMPLETAMENTE RLS PER PATIENTS
-- ===================================================================

SELECT 'DISABILITA RLS PATIENTS' as info;

ALTER TABLE public.patients DISABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 3. ELIMINA TUTTE LE POLICY ESISTENTI (QUALSIASI NOME)
-- ===================================================================

SELECT 'ELIMINA TUTTE LE POLICY ESISTENTI' as info;

-- Elimina policy con nomi standard
DROP POLICY IF EXISTS "clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "clinic_isolation_delete" ON public.patients;

-- Elimina policy con nomi alternativi
DROP POLICY IF EXISTS "patients_clinic_isolation_select" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_update" ON public.patients;
DROP POLICY IF EXISTS "patients_clinic_isolation_delete" ON public.patients;

-- Elimina policy con nomi semplici
DROP POLICY IF EXISTS "patients_select" ON public.patients;
DROP POLICY IF EXISTS "patients_insert" ON public.patients;
DROP POLICY IF EXISTS "patients_update" ON public.patients;
DROP POLICY IF EXISTS "patients_delete" ON public.patients;

-- Elimina eventuali altre policy
DROP POLICY IF EXISTS "select_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "insert_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "update_own_clinic_patients" ON public.patients;
DROP POLICY IF EXISTS "delete_own_clinic_patients" ON public.patients;

-- ===================================================================
-- 4. VERIFICA CHE NON CI SIANO PIÙ POLICY
-- ===================================================================

SELECT 'VERIFICA NESSUNA POLICY RIMASTA' as info;

SELECT 
  COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- ===================================================================
-- 5. RIABILITA RLS
-- ===================================================================

SELECT 'RIABILITA RLS PATIENTS' as info;

ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;

-- ===================================================================
-- 6. CREA POLICY NUOVE CON NOMI UNICI
-- ===================================================================

SELECT 'CREA POLICY NUOVE' as info;

-- Policy SELECT con nome unico
CREATE POLICY "patients_rls_select_v2" ON public.patients
  FOR SELECT USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy INSERT con nome unico
CREATE POLICY "patients_rls_insert_v2" ON public.patients
  FOR INSERT WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy UPDATE con nome unico
CREATE POLICY "patients_rls_update_v2" ON public.patients
  FOR UPDATE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  ) WITH CHECK (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- Policy DELETE con nome unico
CREATE POLICY "patients_rls_delete_v2" ON public.patients
  FOR DELETE USING (
    clinic_id = (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid
  );

-- ===================================================================
-- 7. VERIFICA POLICY CREATE
-- ===================================================================

SELECT 'VERIFICA POLICY CREATE' as info;

SELECT 
  policyname, 
  cmd, 
  qual as using_condition,
  with_check as with_check_condition
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'patients'
ORDER BY cmd;

-- ===================================================================
-- 8. VERIFICA RLS ABILITATO
-- ===================================================================

SELECT 'VERIFICA RLS ABILITATO' as info;

SELECT 
  tablename, 
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename = 'patients';

-- ===================================================================
-- 9. TEST JWT CLAIMS CORRENTE
-- ===================================================================

SELECT 'TEST JWT CLAIMS' as info;

SELECT 
  'JWT Debug' as test,
  auth.jwt() -> 'app_metadata' as app_metadata,
  auth.jwt() -> 'app_metadata' ->> 'clinic_id' as clinic_id_extracted,
  (auth.jwt() -> 'app_metadata' ->> 'clinic_id')::uuid as clinic_id_as_uuid,
  auth.uid() as user_id;

-- ===================================================================
-- 10. LOG FINALE
-- ===================================================================

DO $$
BEGIN
  RAISE NOTICE '=== POLICY RLS PATIENTS RICREATE COMPLETAMENTE ===';
  RAISE NOTICE 'Tutte le policy precedenti eliminate';
  RAISE NOTICE 'Policy nuove create con nomi unici: patients_rls_*_v2';
  RAISE NOTICE 'Path JWT: auth.jwt() -> ''app_metadata'' ->> ''clinic_id''';
  RAISE NOTICE 'RLS abilitato per patients';
  RAISE NOTICE '=== TESTA CREAZIONE PAZIENTE NUOVO UTENTE ===';
END $$;

-- ===================================================================
-- ISTRUZIONI FINALI:
-- ===================================================================
-- 
-- 1. Esegui questo script COMPLETO nella Dashboard
-- 2. Verifica che tutte le policy siano state create
-- 3. Testa immediatamente la creazione di un paziente
-- 
-- QUESTO SCRIPT:
-- - Elimina TUTTE le policy esistenti per patients
-- - Ricrea policy completamente nuove con nomi unici
-- - Usa il path JWT corretto garantito
-- - Dovrebbe risolvere definitivamente il problema
-- 
-- SE QUESTO NON FUNZIONA:
-- Il problema è più profondo (cache Supabase, configurazione auth, etc.)
-- 
-- ===================================================================
