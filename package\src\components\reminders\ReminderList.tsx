import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge } from 'flowbite-react';
import { FiClock, FiCheck, FiEdit, FiTrash2, FiUser } from 'react-icons/fi';
import { Reminder } from '../../services/ReminderService';
import { useReminderStore } from '../../store/useReminderStore';
import { useToast } from '../shared/Toast';

interface ReminderListProps {
  reminders: Reminder[];
  onEdit?: (reminder: Reminder) => void;
  showPatientInfo?: boolean;
}

const ReminderList: React.FC<ReminderListProps> = ({ 
  reminders, 
  onEdit, 
  showPatientInfo = false 
}) => {
  const { toggleCompleted, deleteReminder, loading } = useReminderStore();
  const { showToast } = useToast();

  const handleToggleCompleted = async (reminder: Reminder) => {
    try {
      await toggleCompleted(reminder.id);
      showToast(
        reminder.completed ? 'Promemoria rimarcato come da fare' : 'Promemoria completato',
        'success'
      );
    } catch (error) {
      showToast('Errore nel cambio stato promemoria', 'error');
    }
  };

  const handleDelete = async (reminder: Reminder) => {
    if (confirm('Sei sicuro di voler eliminare questo promemoria?')) {
      try {
        await deleteReminder(reminder.id);
        showToast('Promemoria eliminato con successo', 'success');
      } catch (error) {
        showToast('Errore nell\'eliminazione del promemoria', 'error');
      }
    }
  };

  const formatDate = (date: string) => {
    const reminderDate = new Date(date);
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);

    if (reminderDate.toDateString() === today.toDateString()) {
      return 'Oggi';
    } else if (reminderDate.toDateString() === tomorrow.toDateString()) {
      return 'Domani';
    } else {
      return reminderDate.toLocaleDateString('it-IT');
    }
  };

  const formatTime = (time: string) => {
    return time.substring(0, 5); // HH:MM
  };

  const getChannelColor = (channel: string | null | undefined) => {
    switch (channel) {
      case 'appointment':
        return 'blue';
      case 'follow-up':
        return 'green';
      case 'treatment':
        return 'purple';
      case 'email':
        return 'yellow';
      case 'sms':
        return 'pink';
      case 'phone':
        return 'indigo';
      default:
        return 'gray';
    }
  };

  const getChannelLabel = (channel: string | null | undefined) => {
    switch (channel) {
      case 'appointment':
        return 'Appuntamento';
      case 'follow-up':
        return 'Follow-up';
      case 'treatment':
        return 'Trattamento';
      case 'email':
        return 'Email';
      case 'sms':
        return 'SMS';
      case 'phone':
        return 'Telefono';
      default:
        return 'Generale';
    }
  };

  if (reminders.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FiClock className="mx-auto mb-2 h-8 w-8" />
        <p>Nessun promemoria trovato</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {reminders.map((reminder) => (
        <Card key={reminder.id} className={`${reminder.completed ? 'opacity-60' : ''}`}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className={`font-medium ${reminder.completed ? 'line-through text-gray-500' : ''}`}>
                  {reminder.title}
                </h3>
                <Badge color={getChannelColor(reminder.channel)} size="sm">
                  {getChannelLabel(reminder.channel)}
                </Badge>
                {reminder.patient_id && showPatientInfo && (
                  <Badge color="indigo" size="sm">
                    <FiUser className="w-3 h-3 mr-1" />
                    Paziente
                  </Badge>
                )}
              </div>
              
              <p className={`text-sm mb-2 ${reminder.completed ? 'line-through text-gray-500' : 'text-gray-600'}`}>
                {reminder.text}
              </p>
              
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span className="flex items-center gap-1">
                  <FiClock className="w-3 h-3" />
                  {formatDate(reminder.date)} alle {formatTime(reminder.time)}
                </span>
              </div>
            </div>
            
            <div className="flex items-center gap-1 ml-4">
              <Button
                size="xs"
                color={reminder.completed ? 'light' : 'success'}
                onClick={() => handleToggleCompleted(reminder)}
                disabled={loading}
              >
                <FiCheck className="w-3 h-3" />
              </Button>
              
              {onEdit && (
                <Button
                  size="xs"
                  color="light"
                  onClick={() => onEdit(reminder)}
                  disabled={loading}
                >
                  <FiEdit className="w-3 h-3" />
                </Button>
              )}
              
              <Button
                size="xs"
                color="failure"
                onClick={() => handleDelete(reminder)}
                disabled={loading}
              >
                <FiTrash2 className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default ReminderList;
