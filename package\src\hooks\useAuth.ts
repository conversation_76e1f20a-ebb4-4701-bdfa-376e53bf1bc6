/**
 * Hook per la gestione dell'autenticazione Supabase
 * Sostituisce la simulazione localStorage con vera autenticazione
 */
import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { User } from '@supabase/supabase-js';

export interface AuthUser {
  id: string;
  email: string;
  clinic_id?: string;
  role?: 'manager' | 'doctor' | 'accountant' | 'assistant';
}

interface UseAuthReturn {
  user: AuthUser | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Development mode flag
  const isDevelopment = import.meta.env.DEV;

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          
          // In development mode, create a mock user
          if (isDevelopment) {
            console.warn('🚧 [useAuth] Development mode: Creating mock user');
            setUser({
              id: 'dev-user-id',
              email: '<EMAIL>',
              clinic_id: '********-0000-0000-0000-************',
              role: 'manager'
            });
          } else {
            setUser(null);
          }
        } else if (session?.user) {
          await updateUserState(session.user);
        } else {
          // In development mode, create a mock user even without session
          if (isDevelopment) {
            console.warn('🚧 [useAuth] Development mode: No session found, creating mock user');
            setUser({
              id: 'dev-user-id',
              email: '<EMAIL>',
              clinic_id: '********-0000-0000-0000-************',
              role: 'manager'
            });
          } else {
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        
        // In development mode, create a mock user on any error
        if (isDevelopment) {
          console.warn('🚧 [useAuth] Development mode: Error occurred, creating mock user');
          setUser({
            id: 'dev-user-id',
            email: '<EMAIL>',
            clinic_id: '********-0000-0000-0000-************',
            role: 'manager'
          });
        } else {
          setUser(null);
        }
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes (only in production or when we have real auth)
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, session?.user?.email);
        
        if (session?.user) {
          await updateUserState(session.user);
        } else if (!isDevelopment) {
          // Only clear user in production mode
          setUser(null);
        }
        
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const updateUserState = async (supabaseUser: User) => {
    try {
      console.log('🔄 [useAuth] Updating user state for:', supabaseUser.email);

      // Verifica se l'utente ha JWT claims necessari
      const hasJwtClaims = supabaseUser.app_metadata?.clinic_id && supabaseUser.app_metadata?.app_role;

      if (!hasJwtClaims) {
        console.log('⚠️ [useAuth] JWT claims mancanti, chiamata Edge Function...');

        try {
          const { data: functionResult, error: functionError } = await supabase.functions.invoke('issue-jwt', {
            body: { user: supabaseUser }
          });

          if (functionError) {
            console.error('❌ [useAuth] Errore Edge Function:', functionError.message);
          } else {
            console.log('✅ [useAuth] Edge Function eseguita:', functionResult);

            // Ricarica l'utente per ottenere i nuovi claims
            const { data: { user: updatedUser }, error: refreshError } = await supabase.auth.getUser();
            if (!refreshError && updatedUser) {
              supabaseUser = updatedUser;
              console.log('🔄 [useAuth] JWT claims aggiornati');
            }
          }
        } catch (edgeFunctionError) {
          console.error('❌ [useAuth] Errore chiamata Edge Function:', edgeFunctionError);
        }
      }

      // Prima prova a recuperare i dati dalla tabella public.users
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('clinic_id, role, first_name, last_name')
        .eq('id', supabaseUser.id)
        .maybeSingle(); // Usa maybeSingle() invece di single() per evitare errori se non trova record

      if (userError) {
        console.warn('⚠️ [useAuth] Error querying public.users:', userError.message);
        // Fallback ai metadata
      }

      if (userError || !userData) {
        // Fallback: usa metadata se disponibili o crea record mancante
        console.log('🔄 [useAuth] Using fallback - checking metadata and creating missing record if needed');

        const clinicId = supabaseUser.app_metadata?.clinic_id ||
                        supabaseUser.user_metadata?.clinic_id;
        const role = supabaseUser.app_metadata?.role ||
                     supabaseUser.user_metadata?.role ||
                     'manager'; // Default per nuovi utenti
        const firstName = supabaseUser.user_metadata?.first_name || 'Nome';
        const lastName = supabaseUser.user_metadata?.last_name || 'Cognome';

        // Prova a creare il record mancante se abbiamo i metadata
        if (supabaseUser.user_metadata?.clinic_name && !userData) {
          console.log('🔧 [useAuth] Attempting to create missing user record from metadata');

          try {
            // Prima trova o crea la clinica
            let clinicIdForUser = clinicId;

            if (supabaseUser.user_metadata?.clinic_vat_number) {
              const { data: existingClinic } = await supabase
                .from('clinics')
                .select('id')
                .eq('vat_number', supabaseUser.user_metadata.clinic_vat_number)
                .maybeSingle();

              if (existingClinic) {
                clinicIdForUser = existingClinic.id;
              }
            }

            // Crea il record utente mancante
            const { error: insertError } = await supabase
              .from('users')
              .insert({
                id: supabaseUser.id,
                email: supabaseUser.email || '',
                clinic_id: clinicIdForUser,
                role: role
              });

            if (!insertError) {
              console.log('✅ [useAuth] Missing user record created successfully');
            } else {
              console.warn('⚠️ [useAuth] Failed to create missing user record:', insertError.message);
            }
          } catch (createError) {
            console.warn('⚠️ [useAuth] Error creating missing user record:', createError);
          }
        }

        const authUser: AuthUser = {
          id: supabaseUser.id,
          email: supabaseUser.email || '',
          clinic_id: clinicId,
          role: role as 'manager' | 'doctor' | 'accountant' | 'assistant'
        };

        console.log('✅ [useAuth] User state updated (fallback):', authUser);
        setUser(authUser);
      } else {
        // Usa i dati dalla tabella public.users
        const authUser: AuthUser = {
          id: supabaseUser.id,
          email: supabaseUser.email || '',
          clinic_id: (userData as any)?.clinic_id || null,
          role: ((userData as any)?.role || 'assistant') as 'manager' | 'doctor' | 'accountant' | 'assistant'
        };

        console.log('✅ [useAuth] User state updated (from database):', authUser);
        setUser(authUser);
      }
    } catch (error) {
      console.error('❌ [useAuth] Error updating user state:', error);
      setUser(null);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Error signing out:', error);
        throw error;
      }
      setUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    loading,
    signOut
  };
};
